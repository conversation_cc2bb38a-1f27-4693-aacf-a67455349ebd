<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Webhook\Whatsapp;

use App\Enums\Http;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

final class Receive<PERSON>ontroller extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): JsonResponse
    {
        $entry = $request->input('entry')[0] ?? [];
        $changes = $entry['changes'][0]['value'] ?? [];

        Log::error('WhatsApp Webhook Event:', [
            'request' => $request->all(),
        ]);

        Log::error('WhatsApp Webhook Event: entry', [
            'entry' => $entry,
        ]);

        Log::error('WhatsApp Webhook Event: changes', [
            'changes' => $changes,
        ]);

        Log::error('WhatsApp Webhook Event: statuses', [
            'statuses' => $changes['statuses'],
            'isset' => isset($changes['statuses']),
        ]);

        if (isset($changes['messages'])) {
            foreach ($changes['messages'] as $message) {
                Log::error('Received WhatsApp message:', [
                    'message' => $message,
                ]);
            }
        }

        if (isset($changes['statuses'])) {
            foreach ($changes['statuses'] as $status) {
                Log::error('Received WhatsApp message status:', [
                    'status' => $status,
                ]);
            }
        }

        return response()->json(['status' => 'received'], Http::OK->value);
    }
}
