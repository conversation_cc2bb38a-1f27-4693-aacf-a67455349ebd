<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\Webhook\Whatsapp;

use App\Enums\Http;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

final class VerifyController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): JsonResponse
    {
        $mode = $request->get('hub_mode');
        $token = $request->get('hub_verify_token');
        $challenge = $request->get('hub_challenge');

        Log::error('WhatsApp Webhook Verify:', [
            'request' => $request->all(),
        ]);

        if ('subscribe' === $mode && $token === config('services.whatsapp.verify_token')) {
            Log::error('WhatsApp Webhook Verifed:', [
                'hub_challenge' => (int) $challenge,
            ]);

            return response()->json((int) $challenge, Http::OK->value);
        }

        return response()->json('Forbidden', Http::FORBIDDEN->value);
    }
}
