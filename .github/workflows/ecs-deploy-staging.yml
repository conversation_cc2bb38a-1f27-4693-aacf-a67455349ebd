name:  ECS_STAGING_DEPLOYMENT

on:
  push:
    branches:
      - staging

jobs:
  Deploy_to_AWS_ECS:
    runs-on: ubuntu-latest
    env:
      CONTAINER_NAME: ${{secrets.STAGING_SERVER_CONTAINER_NAME}}
      WORKER_CONTAINER_NAME: ${{secrets.STAGING_WORKER_CONTAINER_NAME}}
      ECS_SERVER_SERVICE: ${{secrets.STAGING_ECS_SERVER_SERVICE}}
      ECS_WORKER_SERVICE: ${{secrets.STAGING_ECS_WORKER_SERVICE}}
      ECS_CLUSTER: ${{secrets.STAGING_ECS_CLUSTER}}
      ECR_URL    : ${{secrets.STAGING_ECR_URL}}
      ECR_REPO   : ${{secrets.STAGING_ECR_REPO}}
    timeout-minutes: 20
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v2

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume:   ${{ secrets.STAGING_AWS_ROLE}}
          aws-region: ${{ secrets.STAGING_AWS_DEFAULT_REGION }}

      - name: copy env	 file
        run: |
          aws s3 cp s3://${{secrets.STAGING_ENV_BUCKET}}/${{secrets.STAGING_ENV_FILE}}	  ./.env

      # - name: copy firebase file
      #   run: |
      #     aws s3 cp s3://${{secrets.STAGING_FIREBASE_BUCKET}}/eventapp-firebase.json   ./eventapp-firebase.json

      - name: Get ecr login token
        run: aws ecr get-login-password --region ${{ secrets.STAGING_AWS_DEFAULT_REGION }}  | docker login --username AWS --password-stdin ${{env.ECR_URL}}

      - uses: benjlevesque/short-sha@v2.1
        id: short-sha
      
      - name: Download Worker task definition
        run: aws ecs describe-task-definition --task-definition worker-eventapp-staging --query taskDefinition > worker-task-definition.json

      - name: Fill in the new image ID in the Amazon ECS worker task definition
        id: update-worker-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: worker-task-definition.json
          container-name: worker
          image: "${{env.ECR_URL}}/${{env.ECR_REPO}}:${{ steps.short-sha.outputs.sha }}"
      
      - name: Download Server task definition
        run: aws ecs describe-task-definition --task-definition laravel-eventapp-staging --query taskDefinition > server-task-definition.json

      - name: Fill in the new image ID in the Amazon ECS server task definition
        id: update-server-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: server-task-definition.json
          container-name: laravel
          image: "${{env.ECR_URL}}/${{env.ECR_REPO}}:${{ steps.short-sha.outputs.sha }}"

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build Server image
        run: |
             docker buildx build -f Dockerfile.staging --cache-to type=gha --cache-from type=gha --platform linux/arm64 --load  -t ${{env.ECR_REPO}} .

      - name: Tag image
        run: docker tag ${{env.ECR_REPO}}:latest ${{env.ECR_URL}}/${{env.ECR_REPO}}:${{ steps.short-sha.outputs.sha }}

      - name: Push Server image
        run: docker push ${{env.ECR_URL}}/${{env.ECR_REPO}}:${{ steps.short-sha.outputs.sha }}

      - name: Deploy Amazon ECS Worker task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
            task-definition: ${{ steps.update-worker-task-def.outputs.task-definition }}
            service: ${{ env.ECS_WORKER_SERVICE }}
            cluster: ${{ env.ECS_CLUSTER }}
            wait-for-service-stability: true

      - name: Deploy Amazon ECS Server task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
            task-definition: ${{ steps.update-server-task-def.outputs.task-definition }}
            service: ${{ env.ECS_SERVER_SERVICE }}
            cluster: ${{ env.ECS_CLUSTER }}
            wait-for-service-stability: true

#       - name: Run JMeter Tests
#         uses: QAInsights/PerfAction@v3.1
#         with:
#           test-plan-path: jmeter-files/staging-plan.jmx
#           args: ""
#       - name: Upload Results
#         uses: actions/upload-artifact@v2
#         with:
#           name: jmeter-results
#           path: result.jtl

#       - name: Analyze Results with Latency Lingo
#         uses: latency-lingo/github-action@v0.0.2
#         with:
#           api-key: ${{ secrets.STAGING_LATENCY_LINGO_API_KEY }}
#           file: result.jtl
#           label: lms-backend staging Flow Automated Test Plan
#           format: jmeter
