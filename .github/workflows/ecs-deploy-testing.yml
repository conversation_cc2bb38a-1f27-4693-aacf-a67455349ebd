name:  ECS_TESTING_DEPLOYMENT

on:
  push:
    branches:
      - testing



jobs:
  Deploy_to_AWS_ECS:
    runs-on: ubuntu-latest
    env:
      CONTAINER_NAME: ${{secrets.TESTING_SERVER_CONTAINER_NAME}}
      WORKER_CONTAINER_NAME: ${{secrets.TESTING_WORKER_CONTAINER_NAME}}
      ECS_SERVER_SERVICE: ${{secrets.TESTING_ECS_SERVER_SERVICE}}
      ECS_WORKER_SERVICE: ${{secrets.TESTING_ECS_WORKER_SERVICE}}
      ECS_CLUSTER: ${{secrets.TESTING_ECS_CLUSTER}}
      ECR_URL    : ${{secrets.TESTING_ECR_URL}}
      ECR_REPO   : ${{secrets.TESTING_ECR_REPO}}
    timeout-minutes: 20
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v2

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume:   ${{ secrets.TESTING_AWS_ROLE}}
          aws-region: ${{ secrets.TESTING_AWS_DEFAULT_REGION }}

      - name: copy env	 file
        run: |
          aws s3 cp s3://${{secrets.TESTING_ENV_BUCKET}}/${{secrets.TESTING_ENV_FILE}}	  ./.env

      - name: copy firebase file
        run: |
          aws s3 cp s3://${{secrets.TESTING_FIREBASE_BUCKET}}/eventapp-firebase.json   ./eventapp-firebase.json

      - name: Get ecr login token
        run: aws ecr get-login-password --region ${{ secrets.TESTING_AWS_DEFAULT_REGION }}  | docker login --username AWS --password-stdin ${{env.ECR_URL}}

      - uses: benjlevesque/short-sha@v2.1
        id: short-sha

      - name: Download Worker task definition
        run: aws ecs describe-task-definition --task-definition worker-eventapp-testing --query taskDefinition > worker-task-definition.json

      - name: Fill in the new image ID in the Amazon ECS worker task definition
        id: update-worker-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: worker-task-definition.json
          container-name: worker
          image: "${{env.ECR_URL}}/${{env.ECR_REPO}}:${{ steps.short-sha.outputs.sha }}"

      - name: Download Server task definition
        run: aws ecs describe-task-definition --task-definition laravel-eventapp-testing --query taskDefinition > server-task-definition.json

      - name: Fill in the new image ID in the Amazon ECS server task definition
        id: update-server-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: server-task-definition.json
          container-name: laravel
          image: "${{env.ECR_URL}}/${{env.ECR_REPO}}:${{ steps.short-sha.outputs.sha }}"

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Build Server image
        run: |
             docker buildx build -f Dockerfile.testing --cache-to type=gha --cache-from type=gha --platform linux/arm64 --load -t ${{env.ECR_REPO}} .

      - name: Tag image
        run: docker tag ${{env.ECR_REPO}}:latest ${{env.ECR_URL}}/${{env.ECR_REPO}}:${{ steps.short-sha.outputs.sha }}

      - name: Push Server image
        run: docker push ${{env.ECR_URL}}/${{env.ECR_REPO}}:${{ steps.short-sha.outputs.sha }}

      - name: Deploy Amazon ECS Worker task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
            task-definition: ${{ steps.update-worker-task-def.outputs.task-definition }}
            service: ${{ env.ECS_WORKER_SERVICE }}
            cluster: ${{ env.ECS_CLUSTER }}

      - name: Deploy Amazon ECS Server task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
            task-definition: ${{ steps.update-server-task-def.outputs.task-definition }}
            service: ${{ env.ECS_SERVER_SERVICE }}
            cluster: ${{ env.ECS_CLUSTER }}
