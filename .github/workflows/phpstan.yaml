name: Static Analysis

on:
  pull_request:
    branches: [develop, testing, main]
    paths:
      - '**.php'
jobs:
  phpstan:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: true
      matrix:
        os: [ubuntu-latest]
        php: [8.3]
        laravel: [11.*]
        stability: [prefer-stable]
        include:
          - laravel: 11.*

    name: PHPStan - P${{ matrix.php }} - L${{ matrix.laravel }} - ${{ matrix.stability }} - ${{ matrix.os }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv, imagick, fileinfo
          coverage: none

      - name: Install dependencies
        run: |
          composer install
      - name: List Installed Dependencies
        run: composer show -D

      - name: Setup <PERSON>
        run: |
          cp .env.ci .env
          php artisan key:generate

      - name: Run PHPStan
        run: vendor/bin/phpstan analyse -c phpstan-ci.neon --error-format=github
        timeout-minutes: 1
