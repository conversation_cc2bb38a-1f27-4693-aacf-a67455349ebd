name:  ECR_BASE_TESTING


on: workflow_dispatch



jobs:
  Deploy_BASE_IMAGE_to_AWS_ECR:
    runs-on: [self-hosted, ARM64]
    env:
      ECR_URL    : ${{secrets.TESTING_ECR_URL}}
      ECR_REPO   : ${{secrets.TESTING_ECR_BASE_REPO}}
      GH_USERNAME: ${{ github.actor }}
      
    permissions:
      id-token: write
      contents: write


  
    steps:
      - uses: actions/checkout@v3
        with:
          ref: 'develop'

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume:   ${{ secrets.TESTING_AWS_ROLE }}
          aws-region: ${{ secrets.TESTING_AWS_DEFAULT_REGION }} 
   
      - name: copy env	 file
        run: |
          aws s3 cp s3://${{secrets.TESTING_ENV_BUCKET}}/${{secrets.TESTING_ENV_FILE}}	  ./.env



      - name: Get ecr login token 
        run: aws ecr get-login-password --region ${{ secrets.TESTING_AWS_DEFAULT_REGION }}  | sudo docker login --username AWS --password-stdin ${{env.ECR_URL}}

   
      - name: Build Server image 
        run: |
             sudo docker build -f Dockerfile.base -t ${{env.ECR_REPO}}  .


      # - name: Test Server image
      #   run: docker run  ${{env.ECR_REPO}}  /bin/sh -c "touch database/database.sqlite ; cp .env.testing.example .env ; php artisan test"
      - id: version-number-generator
        uses: ibrasho-actions/version-number-generator@main
        with:
          major-version: 1

      - name: Tag image 
        run: sudo docker tag ${{env.ECR_REPO}}:latest ${{env.ECR_URL}}/${{env.ECR_REPO}}:${{ steps.version-number-generator.outputs.version }}
      
      - name: Push Server image
        run: sudo docker push ${{env.ECR_URL}}/${{env.ECR_REPO}}:${{ steps.version-number-generator.outputs.version }}  
      
      - name: add new tag
        run: |
             sed -i -e 's/.*FROM.*/FROM  ${{env.ECR_URL}}\/${{env.ECR_REPO}}:${{ steps.version-number-generator.outputs.version }}/g' Dockerfile.testing 

      - name: push new tag
        run: |
             git config user.name github-actions
             git config user.email <EMAIL>
             git add .
             git commit -m "modified base image tag from github Actions in testing to ${{ steps.version-number-generator.outputs.version }}"
             git push origin develop

 
    