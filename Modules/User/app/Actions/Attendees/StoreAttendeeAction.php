<?php

declare(strict_types=1);

namespace Modules\User\Actions\Attendees;

use App\Enums\Http;
use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Modules\Support\Entities\Country;
use Modules\User\Actions\GenerateAttendanceCodeAction;
use Modules\User\DataTransferObjects\Attendees\StoreAttendeeData;
use Modules\User\Entities\Event;
use Modules\User\Entities\User;
use Modules\User\Enums\UserType;
use Modules\User\Events\AttendeeInvitationEmailEvent;
use Random\RandomException;

final class StoreAttendeeAction
{
    /**
     * @throws LogicalException
     * @throws RandomException
     */
    public function __invoke(StoreAttendeeData $data): Model
    {
        $event = Event::query()->findOrFail($data->event_id);

        if ($data->attachment) {
            $attachment_path = $data->attachment->storePublicly('attendees/attachments');
        }

        $country = null;

        if ($data->country_code) {
            $country = Country::query()->firstWhere('code', $data->country_code);
        }

        $user = User::query()->firstOrCreate([
            'email' => $data->email,
        ], [
            'name' => $data->name,
            'mobile' => $data->mobile,
            'country_id' => $country?->id,
        ]);

        if ($user->events()->where('event_id', $data->event_id)->exists()) {
            throw new LogicalException(
                __('event::exceptions.user.attendee.already_exists'),
                Http::UNPROCESSABLE_ENTITY->value
            );
        }

        // Generate a unique 4-digit attendance code
        $attendanceCode = (new GenerateAttendanceCodeAction)($data->event_id);

        $tag = $event->tags()->findOrFail($data->tag_id);

        $user->events()->syncWithoutDetaching([
            $data->event_id => [
                'type' => UserType::ATTENDEE,
                'attachment' => $attachment_path ?? null,
                'extra' => $data->extra,
                'attendance_code' => $attendanceCode,
                'tag_id' => $tag->id,
                'registration_source' => $data->registration_source,
            ],
        ]);

        AttendeeInvitationEmailEvent::dispatch(
            $user->id,
            $data->event_id,
        );

        return $user;
    }
}
