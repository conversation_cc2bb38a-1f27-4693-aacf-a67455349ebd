<?php

declare(strict_types=1);

use App\Http\Middleware\IsoCodeConverter;
use Illuminate\Support\Facades\Route;
use Modules\Organizer\Http\Controllers\Accounts\ChangePasswordController;
use Modules\Organizer\Http\Controllers\Accounts\LoginController;
use Modules\Organizer\Http\Controllers\Accounts\LogoutController;
use Modules\Organizer\Http\Controllers\Accounts\Profile\ShowProfileController;
use Modules\Organizer\Http\Controllers\Accounts\Profile\UpdateProfileController;
use Modules\Organizer\Http\Controllers\OperationAreas\DeleteOperationAreaController;
use Modules\Organizer\Http\Controllers\OperationAreas\IndexOperationAreaController;
use Modules\Organizer\Http\Controllers\OperationAreas\ListOperationAreaController;
use Modules\Organizer\Http\Controllers\OperationAreas\StoreOperationAreaController;
use Modules\Organizer\Http\Controllers\OperationAreas\UpdateOperationAreaController;
use Modules\Organizer\Http\Controllers\Orders\IndexOrderController;
use Modules\Organizer\Http\Controllers\Orders\ShowOrderController;
use Modules\Organizer\Http\Controllers\Organizations\About\DisableAboutController;
use Modules\Organizer\Http\Controllers\Organizations\About\EnableAboutController;
use Modules\Organizer\Http\Controllers\Organizations\About\ShowAboutController;
use Modules\Organizer\Http\Controllers\Organizations\About\UpdateAboutController;
use Modules\Organizer\Http\Controllers\Organizations\Achievements\DisableAchievementsController;
use Modules\Organizer\Http\Controllers\Organizations\Achievements\EnableAchievementsController;
use Modules\Organizer\Http\Controllers\Organizations\Achievements\ShowAchievementsController;
use Modules\Organizer\Http\Controllers\Organizations\Achievements\UpdateAchievementsController;
use Modules\Organizer\Http\Controllers\Organizations\ContactInfo\DisableContactInfoController;
use Modules\Organizer\Http\Controllers\Organizations\ContactInfo\EnableContactInfoController;
use Modules\Organizer\Http\Controllers\Organizations\ContactInfo\ShowContactInfoController;
use Modules\Organizer\Http\Controllers\Organizations\ContactInfo\UpdateContactInfoController;
use Modules\Organizer\Http\Controllers\Organizations\DisableCostEstimatorController;
use Modules\Organizer\Http\Controllers\Organizations\EnableCostEstimatorController;
use Modules\Organizer\Http\Controllers\Organizations\ShowOrganizationDetailsController;
use Modules\Organizer\Http\Controllers\Organizations\UpdateOrganizationDetailsController;
use Modules\Organizer\Http\Controllers\Organizers\IndexOrganizerController;
use Modules\Organizer\Http\Controllers\Organizers\ShowOrganizerController;
use Modules\Organizer\Http\Controllers\Organizers\StoreOrganizerController;
use Modules\Organizer\Http\Controllers\Organizers\UpdateOrganizerController;
use Modules\Organizer\Http\Controllers\Providers\DeleteProviderController;
use Modules\Organizer\Http\Controllers\Providers\IndexProviderController;
use Modules\Organizer\Http\Controllers\Providers\StoreProviderController;
use Modules\Organizer\Http\Controllers\Providers\UpdateProviderController;
use Modules\Organizer\Http\Controllers\Services\DeleteServiceController;
use Modules\Organizer\Http\Controllers\Services\IndexServiceController;
use Modules\Organizer\Http\Controllers\Services\StoreServiceController;
use Modules\Organizer\Http\Controllers\Services\UpdateServiceController;
use Modules\Organizer\Http\Controllers\ServiceTypes\DeleteServiceTypeController;
use Modules\Organizer\Http\Controllers\ServiceTypes\IndexServiceTypeController;
use Modules\Organizer\Http\Controllers\ServiceTypes\StoreServiceTypeController;
use Modules\Organizer\Http\Controllers\ServiceTypes\UpdateServiceTypeController;
use Modules\Organizer\Http\Controllers\Sponsors\DeleteSponsorController;
use Modules\Organizer\Http\Controllers\Sponsors\IndexSponsorController;
use Modules\Organizer\Http\Controllers\Sponsors\StoreSponsorController;
use Modules\Organizer\Http\Controllers\Sponsors\UpdateSponsorController;
use Modules\Organizer\Http\Controllers\Venues\DeleteVenueController;
use Modules\Organizer\Http\Controllers\Venues\IndexVenueController;
use Modules\Organizer\Http\Controllers\Venues\StoreVenueController;
use Modules\Organizer\Http\Controllers\Venues\UpdateVenueController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::name('platform.')->prefix('platform')->whereNumber(['id', 'organizer_id', 'service_id'])
    ->group(function (): void {
        Route::name('accounts.')->prefix('accounts')->group(function (): void {
            Route::post(
                'login',
                LoginController::class
            )->name('login');

            Route::middleware(['auth:organizer-api'])->group(function (): void {
                Route::post(
                    'logout',
                    LogoutController::class
                )->name('logout');
                Route::post(
                    'change-password',
                    ChangePasswordController::class
                )->name('change-password');

                Route::name('profile.')->prefix('profile')->group(function (): void {
                    Route::get(
                        'me',
                        ShowProfileController::class
                    )->name('me');
                    Route::patch(
                        'update',
                        UpdateProfileController::class
                    )->name('update')->middleware([
                        IsoCodeConverter::class
                    ]);
                });
            });
        });

        Route::middleware(
            'auth:organizer-api'
        )->name('organizers.')->prefix('organizers')->group(function (): void {
            Route::get(
                '/',
                IndexOrganizerController::class
            )->name('index');
            Route::get(
                '/{organizer_id}',
                ShowOrganizerController::class
            )->name('show');
            Route::post(
                '/',
                StoreOrganizerController::class
            )->name('store')->middleware([
                IsoCodeConverter::class
            ]);
            Route::patch(
                '/{organizer_id}',
                UpdateOrganizerController::class
            )->name('update')->middleware([
                IsoCodeConverter::class
            ]);
        });

        Route::middleware(
            'auth:organizer-api'
        )->name('organizations.')->prefix('organizations')->group(function (): void {
            Route::name('details.')->prefix('details')->group(function (): void {
                Route::get(
                    '/',
                    ShowOrganizationDetailsController::class
                )->name('show');
                Route::patch(
                    '/',
                    UpdateOrganizationDetailsController::class
                )->name('update');
            });

            Route::name('settings.')->prefix('settings')->group(function (): void {
                Route::name('price-calculator.')->prefix('price-calculator')->group(function (): void {

                    Route::patch(
                        '/enable',
                        EnableCostEstimatorController::class
                    )->name('enable');

                    Route::patch(
                        '/disable',
                        DisableCostEstimatorController::class
                    )->name('disable');

                    Route::name('operation-areas.')->prefix('operation-areas')->group(function (): void {
                        Route::get(
                            '/',
                            IndexOperationAreaController::class
                        )->name('index');

                        Route::get(
                            '/list',
                            ListOperationAreaController::class
                        )->name('list');

                        Route::post(
                            '/',
                            StoreOperationAreaController::class
                        )->name('store');

                        Route::patch(
                            '/{id}',
                            UpdateOperationAreaController::class
                        )->name('update');

                        Route::delete(
                            '/{id}',
                            DeleteOperationAreaController::class
                        )->name('delete');
                    });

                    Route::name('service-types.')->prefix('service-types')->group(function (): void {
                        Route::get(
                            '/',
                            IndexServiceTypeController::class
                        )->name('index');

                        Route::post(
                            '/',
                            StoreServiceTypeController::class
                        )->name('store');

                        Route::patch(
                            '/{id}',
                            UpdateServiceTypeController::class
                        )->name('update');

                        Route::delete(
                            '/{id}',
                            DeleteServiceTypeController::class
                        )->name('delete');
                    });

                    Route::name('sponsors.')->prefix('sponsors')->group(function (): void {
                        Route::get(
                            '/',
                            IndexSponsorController::class
                        )->name('index');

                        Route::post(
                            '/',
                            StoreSponsorController::class
                        )->name('store');

                        Route::patch(
                            '/{id}',
                            UpdateSponsorController::class
                        )->name('update');

                        Route::delete(
                            '/{id}',
                            DeleteSponsorController::class
                        )->name('delete');
                    });

                    Route::name('providers.')->prefix('providers')->group(function (): void {
                        Route::get(
                            '/',
                            IndexProviderController::class
                        )->name('index');

                        Route::post(
                            '/',
                            StoreProviderController::class
                        )->name('store');

                        Route::patch(
                            '/{id}',
                            UpdateProviderController::class
                        )->name('update');

                        Route::delete(
                            '/{id}',
                            DeleteProviderController::class
                        )->name('delete');
                    });

                    Route::name('services.')->prefix('services')->group(function (): void {
                        Route::get(
                            '/',
                            IndexServiceController::class
                        )->name('index');

                        Route::post(
                            '/',
                            StoreServiceController::class
                        )->name('store');

                        Route::patch(
                            '/{id}',
                            UpdateServiceController::class
                        )->name('update');

                        Route::delete(
                            '/{id}',
                            DeleteServiceController::class
                        )->name('delete');
                    });

                    Route::name('venues.')->prefix('venues')->group(function (): void {
                        Route::get(
                            '/',
                            IndexVenueController::class
                        )->name('index');

                        Route::post(
                            '/',
                            StoreVenueController::class
                        )->name('store');

                        Route::patch(
                            '/{id}',
                            UpdateVenueController::class
                        )->name('update');

                        Route::delete(
                            '/{id}',
                            DeleteVenueController::class
                        )->name('delete');

                        Route::name('services.')->prefix('{id}/services')->group(function (): void {
                            Route::get(
                                '/',
                                Modules\Organizer\Http\Controllers\VenueServices\IndexVenueServiceController::class
                            )->name('index');

                            Route::post(
                                '/',
                                Modules\Organizer\Http\Controllers\VenueServices\StoreVenueServiceController::class
                            )->name('store');

                            Route::patch(
                                '/{service_id}',
                                Modules\Organizer\Http\Controllers\VenueServices\UpdateVenueServiceController::class
                            )->name('update');

                            Route::delete(
                                '/{service_id}',
                                Modules\Organizer\Http\Controllers\VenueServices\DeleteVenueServiceController::class
                            )->name('delete');
                        });
                    });

                    Route::name('orders.')->prefix('orders')->group(function (): void {
                        Route::get(
                            '/',
                            IndexOrderController::class
                        )->name('index');

                        Route::get(
                            '/{id}',
                            ShowOrderController::class
                        )->name('show');
                    });
                });

                Route::name('landing-page.')->prefix('landing-page')->group(function (): void {
                    Route::name('contact-info.')->prefix('contact-info')->group(function (): void {
                        Route::get(
                            '/',
                            ShowContactInfoController::class
                        )->name('show');

                        Route::patch(
                            '/',
                            UpdateContactInfoController::class
                        )->name('update');

                        Route::patch(
                            '/enable',
                            EnableContactInfoController::class
                        )->name('enable');

                        Route::patch(
                            '/disable',
                            DisableContactInfoController::class
                        )->name('disable');
                    });

                    Route::name('achievements.')->prefix('achievements')->group(function (): void {
                        Route::get(
                            '/',
                            ShowAchievementsController::class
                        )->name('show');

                        Route::patch(
                            '/',
                            UpdateAchievementsController::class
                        )->name('update');

                        Route::patch(
                            '/enable',
                            EnableAchievementsController::class
                        )->name('enable');

                        Route::patch(
                            '/disable',
                            DisableAchievementsController::class
                        )->name('disable');
                    });

                    Route::name('about.')->prefix('about')->group(function (): void {
                        Route::get(
                            '/',
                            ShowAboutController::class
                        )->name('show');

                        Route::patch(
                            '/',
                            UpdateAboutController::class
                        )->name('update');

                        Route::patch(
                            '/enable',
                            EnableAboutController::class
                        )->name('enable');

                        Route::patch(
                            '/disable',
                            DisableAboutController::class
                        )->name('disable');
                    });
                });
            });
        });
    });
