<?php

declare(strict_types=1);

namespace Modules\Organizer\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class OrderVenueServiceAdditionalService extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'order_venue_service_id',
        'additional_service_id',
    ];

    /**
     * Get the venue service that owns the additional service.
     */
    public function venueService(): BelongsTo
    {
        return $this->belongsTo(OrderVenueService::class, 'order_venue_service_id');
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'order_venue_service_id' => SnowflakeCast::class,
            'additional_service_id' => SnowflakeCast::class,
        ];
    }
}
