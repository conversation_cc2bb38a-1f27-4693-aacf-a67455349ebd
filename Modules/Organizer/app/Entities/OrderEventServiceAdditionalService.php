<?php

declare(strict_types=1);

namespace Modules\Organizer\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class OrderEventServiceAdditionalService extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'order_event_service_id',
        'additional_service_id',
    ];

    /**
     * Get the event service that owns the additional service.
     */
    public function eventService(): BelongsTo
    {
        return $this->belongsTo(OrderEventService::class, 'order_event_service_id');
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'order_event_service_id' => SnowflakeCast::class,
            'additional_service_id' => SnowflakeCast::class,
        ];
    }
}
