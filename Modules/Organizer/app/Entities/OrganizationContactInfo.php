<?php

declare(strict_types=1);

namespace Modules\Organizer\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class OrganizationContactInfo extends Model
{
    use Snowflakes;

    protected $table = 'organization_contact_info';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'organization_id',
        'content_ar',
        'content_en',
        'phone_number',
        'email',
        'is_active',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'organization_id' => SnowflakeCast::class,
            'is_active' => 'boolean',
        ];
    }
}
