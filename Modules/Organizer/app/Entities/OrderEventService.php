<?php

declare(strict_types=1);

namespace Modules\Organizer\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class OrderEventService extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'order_id',
        'service_type_id',
        'service_id',
        'provider_id',
    ];

    /**
     * Get the order that owns the event service.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the service details.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the provider details.
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the service type details.
     */
    public function serviceType(): BelongsTo
    {
        return $this->belongsTo(ServiceType::class);
    }

    /**
     * Get the additional services for this event service.
     */
    public function additionalServices(): HasMany
    {
        return $this->hasMany(OrderEventServiceAdditionalService::class);
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'order_id' => SnowflakeCast::class,
            'service_type_id' => SnowflakeCast::class,
            'service_id' => SnowflakeCast::class,
            'provider_id' => SnowflakeCast::class,
        ];
    }
}
