<?php

declare(strict_types=1);

namespace Modules\Organizer\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\Organizer\Presenters\OrganizationPresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Organization extends Model
{
    use OrganizationPresenter;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone_number',
        'logo',
        'description',
        'primary_color',
        'secondary_color',
        'status',
        'allow_cost_estimator',
        'owner_id',
    ];

    public function organizers(): HasMany
    {
        return $this->hasMany(
            Organizer::class
        );
    }

    public function events(): HasMany
    {
        return $this->hasMany(
            Event::class
        );
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(
            Organizer::class,
            'owner_id'
        );
    }

    public function operationAreas(): HasMany
    {
        return $this->hasMany(
            OperationArea::class
        );
    }

    public function serviceTypes(): HasMany
    {
        return $this->hasMany(
            ServiceType::class
        );
    }

    public function sponsors(): HasMany
    {
        return $this->hasMany(
            Sponsor::class
        );
    }

    public function providers(): HasMany
    {
        return $this->hasMany(
            Provider::class
        );
    }

    public function services(): HasMany
    {
        return $this->hasMany(
            Service::class
        );
    }

    public function venues(): HasMany
    {
        return $this->hasMany(
            Venue::class
        );
    }

    public function orders(): HasMany
    {
        return $this->hasMany(
            Order::class
        );
    }

    public function contactInfo(): HasOne
    {
        return $this->hasOne(OrganizationContactInfo::class);
    }

    public function achievements(): HasOne
    {
        return $this->hasOne(OrganizationAchievement::class);
    }

    public function about(): HasOne
    {
        return $this->hasOne(OrganizationAbout::class);
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'owner_id' => SnowflakeCast::class,
            'status' => 'boolean',
            'allow_cost_estimator' => 'boolean',
        ];
    }
}
