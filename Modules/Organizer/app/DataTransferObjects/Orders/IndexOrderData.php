<?php

declare(strict_types=1);

namespace Modules\Organizer\DataTransferObjects\Orders;

use Spatie\LaravelData\Data;

final class IndexOrderData extends Data
{
    public function __construct(
        public readonly ?string $search = null,
        public readonly int $per_page = 10,
        public readonly string $sort_by = 'created_at',
        public readonly string $sort_direction = 'desc',
    ) {}
}
