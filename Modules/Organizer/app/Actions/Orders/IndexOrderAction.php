<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Orders;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\Organizer\DataTransferObjects\Orders\IndexOrderData;
use Modules\Organizer\Entities\Organizer;

final class IndexOrderAction
{
    public function __invoke(IndexOrderData $data): LengthAwarePaginator
    {
        /** @var Organizer $organizer */
        $organizer = auth('organizer-api')->user();
        $organization = $organizer->organization;

        return $organization->orders()
            ->when($data->search, function ($query, $search) {
                return $query->where(function ($query) use ($search): void {
                    $query->where('name', 'ilike', "%{$search}%")
                        ->orWhere('email', 'ilike', "%{$search}%")
                        ->orWhere('event_name', 'ilike', "%{$search}%")
                        ->orWhere('phone', 'ilike', "%{$search}%");
                });
            })
            ->orderBy($data->sort_by, $data->sort_direction)
            ->paginate($data->per_page);
    }
}
