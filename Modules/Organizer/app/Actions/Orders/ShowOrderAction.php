<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Orders;

use Modules\Organizer\Entities\Order;
use Modules\Organizer\Entities\Organizer;

final class ShowOrderAction
{
    public function __invoke(int $id): Order
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();
        $organization = $organizer->organization;

        return $organization->orders()
            ->with([
                'venue',
                'category',
                'operationArea',
                'venueServices.venueService',
                'venueServices.additionalServices',
                'eventServices.service',
                'eventServices.provider',
                'eventServices.serviceType',
                'eventServices.additionalServices',
            ])
            ->findOrFail($id);
    }
}
