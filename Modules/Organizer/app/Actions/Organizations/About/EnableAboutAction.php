<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\About;

use Modules\Organizer\Entities\Organizer;

final class EnableAboutAction
{
    public function __invoke(): void
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $organization->about()->update([
            'is_active' => true,
        ]);
    }
}
