<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\About;

use Modules\Organizer\Entities\OrganizationAbout;
use Modules\Organizer\Entities\Organizer;

final class ShowAboutAction
{
    public function __invoke(): OrganizationAbout
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        return $organization->about()->firstOrCreate([
            'organization_id' => $organization->id,
        ], [
            'content' => '',
            'is_active' => false,
        ]);
    }
}
