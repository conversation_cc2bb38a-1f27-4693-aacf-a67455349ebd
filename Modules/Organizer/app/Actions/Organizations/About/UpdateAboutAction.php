<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\About;

use Modules\Organizer\DataTransferObjects\Organizations\About\UpdateAboutData;
use Modules\Organizer\Entities\OrganizationAbout;
use Modules\Organizer\Entities\Organizer;

final class UpdateAboutAction
{
    public function __invoke(UpdateAboutData $data): OrganizationAbout
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $about = $organization->about()->firstOrCreate([
            'organization_id' => $organization->id,
        ], [
            'content_ar' => '',
            'content_en' => '',
            'is_active' => false,
        ]);

        // Check if the section is enabled before allowing updates
        if (!$about->is_active) {
            throw new \Exception(__('organizer::messages.section_not_enabled'));
        }

        $about->update([
            'content_ar' => $data->content_ar,
            'content_en' => $data->content_en,
        ]);

        return $about->fresh();
    }
}
