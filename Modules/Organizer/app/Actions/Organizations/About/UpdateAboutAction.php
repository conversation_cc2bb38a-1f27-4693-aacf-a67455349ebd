<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\About;

use Modules\Organizer\DataTransferObjects\Organizations\About\UpdateAboutData;
use Modules\Organizer\Entities\OrganizationAbout;
use Modules\Organizer\Entities\Organizer;

final class UpdateAboutAction
{
    public function __invoke(UpdateAboutData $data): OrganizationAbout
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $about = $organization->about()->firstOrCreate([
            'organization_id' => $organization->id,
        ], [
            'content' => '',
            'is_active' => false,
        ]);

        $about->update([
            'content' => $data->content,
        ]);

        return $about->fresh();
    }
}
