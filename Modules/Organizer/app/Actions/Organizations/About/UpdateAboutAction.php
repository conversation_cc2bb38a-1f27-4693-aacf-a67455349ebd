<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\About;

use App\Exceptions\LogicalException;
use Modules\Organizer\DataTransferObjects\Organizations\About\UpdateAboutData;
use Modules\Organizer\Entities\OrganizationAbout;
use Modules\Organizer\Entities\Organizer;

final class UpdateAboutAction
{
    public function __invoke(UpdateAboutData $data): OrganizationAbout
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $about = $organization->about;

        if (!$about->is_active) {
            throw new LogicalException(__('organizer::exceptions.section_not_enabled'));
        }

        $about->update($data->toArray());


        return $about->fresh();
    }
}
