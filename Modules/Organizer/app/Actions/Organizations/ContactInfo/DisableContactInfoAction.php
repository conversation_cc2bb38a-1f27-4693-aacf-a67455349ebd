<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\ContactInfo;

use Mo<PERSON>les\Organizer\Entities\Organizer;

final class DisableContactInfoAction
{
    public function __invoke(): void
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $contactInfo = $organization->contactInfo()->firstOrCreate([
            'organization_id' => $organization->id,
        ], [
            'content' => '',
            'is_active' => false,
        ]);

        $contactInfo->update([
            'is_active' => false,
        ]);
    }
}
