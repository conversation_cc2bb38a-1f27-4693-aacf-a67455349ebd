<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\ContactInfo;

use Mo<PERSON>les\Organizer\Entities\OrganizationContactInfo;
use Modules\Organizer\Entities\Organizer;

final class ShowContactInfoAction
{
    public function __invoke(): OrganizationContactInfo
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        return $organization->contactInfo()->firstOrCreate([
            'organization_id' => $organization->id,
        ], [
            'content' => '',
            'is_active' => false,
        ]);
    }
}
