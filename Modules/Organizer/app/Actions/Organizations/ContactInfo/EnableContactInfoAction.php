<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\ContactInfo;

use Modules\Organizer\Entities\Organizer;

final class EnableContactInfoAction
{
    public function __invoke(): void
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $organization->contactInfo()->update([
            'is_active' => false,
        ]);
    }
}
