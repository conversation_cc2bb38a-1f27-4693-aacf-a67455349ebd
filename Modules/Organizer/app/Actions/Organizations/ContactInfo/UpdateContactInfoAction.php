<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\ContactInfo;

use App\Exceptions\LogicalException;
use Modules\Organizer\DataTransferObjects\Organizations\ContactInfo\UpdateContactInfoData;
use Modules\Organizer\Entities\OrganizationContactInfo;
use Modules\Organizer\Entities\Organizer;

final class UpdateContactInfoAction
{
    public function __invoke(UpdateContactInfoData $data): OrganizationContactInfo
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $contactInfo = $organization->contactInfo;

        if (!$contactInfo->is_active) {
            throw new LogicalException(__('organizer::exceptions.section_not_enabled'));
        }

        $contactInfo->update($data->toArray());

        return $contactInfo->fresh();
    }
}
