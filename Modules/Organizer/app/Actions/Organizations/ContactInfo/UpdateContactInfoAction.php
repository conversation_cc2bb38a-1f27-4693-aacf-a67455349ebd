<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\ContactInfo;

use Mo<PERSON>les\Organizer\DataTransferObjects\Organizations\ContactInfo\UpdateContactInfoData;
use Modules\Organizer\Entities\OrganizationContactInfo;
use Modules\Organizer\Entities\Organizer;

final class UpdateContactInfoAction
{
    public function __invoke(UpdateContactInfoData $data): OrganizationContactInfo
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;
        $co
        $contactInfo = $organization->contactInfo()->firstOrCreate([
            'organization_id' => $organization->id,
        ], [
            'content' => '',
            'is_active' => false,
        ]);

        $contactInfo->update([
            'content' => $data->content,
        ]);

        return $contactInfo->fresh();
    }
}
