<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\ContactInfo;

use Mo<PERSON>les\Organizer\DataTransferObjects\Organizations\ContactInfo\UpdateContactInfoData;
use Modules\Organizer\Entities\OrganizationContactInfo;
use Modules\Organizer\Entities\Organizer;

final class UpdateContactInfoAction
{
    public function __invoke(UpdateContactInfoData $data): OrganizationContactInfo
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $contactInfo = $organization->contactInfo()->firstOrCreate([
            'organization_id' => $organization->id,
        ], [
            'content_ar' => '',
            'content_en' => '',
            'is_active' => false,
        ]);

        // Check if the section is enabled before allowing updates
        if (!$contactInfo->is_active) {
            throw new \Exception(__('organizer::messages.section_not_enabled'));
        }

        $contactInfo->update([
            'content_ar' => $data->content_ar,
            'content_en' => $data->content_en,
        ]);

        return $contactInfo->fresh();
    }
}
