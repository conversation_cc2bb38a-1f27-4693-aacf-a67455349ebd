<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\Achievements;

use Modules\Organizer\Entities\Organizer;

final class DisableAchievementsAction
{
    public function __invoke(): void
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $organization->achievements()->update([
            'is_active' => false,
        ]);
    }
}
