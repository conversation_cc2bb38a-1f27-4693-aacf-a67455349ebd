<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\Achievements;

use Modules\Organizer\Entities\Organizer;

final class EnableAchievementsAction
{
    public function __invoke(): void
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $achievements = $organization->achievements()->firstOrCreate([
            'organization_id' => $organization->id,
        ], [
            'content' => '',
            'is_active' => false,
        ]);

        $achievements->update([
            'is_active' => true,
        ]);
    }
}
