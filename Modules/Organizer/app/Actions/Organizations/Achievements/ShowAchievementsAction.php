<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\Achievements;

use Modules\Organizer\Entities\OrganizationAchievement;
use Modules\Organizer\Entities\Organizer;

final class ShowAchievementsAction
{
    public function __invoke(): OrganizationAchievement
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        return $organization->achievements()->firstOrCreate([
            'organization_id' => $organization->id,
        ], [
            'content' => '',
            'is_active' => false,
        ]);
    }
}
