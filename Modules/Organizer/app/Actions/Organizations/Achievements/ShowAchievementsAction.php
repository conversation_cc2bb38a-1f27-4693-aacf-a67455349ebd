<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\Achievements;

use App\Exceptions\LogicalException;
use Modules\Organizer\Entities\OrganizationAchievement;
use Modules\Organizer\Entities\Organizer;

final class ShowAchievementsAction
{
    public function __invoke(): OrganizationAchievement
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $achievements = $organization->achievements;

        if ( ! $achievements->is_active) {
            throw new LogicalException(__('organizer::exceptions.section_not_enabled'));
        }

        return $achievements;
    }
}
