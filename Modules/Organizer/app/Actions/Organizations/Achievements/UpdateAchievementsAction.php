<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\Achievements;

use Modules\Organizer\DataTransferObjects\Organizations\Achievements\UpdateAchievementsData;
use Modules\Organizer\Entities\OrganizationAchievement;
use Modules\Organizer\Entities\Organizer;

final class UpdateAchievementsAction
{
    public function __invoke(UpdateAchievementsData $data): OrganizationAchievement
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $achievements = $organization->achievements()->firstOrCreate([
            'organization_id' => $organization->id,
        ], [
            'content' => '',
            'is_active' => false,
        ]);

        $achievements->update([
            'content' => $data->content,
        ]);

        return $achievements->fresh();
    }
}
