<?php

declare(strict_types=1);

namespace Modules\Organizer\Actions\Organizations\Achievements;

use App\Exceptions\LogicalException;
use Modules\Organizer\DataTransferObjects\Organizations\Achievements\UpdateAchievementsData;
use Modules\Organizer\Entities\OrganizationAchievement;
use Modules\Organizer\Entities\Organizer;

final class UpdateAchievementsAction
{
    public function __invoke(UpdateAchievementsData $data): OrganizationAchievement
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $organization = $organizer->organization;

        $achievements = $organization->achievements;

        if (!$achievements->is_active) {
            throw new LogicalException(__('organizer::exceptions.section_not_enabled'));
        }

        $achievements->update($data->toArray());


        return $achievements->fresh();
    }
}
