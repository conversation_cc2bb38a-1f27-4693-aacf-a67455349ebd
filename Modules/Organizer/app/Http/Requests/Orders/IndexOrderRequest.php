<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Requests\Orders;

use App\Http\Requests\BaseFormRequest;

final class IndexOrderRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'search' => [
                'nullable',
                'string',
                'max:255'
            ],
            'per_page' => [
                'nullable',
                'integer',
                'min:1',
                'max:100'
            ],
            'sort_by' => [
                'nullable',
                'string',
                'in:name,email,event_name,start_at,end_at,total_price,created_at,updated_at'
            ],
            'sort_direction' => [
                'nullable',
                'string',
                'in:asc,desc'
            ],
        ];
    }
}
