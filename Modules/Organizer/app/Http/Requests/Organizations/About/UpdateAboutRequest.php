<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Requests\Organizations\About;

use App\Http\Requests\BaseFormRequest;

final class UpdateAboutRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'content' => [
                'required',
                'string',
            ],
        ];
    }
}
