<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Requests\Organizations\ContactInfo;

use App\Http\Requests\BaseFormRequest;

final class UpdateContactInfoRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'content_ar' => [
                'required',
                'string',
            ],
            'content_en' => [
                'required',
                'string',
            ],
            'phone_number' => [
                'nullable',
                'string',
                'max:20',
            ],
            'email' => [
                'nullable',
                'email',
                'max:255',
                'email'
            ],
        ];
    }
}
