<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Organizations\ContactInfo;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Organizer\Actions\Organizations\ContactInfo\DisableContactInfoAction;

final class DisableContactInfoController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): JsonResponse
    {
        app(DisableContactInfoAction::class)();

        return sendSuccessResponse(
            message: __('organizer::messages.disable_contact_info'),
        );
    }
}
