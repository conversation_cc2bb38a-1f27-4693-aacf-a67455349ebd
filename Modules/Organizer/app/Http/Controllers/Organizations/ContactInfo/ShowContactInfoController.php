<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Organizations\ContactInfo;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Organizer\Actions\Organizations\ContactInfo\ShowContactInfoAction;
use Modules\Organizer\Transformers\Organizations\ContactInfo\ContactInfoResource;

final class ShowContactInfoController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): JsonResponse
    {
        $contactInfo = app(ShowContactInfoAction::class)();

        return sendSuccessResponse(
            message: __('organizer::messages.get_contact_info'),
            data: ContactInfoResource::make($contactInfo)
        );
    }
}
