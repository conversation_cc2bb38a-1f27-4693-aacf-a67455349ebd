<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Organizations\ContactInfo;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Organizer\Actions\Organizations\ContactInfo\EnableContactInfoAction;

final class EnableContactInfoController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): JsonResponse
    {
        app(EnableContactInfoAction::class)();

        return sendSuccessResponse(
            message: __('organizer::messages.enable_contact_info'),
        );
    }
}
