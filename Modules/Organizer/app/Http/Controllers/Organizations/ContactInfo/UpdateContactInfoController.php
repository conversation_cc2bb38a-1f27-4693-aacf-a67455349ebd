<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Organizations\ContactInfo;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Organizer\Actions\Organizations\ContactInfo\UpdateContactInfoAction;
use Modules\Organizer\DataTransferObjects\Organizations\ContactInfo\UpdateContactInfoData;
use Modules\Organizer\Http\Requests\Organizations\ContactInfo\UpdateContactInfoRequest;
use Modules\Organizer\Transformers\Organizations\ContactInfo\ContactInfoResource;

final class UpdateContactInfoController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(UpdateContactInfoRequest $request): JsonResponse
    {
        $data = UpdateContactInfoData::from($request->validated());

        $contactInfo = app(UpdateContactInfoAction::class)($data);

        return sendSuccessResponse(
            message: __('organizer::messages.update_contact_info'),
            data: ContactInfoResource::make($contactInfo)
        );
    }
}
