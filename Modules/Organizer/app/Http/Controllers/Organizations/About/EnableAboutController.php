<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Organizations\About;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Organizer\Actions\Organizations\About\EnableAboutAction;

final class EnableAboutController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): JsonResponse
    {
        app(EnableAboutAction::class)();

        return sendSuccessResponse(
            message: __('organizer::messages.enable_about'),
        );
    }
}
