<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Organizations\About;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Organizer\Actions\Organizations\About\UpdateAboutAction;
use Modules\Organizer\DataTransferObjects\Organizations\About\UpdateAboutData;
use Modules\Organizer\Http\Requests\Organizations\About\UpdateAboutRequest;
use Modules\Organizer\Transformers\Organizations\About\AboutResource;

final class UpdateAboutController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(UpdateAboutRequest $request): JsonResponse
    {
        $data = UpdateAboutData::from($request->validated());

        $about = app(UpdateAboutAction::class)($data);

        return sendSuccessResponse(
            message: __('organizer::messages.update_about'),
            data: AboutResource::make($about)
        );
    }
}
