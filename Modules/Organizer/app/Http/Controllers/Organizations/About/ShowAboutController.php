<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Organizations\About;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Organizer\Actions\Organizations\About\ShowAboutAction;
use Modules\Organizer\Transformers\Organizations\About\AboutResource;

final class ShowAboutController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): JsonResponse
    {
        $about = app(ShowAboutAction::class)();

        return sendSuccessResponse(
            message: __('organizer::messages.get_about'),
            data: AboutResource::make($about)
        );
    }
}
