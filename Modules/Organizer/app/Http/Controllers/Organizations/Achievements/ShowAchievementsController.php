<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Organizations\Achievements;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Organizer\Actions\Organizations\Achievements\ShowAchievementsAction;
use Modules\Organizer\Transformers\Organizations\Achievements\AchievementsResource;

final class ShowAchievementsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): JsonResponse
    {
        $achievements = app(ShowAchievementsAction::class)();

        return sendSuccessResponse(
            message: __('organizer::messages.get_achievements'),
            data: AchievementsResource::make($achievements)
        );
    }
}
