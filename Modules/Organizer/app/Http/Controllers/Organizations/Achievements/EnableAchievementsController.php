<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Organizations\Achievements;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Organizer\Actions\Organizations\Achievements\EnableAchievementsAction;

final class EnableAchievementsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): JsonResponse
    {
        app(EnableAchievementsAction::class)();

        return sendSuccessResponse(
            message: __('organizer::messages.enable_achievements'),
        );
    }
}
