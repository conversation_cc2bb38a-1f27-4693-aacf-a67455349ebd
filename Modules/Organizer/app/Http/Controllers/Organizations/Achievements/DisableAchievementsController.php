<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Organizations\Achievements;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Organizer\Actions\Organizations\Achievements\DisableAchievementsAction;

final class DisableAchievementsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request): JsonResponse
    {
        app(DisableAchievementsAction::class)();

        return sendSuccessResponse(
            message: __('organizer::messages.disable_achievements'),
        );
    }
}
