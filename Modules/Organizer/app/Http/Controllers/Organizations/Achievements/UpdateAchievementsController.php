<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Organizations\Achievements;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Organizer\Actions\Organizations\Achievements\UpdateAchievementsAction;
use Modules\Organizer\DataTransferObjects\Organizations\Achievements\UpdateAchievementsData;
use Modules\Organizer\Http\Requests\Organizations\Achievements\UpdateAchievementsRequest;
use Modules\Organizer\Transformers\Organizations\Achievements\AchievementsResource;

final class UpdateAchievementsController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(UpdateAchievementsRequest $request): JsonResponse
    {
        $data = UpdateAchievementsData::from($request->validated());

        $achievements = app(UpdateAchievementsAction::class)($data);

        return sendSuccessResponse(
            message: __('organizer::messages.update_achievements'),
            data: AchievementsResource::make($achievements)
        );
    }
}
