<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Orders;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Organizer\Actions\Orders\IndexOrderAction;
use Modules\Organizer\DataTransferObjects\Orders\IndexOrderData;
use Modules\Organizer\Http\Requests\Orders\IndexOrderRequest;
use Modules\Organizer\Transformers\Orders\OrdersResource;

final class IndexOrderController extends Controller
{
    public function __invoke(IndexOrderRequest $request): JsonResponse
    {
        $data = IndexOrderData::from($request->validated());

        $orders = app(IndexOrderAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: OrdersResource::collection($orders)
        );
    }
}
