<?php

declare(strict_types=1);

namespace Modules\Organizer\Http\Controllers\Orders;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Organizer\Actions\Orders\ShowOrderAction;
use Modules\Organizer\Transformers\Orders\OrderResource;

final class ShowOrderController extends Controller
{
    public function __invoke(int $id): JsonResponse
    {
        $order = app(ShowOrderAction::class)($id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: OrderResource::make($order)
        );
    }
}
