<?php

declare(strict_types=1);

namespace Modules\Organizer\Transformers\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Organizer\Transformers\CategoryResource;
use Modules\Organizer\Transformers\OperationAreaResource;
use Modules\Organizer\Transformers\Venues\VenueResource;

final class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'customer_details' => [
                'name' => $this->name,
                'email' => $this->email,
                'country_code' => $this->country_code,
                'phone' => $this->phone,
            ],
            'event_details' => [
                'event_name' => $this->event_name,
                'event_type' => $this->event_type,
                'event_duration' => $this->event_duration,
                'start_at' => $this->start_at?->format('Y-m-d H:i:s'),
                'end_at' => $this->end_at?->format('Y-m-d H:i:s'),
                'number_of_attendees' => $this->number_of_attendees,
                'category' => CategoryResource::make($this->whenLoaded('category')),
                'operation_area' => OperationAreaResource::make($this->whenLoaded('operationArea')),
            ],
            'venue_location' => [
                'venue' => VenueResource::make($this->whenLoaded('venue')),
                'venue_services' => OrderVenueServiceResource::collection($this->whenLoaded('venueServices')),
            ],
            'general_services' => [
                'event_services' => OrderEventServiceResource::collection($this->whenLoaded('eventServices')),
            ],
            'total_price' => $this->total_price,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
