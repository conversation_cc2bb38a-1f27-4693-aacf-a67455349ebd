<?php

declare(strict_types=1);

namespace Modules\Organizer\Transformers\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class OrdersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'country_code' => $this->country_code,
            'phone' => $this->phone,
            'event_name' => $this->event_name,
            'event_duration' => $this->event_duration,
            'start_at' => $this->start_at?->format('Y-m-d H:i:s'),
            'end_at' => $this->end_at?->format('Y-m-d H:i:s'),
            'event_type' => $this->event_type,
            'number_of_attendees' => $this->number_of_attendees,
            'total_price' => $this->total_price,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
