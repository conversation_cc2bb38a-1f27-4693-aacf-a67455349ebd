<?php

declare(strict_types=1);

namespace Modules\Organizer\Transformers\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Organizer\Transformers\AdditionalServiceResource;
use Modules\Organizer\Transformers\VenueServices\VenueServiceResource;

final class OrderVenueServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'service' => VenueServiceResource::make($this->whenLoaded('venueService')),
            'additional_services' => AdditionalServiceResource::collection($this->whenLoaded('additionalServices')),
        ];
    }
}
