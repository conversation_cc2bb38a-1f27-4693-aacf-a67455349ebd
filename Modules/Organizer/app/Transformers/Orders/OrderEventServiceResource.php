<?php

declare(strict_types=1);

namespace Modules\Organizer\Transformers\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Organizer\Transformers\AdditionalServiceResource;
use Modules\Organizer\Transformers\ProviderResource;
use Modules\Organizer\Transformers\ServiceResource;
use Modules\Organizer\Transformers\ServiceTypeResource;

final class OrderEventServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'service' => ServiceResource::make($this->whenLoaded('service')),
            'provider' => ProviderResource::make($this->whenLoaded('provider')),
            'service_type' => ServiceTypeResource::make($this->whenLoaded('serviceType')),
            'additional_services' => AdditionalServiceResource::collection($this->whenLoaded('additionalServices')),
        ];
    }
}
