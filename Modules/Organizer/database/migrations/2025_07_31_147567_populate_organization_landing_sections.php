<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all existing organizations
        $organizations = DB::table('organizations')->select('id')->get();

        foreach ($organizations as $organization) {
            // Create organization_about record with default values
            DB::table('organization_about')->insertOrIgnore([
                'id' => app('snowflake')->next(),
                'organization_id' => $organization->id,
                'content_ar' => '',
                'content_en' => '',
                'is_active' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Create organization_achievements record with default values
            DB::table('organization_achievements')->insertOrIgnore([
                'id' => app('snowflake')->next(),
                'organization_id' => $organization->id,
                'content_ar' => '',
                'content_en' => '',
                'is_active' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Create organization_contact_info record with default values
            DB::table('organization_contact_info')->insertOrIgnore([
                'id' => app('snowflake')->next(),
                'organization_id' => $organization->id,
                'content_ar' => '',
                'content_en' => '',
                'phone_number' => null,
                'email' => null,
                'is_active' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove all records that were created by this migration
        // We'll only remove records with empty content to avoid deleting user data
        DB::table('organization_about')
            ->where('content_ar', '')
            ->where('content_en', '')
            ->where('is_active', false)
            ->delete();

        DB::table('organization_achievements')
            ->where('content_ar', '')
            ->where('content_en', '')
            ->where('is_active', false)
            ->delete();

        DB::table('organization_contact_info')
            ->where('content_ar', '')
            ->where('content_en', '')
            ->where('is_active', false)
            ->whereNull('phone_number')
            ->whereNull('email')
            ->delete();
    }
};
