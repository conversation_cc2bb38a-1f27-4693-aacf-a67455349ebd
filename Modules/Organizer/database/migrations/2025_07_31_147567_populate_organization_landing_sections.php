<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Mo<PERSON>les\Organizer\Entities\Organization;
use Modules\Organizer\Entities\OrganizationAbout;
use Modules\Organizer\Entities\OrganizationAchievement;
use Modules\Organizer\Entities\OrganizationContactInfo;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all existing organizations
        $organizations = Organization::all();

        foreach ($organizations as $organization) {
            // Create organization_about record with default values
            OrganizationAbout::firstOrCreate(
                ['organization_id' => $organization->id],
                [
                    'content_ar' => '',
                    'content_en' => '',
                    'is_active' => false,
                ]
            );

            // Create organization_achievements record with default values
            OrganizationAchievement::firstOrCreate(
                ['organization_id' => $organization->id],
                [
                    'content_ar' => '',
                    'content_en' => '',
                    'is_active' => false,
                ]
            );

            // Create organization_contact_info record with default values
            OrganizationContactInfo::firstOrCreate(
                ['organization_id' => $organization->id],
                [
                    'content_ar' => '',
                    'content_en' => '',
                    'phone_number' => null,
                    'email' => null,
                    'is_active' => false,
                ]
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
