<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organization_about', function (Blueprint $table): void {
            $table->snowflake()->primary();
            $table->foreignSnowflake('organization_id')->constrained()->cascadeOnDelete();
            $table->longText('content_ar');
            $table->longText('content_en');
            $table->boolean('is_active')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organization_about');
    }
};
