<?php

declare(strict_types=1);

namespace Modules\Support\Http\Controllers\Organizations;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Support\Actions\Organizations\ShowOrganizationLandingPageAction;
use Modules\Support\Transformers\Organizations\OrganizationLandingPageResource;

final class LandingPageController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function __invoke(int $id): JsonResponse
    {
        $organization = app(ShowOrganizationLandingPageAction::class)($id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: OrganizationLandingPageResource::make(
                $organization
            )
        );
    }
}
