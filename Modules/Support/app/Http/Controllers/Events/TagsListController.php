<?php

declare(strict_types=1);

namespace Modules\Support\Http\Controllers\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Support\Actions\Events\TagsListAction;
use Modules\Support\Transformers\Events\TagsListResource;

final class TagsListController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(int $id): JsonResponse
    {
        $tags = app(TagsListAction::class)($id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: TagsListResource::collection($tags)
        );
    }
}
