<?php

declare(strict_types=1);

namespace Modules\Support\Actions\CostEstimator\InitialBooking;

use Modules\Support\DataTransferObjects\CostEstimator\InitialBooking\InitiateBookingData;
use Modules\Support\Entities\Organization;
use Modules\Support\Services\OrderPriceCalculationService;

final class InitiateBookingAction
{
    public function __invoke(InitiateBookingData $data, int $id): void
    {
        $organization = Organization::query()->findOrFail($id);

        // Calculate total price
        $priceCalculationService = app(OrderPriceCalculationService::class);
        $totalPrice = $priceCalculationService->calculateTotalPrice($data);

        // Create order with calculated total price
        $orderData = $data->except('venue_services', 'event_services')->toArray();
        $orderData['total_price'] = $totalPrice;

        $order = $organization->orders()->create($orderData);

        // Create venue services with their additional services
        foreach ($data->venue_services as $venueService) {
            $createdVenueService = $order->venueServices()->create([
                'venue_service_id' => $venueService['id'],
            ]);

            // Create additional services for this venue service
            foreach ($venueService['additional_services_ids'] as $additionalServiceId) {
                $createdVenueService->additionalServices()->create([
                    'additional_service_id' => $additionalServiceId,
                ]);
            }
        }

        // Create event services with their additional services
        foreach ($data->event_services as $eventService) {
            $createdEventService = $order->eventServices()->create([
                'service_type_id' => $eventService['service_type_id'],
                'service_id' => $eventService['service_id'],
                'provider_id' => $eventService['provider_id'],
            ]);

            // Create additional services for this event service
            foreach ($eventService['additional_services_ids'] as $additionalServiceId) {
                $createdEventService->additionalServices()->create([
                    'additional_service_id' => $additionalServiceId,
                ]);
            }
        }
    }
}
