<?php

declare(strict_types=1);

namespace Modules\Support\Transformers\Events;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class TagsListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'color' => $this->color,
        ];
    }
}
