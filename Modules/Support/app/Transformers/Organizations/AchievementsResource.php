<?php

declare(strict_types=1);

namespace Modules\Support\Transformers\Organizations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class AchievementsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'content_ar' => $this->content_ar,
            'content_en' => $this->content_en,
        ];
    }
}
