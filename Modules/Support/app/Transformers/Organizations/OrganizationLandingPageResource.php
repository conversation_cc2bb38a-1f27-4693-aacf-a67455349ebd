<?php

declare(strict_types=1);

namespace Modules\Support\Transformers\Organizations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class OrganizationLandingPageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'logo' => $this->logo,
            'description' => $this->description,
            'primary_color' => $this->primary_color,
            'secondary_color' => $this->secondary_color,
            'status' => $this->status,
            'about' => $this->is_active ? AboutResource::make($this->about) : null,
            'achievements' => $this->is_active ? AchievementsResource::make($this->achievements) : null,
            'contact_info' => $this->is_active ? ContactInfoResource::make($this->contactInfo) : null,
        ];
    }
}
