<?php

declare(strict_types=1);

namespace Modules\Support\Transformers\Organizations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class OrganizationLandingPageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'logo' => $this->logo,
            'description' => $this->description,
            'primary_color' => $this->primary_color,
            'secondary_color' => $this->secondary_color,
            'status' => $this->status,
            'about' => $this->when($this->about && $this->about->is_active, AboutResource::make($this->about)),
            'achievements' => $this->when($this->achievements && $this->achievements->is_active, AchievementsResource::make($this->achievements)),
            'contact_info' => $this->when($this->contactInfo && $this->contactInfo->is_active, ContactInfoResource::make($this->contactInfo)),
        ];
    }
}
