<?php

declare(strict_types=1);

namespace Modules\Support\Services;

use Modules\Support\DataTransferObjects\CostEstimator\InitialBooking\InitiateBookingData;
use Modules\Support\Entities\Service;
use Modules\Support\Entities\ServiceAdditionalService;
use Modules\Support\Entities\VenueAdditionalService;
use Modules\Support\Entities\VenueService;

final class OrderPriceCalculationService
{
    public function calculateTotalPrice(InitiateBookingData $data): float
    {
        $totalPrice = 0.0;

        // Calculate venue services price
        foreach ($data->venue_services as $venueServiceData) {
            $venueService = VenueService::query()->findOrFail($venueServiceData['id']);
            $totalPrice += $this->calculateVenueServicePrice($venueService, $data->number_of_attendees);

            // Calculate additional services for venue service
            foreach ($venueServiceData['additional_services_ids'] as $additionalServiceId) {
                /** @var VenueAdditionalService $additionalService */
                $additionalService = VenueAdditionalService::query()->where('id', $additionalServiceId)->firstOrFail();
                $totalPrice += $additionalService->price;
            }
        }

        // Calculate event services price
        foreach ($data->event_services as $eventServiceData) {
            $service = Service::query()->findOrFail($eventServiceData['service_id']);
            $totalPrice += $this->calculateEventServicePrice($service, $data->number_of_attendees);

            // Calculate additional services for event service
            foreach ($eventServiceData['additional_services_ids'] as $additionalServiceId) {
                /** @var ServiceAdditionalService $additionalService */
                $additionalService = ServiceAdditionalService::query()->where('id', $additionalServiceId)->firstOrFail();
                $totalPrice += $additionalService->price;
            }
        }

        return $totalPrice;
    }

    private function calculateVenueServicePrice(VenueService $venueService, int $numberOfAttendees): float
    {
        // For venue services, pricing strategy determines how to calculate the price
        switch ($venueService->pricing_strategy) {
            case 'per_person':
                return $venueService->price * $numberOfAttendees;
            case 'per_hour':
            case 'fixed':
            default:
                return $venueService->price;
        }
    }

    private function calculateEventServicePrice(Service $service, int $numberOfAttendees): float
    {
        // For event services, pricing strategy determines how to calculate the price
        switch ($service->pricing_strategy?->value) {
            case 'per_person':
                return $service->price * $numberOfAttendees;
            case 'per_hour':
            case 'fixed':
            default:
                return $service->price;
        }
    }
}
