<?php

declare(strict_types=1);

namespace Modules\Support\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Support\Presenters\VenueAdditionalServicePresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class VenueAdditionalService extends Model
{
    use HasFactory;
    use Snowflakes;
    use VenueAdditionalServicePresenter;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'price',
        'venue_service_id',
    ];

    public function service(): BelongsTo
    {
        return $this->belongsTo(
            VenueService::class
        );
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'venue_service_id' => SnowflakeCast::class,
            'price' => 'float',
        ];
    }
}
