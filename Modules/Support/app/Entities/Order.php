<?php

declare(strict_types=1);

namespace Modules\Support\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Order extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'email',
        'country_code',
        'phone',
        'event_name',
        'event_duration',
        'start_at',
        'end_at',
        'event_type',
        'category_id',
        'number_of_attendees',
        'operation_area_id',
        'venue_id',
        'organization_id',
        'total_price',
    ];

    /**
     * Get the organization that owns the order.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get all the venue services for the order.
     */
    public function venueServices(): HasMany
    {
        return $this->hasMany(OrderVenueService::class);
    }

    /**
     * Get all the event services for the order.
     */
    public function eventServices(): HasMany
    {
        return $this->hasMany(OrderEventService::class);
    }

    /**
     * Get the venue for the order.
     */
    public function venue(): BelongsTo
    {
        return $this->belongsTo(Venue::class);
    }

    /**
     * Get the category for the order.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the operation area for the order.
     */
    public function operationArea(): BelongsTo
    {
        return $this->belongsTo(OperationArea::class);
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'category_id' => SnowflakeCast::class,
            'operation_area_id' => SnowflakeCast::class,
            'venue_id' => SnowflakeCast::class,
            'start_at' => 'datetime',
            'end_at' => 'datetime',
            'total_price' => 'decimal:2',
        ];
    }
}
