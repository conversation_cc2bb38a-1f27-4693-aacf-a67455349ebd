<?php

declare(strict_types=1);

namespace Modules\Support\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class OrderVenueService extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'order_id',
        'venue_service_id',
    ];

    /**
     * Get the order that owns the venue service.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the venue service details.
     */
    public function venueService(): BelongsTo
    {
        return $this->belongsTo(VenueService::class);
    }

    /**
     * Get the additional services for this venue service.
     */
    public function additionalServices(): HasMany
    {
        return $this->hasMany(OrderVenueServiceAdditionalService::class);
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'order_id' => SnowflakeCast::class,
            'venue_service_id' => SnowflakeCast::class,
        ];
    }
}
