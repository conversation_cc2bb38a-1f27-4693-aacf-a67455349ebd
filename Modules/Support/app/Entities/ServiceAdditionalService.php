<?php

declare(strict_types=1);

namespace Modules\Support\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Support\Presenters\ServiceAdditionalServicePresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class ServiceAdditionalService extends Model
{
    use ServiceAdditionalServicePresenter;
    use Snowflakes;

    public function service(): BelongsTo
    {
        return $this->belongsTo(
            Service::class
        );
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'service_id' => SnowflakeCast::class,
            'price' => 'float',
        ];
    }
}
