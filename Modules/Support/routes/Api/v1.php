<?php

declare(strict_types=1);

use Illuminate\Http\Middleware\HandleCors;
use Illuminate\Support\Facades\Route;
use Modules\Support\Http\Controllers\Accounts\CreatePasswordController;
use Modules\Support\Http\Controllers\Accounts\ForgetPasswordController;
use Modules\Support\Http\Controllers\Accounts\ResendVerificationOtpController;
use Modules\Support\Http\Controllers\Accounts\ResetPasswordController;
use Modules\Support\Http\Controllers\Accounts\VerifyOtpController;
use Modules\Support\Http\Controllers\Article\GetLatestArticleController;
use Modules\Support\Http\Controllers\Article\IndexArticleController;
use Modules\Support\Http\Controllers\Article\ShowArticleController;
use Modules\Support\Http\Controllers\Categories\ListCategoryController;
use Modules\Support\Http\Controllers\ContactUs\ContactUsController;
use Modules\Support\Http\Controllers\CostEstimator\InitialBooking\InitiateBookingController;
use Modules\Support\Http\Controllers\CostEstimator\Services\ListAdditionalServiceController;
use Modules\Support\Http\Controllers\CostEstimator\Sponsors\ListSponsorController;
use Modules\Support\Http\Controllers\Countries\ListCountryController;
use Modules\Support\Http\Controllers\Devices\StoreDeviceController;
use Modules\Support\Http\Controllers\Events\IndexAgendaController;
use Modules\Support\Http\Controllers\Events\IndexSpeakerController;
use Modules\Support\Http\Controllers\Events\IndexSupervisorController;
use Modules\Support\Http\Controllers\Events\Invitations\ShowInvitationBadgeController;
use Modules\Support\Http\Controllers\Events\SelfRegistrationController;
use Modules\Support\Http\Controllers\Events\ShowEventController;
use Modules\Support\Http\Controllers\Events\TagsListController;
use Modules\Support\Http\Controllers\Newsletters\SubscribeController;
use Modules\Support\Http\Controllers\OperationAreas\IndexOperationAreaController;
use Modules\Support\Http\Controllers\Organizations\LandingPageController;
use Modules\Support\Http\Controllers\Organizations\ShowOrganizationController;
use Modules\Support\Http\Controllers\Partner\IndexPartnerController;
use Modules\Support\Http\Controllers\Preferences\SetLocaleController;
use Modules\Support\Http\Controllers\Providers\ListProviderController;
use Modules\Support\Http\Controllers\Services\GoogleMaps\GetLocationController;
use Modules\Support\Http\Controllers\ServiceTypes\ListServiceTypeController;
use Modules\Support\Http\Controllers\Statistics\StatisticsController;
use Modules\Support\Http\Controllers\SuccessStory\GetLatestSuccessStoryController;
use Modules\Support\Http\Controllers\SuccessStory\IndexSuccessStoryController;
use Modules\Support\Http\Controllers\SuccessStory\ShowSuccessStoryController;
use Modules\Support\Http\Controllers\SupportRequests\StoreSupportRequestController;
use Modules\Support\Http\Controllers\Testimony\IndexTestimonyController;
use Modules\Support\Http\Controllers\Topic\IndexTopicController;
use Modules\Support\Http\Controllers\Venues\IndexVenueController;
use Modules\Support\Http\Controllers\VenueServiceAdditionalServices\IndexVenueServiceAdditionalServiceController;
use Modules\Support\Http\Controllers\VenueServices\IndexVenueServiceController;
use Modules\Support\Http\Middleware\EnsureCostEstimatorEnabled;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::name('support.')->prefix('support')->whereNumber([
    'id',
    'operationAreaId',
    'venueId',
    'serviceId',
    'successStoryId',
    'articleId',
])->group(function (): void {

    Route::name('accounts.')->prefix('accounts')->group(function (): void {
        Route::post('verify', VerifyOtpController::class)->name('verify-otp');
        Route::post('resend-otp', ResendVerificationOtpController::class)->name('resend-otp');

        Route::name('password.')->prefix('password')->group(function (): void {
            Route::post('forget', ForgetPasswordController::class)->name('forget');
            Route::post('reset', ResetPasswordController::class)->name('reset');
            Route::post('create', CreatePasswordController::class)->name('create');
        });
    });

    Route::name('lists.')->prefix('lists')->group(function (): void {
        Route::name('countries.')->prefix('countries')->group(function (): void {
            Route::get(
                '/',
                ListCountryController::class
            );
        });

        Route::name('categories.')->prefix('categories')->group(function (): void {
            Route::get(
                '/',
                ListCategoryController::class
            );
        });

        Route::name('service-types.')->prefix('{id}/service-types')->group(function (): void {
            Route::get(
                '/',
                ListServiceTypeController::class
            );
        });

        Route::name('providers.')->prefix('{id}/providers')->group(function (): void {
            Route::get(
                '/',
                ListProviderController::class
            );
        });

        Route::middleware(['auth:sanctum'])->group(function (): void {});
    });

    Route::middleware(['auth:sanctum'])->group(function (): void {
        Route::prefix('preferences')->name('preferences.')->group(callback: static function (): void {
            Route::post(
                uri: '/language',
                action: SetLocaleController::class
            )->name(name: 'set-locale');
        });
    });

    Route::middleware(['auth:sanctum'])->group(function (): void {
        Route::prefix('devices')->name('devices.')->group(function (): void {
            Route::post(
                '/',
                StoreDeviceController::class
            )->name('store');
        });
    });

    Route::middleware(['auth:sanctum'])->group(function (): void {
        Route::prefix('services')->name('services.')->group(function (): void {
            Route::get(
                '/get-location',
                GetLocationController::class
            )->name('get-location');
        });
    });

    Route::post('/contact-us', ContactUsController::class)->name('contact-us');

    Route::prefix('support-requests')->name('support-requests.')->group(function (): void {
        Route::post(
            '/',
            StoreSupportRequestController::class
        )->name('store');
    });

    Route::withoutMiddleware([
        HandleCors::class,
    ])->name(
        'events.'
    )->prefix(
        'events/{id}'
    )->group(function (): void {
        Route::get(
            '/',
            ShowEventController::class
        )->name('details');

        Route::post(
            '/self-registration',
            SelfRegistrationController::class
        )->name('self-registration');

        Route::name('supervisors.')->prefix('supervisors')->group(function (): void {
            Route::get(
                '/',
                IndexSupervisorController::class
            )->name('index');
        });

        Route::name('speakers.')->prefix('speakers')->group(function (): void {
            Route::get(
                '/',
                IndexSpeakerController::class
            )->name('index');
        });

        Route::name('sessions.')->prefix('sessions')->group(function (): void {
            Route::get(
                '/',
                IndexAgendaController::class
            )->name('index');
        });

        Route::name('tags.')->prefix('tags')->group(function (): void {
            Route::get(
                '/',
                TagsListController::class
            )->name('index');
        });
    });

    Route::name('success-stories.')->prefix('success-stories')->group(function (): void {
        Route::get(
            '/',
            IndexSuccessStoryController::class
        )->name('index');

        Route::get(
            'latest',
            GetLatestSuccessStoryController::class
        )->name('latest');

        Route::get(
            '/{successStoryId}',
            ShowSuccessStoryController::class
        )->name('show');
    });

    Route::name('partners.')->prefix('partners')->group(function (): void {
        Route::get(
            '/',
            IndexPartnerController::class
        )->name('index');
    });

    Route::name('testimonies.')->prefix('testimonies')->group(function (): void {
        Route::get(
            '/',
            IndexTestimonyController::class
        )->name('index');
    });

    Route::name('newsletters.')->prefix('newsletters')->group(function (): void {
        Route::post(
            '/subscribe',
            SubscribeController::class
        )->name('subscribe');
    });

    Route::name('articles.')->prefix('articles')->group(function (): void {
        Route::get(
            '/',
            IndexArticleController::class
        )->name('index');

        Route::get(
            'latest',
            GetLatestArticleController::class
        )->name('latest');

        Route::get(
            '/{articleId}',
            ShowArticleController::class
        )->name('show');
    });

    Route::name('topics.')->prefix('topics')->group(function (): void {
        Route::get(
            '/',
            IndexTopicController::class
        )->name('index');
    });

    Route::name('statistics.')->prefix('statistics')->group(function (): void {
        Route::get(
            '/',
            StatisticsController::class
        )->name('index');
    });

    Route::middleware([EnsureCostEstimatorEnabled::class])->name('calculator.')->prefix('calculator')->group(function (): void {
        Route::get(
            '/{id}',
            ShowOrganizationController::class
        )->name('show');

        Route::name('operation-areas.')->prefix('{id}/operation-areas')->group(function (): void {
            Route::get(
                '/',
                IndexOperationAreaController::class
            )->name('index');

            Route::name('venues.')->prefix('{operationAreaId}/venues')->group(function (): void {
                Route::get(
                    '/',
                    IndexVenueController::class
                )->name('index');

                Route::name('services.')->prefix('{venueId}/services')->group(function (): void {
                    Route::get(
                        '/',
                        IndexVenueServiceController::class
                    )->name('index');

                    Route::name('additional-services.')->prefix('{serviceId}/additional-services')->group(function (): void {
                        Route::get(
                            '/',
                            IndexVenueServiceAdditionalServiceController::class
                        )->name('index');
                    });
                });
            });
        });

        Route::name('service-types.')->prefix('{id}/service-types')->group(function (): void {
            Route::get(
                '/',
                Modules\Support\Http\Controllers\CostEstimator\ServiceTypes\ListServiceTypeController::class
            );
        });

        Route::name('providers.')->prefix('{id}/providers')->group(function (): void {
            Route::get(
                '/',
                Modules\Support\Http\Controllers\CostEstimator\Providers\ListProviderController::class
            );
        });

        Route::name('services.')->prefix('{id}/services')->group(function (): void {
            Route::get(
                '/',
                Modules\Support\Http\Controllers\CostEstimator\Services\ListServiceController::class
            );

            Route::name('additional-services.')->prefix('{serviceId}/additional-services')->group(function (): void {
                Route::get(
                    '/',
                    ListAdditionalServiceController::class,
                )->name('index');
            });
        });

        Route::name('sponsors.')->prefix('{id}/sponsors')->group(function (): void {
            Route::get(
                '/',
                ListSponsorController::class
            );
        });

        Route::name('booking.')->prefix('{id}/booking')->group(function (): void {
            Route::name('initiate.')->prefix('initiate')->group(function (): void {
                Route::post(
                    '/',
                    InitiateBookingController::class
                )->name('store');
            });
        });
    });

    Route::name('events.')->prefix('events/{id}')->group(function (): void {
        Route::name('invitations.')->prefix('invitations/{invitedId}')->group(function (): void {
            Route::get(
                '/badge',
                ShowInvitationBadgeController::class
            )->name('badge');
        });
    });

    Route::name('organizations.')->prefix('organizations')->group(function (): void {
        Route::get(
            '/{id}/landing-page',
            LandingPageController::class
        )->name('landing-page');
    });
});
