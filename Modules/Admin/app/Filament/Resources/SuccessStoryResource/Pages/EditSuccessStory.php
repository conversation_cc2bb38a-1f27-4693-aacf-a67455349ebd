<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\SuccessStoryResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Admin\Filament\Resources\SuccessStoryResource;

final class EditSuccessStory extends EditRecord
{
    protected static string $resource = SuccessStoryResource::class;


    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
