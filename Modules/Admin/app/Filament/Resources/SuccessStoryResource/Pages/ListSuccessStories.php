<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\SuccessStoryResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Admin\Filament\Resources\SuccessStoryResource;

final class ListSuccessStories extends ListRecords
{
    protected static string $resource = SuccessStoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
