<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\OrganizationResource\RelationManagers;

use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

final class OrganizersRelationManager extends RelationManager
{
    protected static string $relationship = 'organizers';

    public function form(Form $form): Form
    {
        return $form
            ->schema([

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('type')->sortable(),
                Tables\Columns\TextColumn::make('email'),
                Tables\Columns\TextColumn::make('mobile')->default('No Phone'),
                Tables\Columns\TextColumn::make('locale'),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(fn ($state) => $state ? 'Active' : 'Not Active')
                    ->color(fn (string $state): string => $state ? 'success' : 'danger'),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->formatStateUsing(fn ($state) => 'owner' === $state ? 'Owner' : $state)
                    ->color(fn (string $state): string => 'owner' === $state ? 'success' : 'warning'),
            ])
            ->filters([

            ])
            ->headerActions([

            ])
            ->actions([

            ])
            ->bulkActions([

            ]);
    }
}
