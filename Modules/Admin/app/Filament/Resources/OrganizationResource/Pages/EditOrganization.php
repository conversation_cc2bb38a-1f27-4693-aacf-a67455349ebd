<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\OrganizationResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Admin\Entities\Organizer;
use Modules\Admin\Filament\Resources\OrganizationResource;

final class EditOrganization extends EditRecord
{
    protected static string $resource = OrganizationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function afterSave(): void
    {
        if ($this->record->wasChanged('status')) {
            Organizer::query()->where(
                'organization_id',
                $this->record->getAttribute('id'),
            )->update([
                'status' => $this->record->getAttribute('status'),
            ]);
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
