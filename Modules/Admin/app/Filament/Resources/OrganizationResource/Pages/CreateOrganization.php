<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\OrganizationResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Modules\Admin\Entities\Organization;
use Modules\Admin\Entities\Organizer;
use Modules\Admin\Filament\Resources\OrganizationResource;

final class CreateOrganization extends CreateRecord
{
    protected static string $resource = OrganizationResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $record = Organization::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'status' => $data['status'],

        ]);

        $organizer_record = Organizer::create([
            'name' => $data['owner_name'],
            'email' => $data['owner_email'],
            'type' => $data['type'],
            'locale' => $data['locale'],
            'password' => Hash::make($data['password']),
            'status' => $data['owner_status'],
            'mobile' => $data['mobile'],
            'country_id' => $data['country_id'],
            'organization_id' => $record->id,

        ]);

        $record->update([
            'owner_id' => $organizer_record->id
        ]);

        return $record;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
