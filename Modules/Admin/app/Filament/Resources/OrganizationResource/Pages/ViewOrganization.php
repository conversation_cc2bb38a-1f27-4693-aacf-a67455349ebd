<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\OrganizationResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Modules\Admin\Filament\Resources\OrganizationResource;

final class ViewOrganization extends ViewRecord
{
    protected static string $resource = OrganizationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

}
