<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources;

use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Modules\Admin\Enums\SupportRequestStatus;
use Modules\Admin\Enums\SupportRequestType;
use Modules\Admin\Filament\Resources\SupportRequestResource\Pages;
use Modules\Support\Entities\SupportRequest;

final class SupportRequestResource extends Resource
{
    protected static ?string $model = SupportRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-paper-airplane';

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit(Model $record): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->searchable(),
                TextColumn::make('email')->searchable(),
                TextColumn::make('country.mobile_code')->label('Country Code'),
                TextColumn::make('mobile'),
                TextColumn::make('company'),
                TextColumn::make('request_type'),
                TextColumn::make('status')->badge()
                    ->color(function (string $state): string {
                        return match ($state) {
                            SupportRequestStatus::REJECTED->value => 'danger',
                            SupportRequestStatus::ACCEPTED->value => 'success',
                            default => 'warning',
                        };
                    }),
                TextColumn::make('note')
                    ->default('-')
                    ->limit(30)
                    ->tooltip(fn ($record) => $record->note)->copyable(),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->dateTime()
                    ->sortable(),
            ])->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('request_type')
                    ->options([
                        'demo' => SupportRequestType::DEMO->value,
                        'trial' => SupportRequestType::TRIAL->value
                    ]),
                SelectFilter::make('status')
                    ->options([
                        'pending' => SupportRequestStatus::PENDING->value,
                        'accepted' => SupportRequestStatus::ACCEPTED->value,
                        'rejected' => SupportRequestStatus::REJECTED->value

                    ])
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                // Button to Accept the Request
                Action::make('accept')
                    ->label('Accept')
                    ->color('success')
                    ->action(function (Model $record, array $data): void {
                        $record->update([
                            'status' => SupportRequestStatus::ACCEPTED->value,
                            'note' => $data['note']
                        ]);
                    })
                    ->requiresConfirmation()
                    ->icon('heroicon-o-check')
                    ->form([
                        Textarea::make('note')
                            ->label('Note')
                            ->placeholder('Add a note here...')
                            ->default(fn (Model $record) => $record->note)
                    ])->visible(fn (Model $record) => $record->status !== SupportRequestStatus::ACCEPTED->value),
                // Button to Reject the Request
                Action::make('reject')
                    ->label('Reject')
                    ->color('danger')
                    ->action(function (Model $record, array $data): void {
                        $record->update([
                            'status' => SupportRequestStatus::REJECTED->value,
                            'note' => $data['note']
                        ]);
                    })
                    ->requiresConfirmation()
                    ->icon('heroicon-o-x-circle')
                    ->form([
                        Textarea::make('note')
                            ->label('Note')
                            ->placeholder('Add a note here...')
                            ->default(fn (Model $record) => $record->note)
                    ])->visible(fn (Model $record) => $record->status !== SupportRequestStatus::REJECTED->value),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSupportRequests::route('/'),
            'create' => Pages\CreateSupportRequest::route('/create'),
            'edit' => Pages\EditSupportRequest::route('/{record}/edit'),
        ];
    }
}
