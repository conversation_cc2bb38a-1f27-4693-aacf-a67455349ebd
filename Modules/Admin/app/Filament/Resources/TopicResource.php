<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Table;
use Modules\Admin\Entities\Topic;
use Modules\Admin\Filament\Resources\TopicResource\Pages;

final class TopicResource extends Resource
{
    protected static ?string $model = Topic::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name_ar')->label('Arabic Name')->required(),
                TextInput::make('name_en')->label('English Name')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')->label('Arabic Name'),
                Tables\Columns\TextColumn::make('name_en')->label('English Name'),
            ])
            ->filters([

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                DeleteAction::make()
                    ->failureNotificationTitle('Cannot delete topic with articles')
                    ->before(function (DeleteAction $action, Topic $record): void {
                        if ($record->articles()->count() > 0) {
                            $action->failure();
                            $action->cancel();
                        }
                    }),
            ]);
    }

    public static function getRelations(): array
    {
        return [

        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTopics::route('/'),
            'create' => Pages\CreateTopic::route('/create'),
            'edit' => Pages\EditTopic::route('/{record}/edit'),
        ];
    }
}
