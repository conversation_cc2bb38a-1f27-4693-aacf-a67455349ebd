<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources;

use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Modules\Admin\Filament\Resources\NewsletterSubscriberResource\Pages;
use Modules\Support\Entities\NewsletterSubscriber;
use Modules\Support\Enums\NewsletterStatus;

final class NewsletterSubscriberResource extends Resource
{
    protected static ?string $model = NewsletterSubscriber::class;

    protected static ?string $navigationIcon = 'heroicon-o-envelope';

    public static function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        NewsletterStatus::SUBSCRIBED->value => 'success',
                        NewsletterStatus::UNSUBSCRIBED->value => 'danger',
                        default => 'primary',
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        NewsletterStatus::SUBSCRIBED->value => 'Subscribed',
                        NewsletterStatus::UNSUBSCRIBED->value => 'Unsubscribed',
                    ]),
            ])
            ->actions([])
            ->bulkActions([]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNewsletterSubscribers::route('/'),
        ];
    }
}
