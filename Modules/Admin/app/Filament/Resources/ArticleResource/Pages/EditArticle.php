<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\ArticleResource\Pages;

use Filament\Actions;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Modules\Admin\Entities\Article;
use Modules\Admin\Enums\ArticleStatus;
use Modules\Admin\Filament\Resources\ArticleResource;

final class EditArticle extends EditRecord
{
    protected static string $resource = ArticleResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Action::make('publish')
                ->label('Publish')
                ->action(fn (Article $record) => $record->publish())
                ->hidden(fn (Article $record) => ArticleStatus::PUBLISHED === $record->status)
                ->successNotification(
                    Notification::make()
                        ->success()
                        ->title('Article Published')
                        ->body('The article has been published successfully.'),
                ),
        ];
    }
}
