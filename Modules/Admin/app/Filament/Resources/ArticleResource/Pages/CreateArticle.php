<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\ArticleResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Modules\Admin\Enums\ArticleStatus;
use Modules\Admin\Filament\Resources\ArticleResource;

final class CreateArticle extends CreateRecord
{
    protected static string $resource = ArticleResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if ($data['status'] === ArticleStatus::PUBLISHED->value) {
            $data['published_at'] = now();
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
