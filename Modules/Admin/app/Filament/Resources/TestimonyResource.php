<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources;

use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Modules\Admin\Filament\Resources\TestimonyResource\Pages\CreateTestimony;
use Modules\Admin\Filament\Resources\TestimonyResource\Pages\EditTestimony;
use Modules\Admin\Filament\Resources\TestimonyResource\Pages\ListTestimonies;
use Modules\Support\Entities\Testimony;
use Modules\Support\Enums\TestimonyStatus;

final class TestimonyResource extends Resource
{
    protected static ?string $model = Testimony::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-bottom-center-text';

    protected static ?string $modelLabel = 'Testimony';
    protected static ?string $pluralModelLabel = 'Testimonies';

    public static function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('name')
                ->required()
                ->minLength(2)
                ->maxLength(512),

            TextInput::make('position')
                ->required()
                ->minLength(2)
                ->maxLength(512),

            FileUpload::make('image')
                ->disk('s3')
                ->image()
                ->maxSize(3072) // 3MB
                ->directory('testimonies/images')
                ->visibility('public'),

            Textarea::make('description_ar')
                ->label('Arabic Description')
                ->required()
                ->maxLength(1024),

            Textarea::make('description_en')
                ->label('English Description')
                ->required()
                ->maxLength(1024),

            Toggle::make('status')
                ->onColor('success')
                ->offColor('danger')
                ->onIcon('heroicon-m-check')
                ->offIcon('heroicon-m-x-mark')
                ->inline()
                ->afterStateHydrated(function (Toggle $component, $state): void {
                    $component->state($state === TestimonyStatus::PUBLIC->value);
                })
                ->dehydrateStateUsing(fn ($state): string => $state ? TestimonyStatus::PUBLIC->value : TestimonyStatus::PRIVATE->value),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('image')
                    ->square(),
                TextColumn::make('name')
                    ->searchable(),
                TextColumn::make('position')
                    ->searchable(),
                TextColumn::make('description_ar')
                    ->label('Arabic Description')
                    ->searchable()
                    ->limit(50),
                TextColumn::make('description_en')
                    ->label('English Description')
                    ->searchable()
                    ->limit(50),
                ToggleColumn::make('status')
                    ->onColor('success')
                    ->offColor('danger')
                    ->getStateUsing(fn (Testimony $record): bool => $record->status === TestimonyStatus::PUBLIC->value)
                    ->afterStateUpdated(function (Testimony $record, bool $state): void {
                        $record->update(['status' => $state ? TestimonyStatus::PUBLIC->value : TestimonyStatus::PRIVATE->value]);
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        TestimonyStatus::PUBLIC->value => 'Public',
                        TestimonyStatus::PRIVATE->value => 'Private',
                    ])
                    ->placeholder('All'),
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListTestimonies::route('/'),
            'create' => CreateTestimony::route('/create'),
            'edit' => EditTestimony::route('/{record}/edit'),
        ];
    }
}
