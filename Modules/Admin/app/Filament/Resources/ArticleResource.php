<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources;

use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Modules\Admin\Entities\Article;
use Modules\Admin\Entities\Topic;
use Modules\Admin\Enums\ArticleStatus;
use Modules\Admin\Filament\Resources\ArticleResource\Pages;

final class ArticleResource extends Resource
{
    protected static ?string $model = Article::class;

    protected static ?string $navigationIcon = 'heroicon-s-newspaper';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('title_en')
                    ->label('English Title')
                    ->required()
                    ->maxLength(300)
                    ->minLength(2),

                TextInput::make('title_ar')
                    ->label('Arabic Title')
                    ->required()
                    ->maxLength(300)
                    ->minLength(2),


                TextInput::make('preview_en')
                    ->maxLength(300)
                    ->minLength(3)
                    ->label('English Preview')
                    ->required(),

                TextInput::make('preview_ar')
                    ->maxLength(150)
                    ->minLength(3)
                    ->label('Arabic Preview')
                    ->required(),


                Select::make('status')
                    ->required()
                    ->default(ArticleStatus::DRAFT->value)
                    ->options([
                        ArticleStatus::DRAFT->value => 'Draft',
                        ArticleStatus::PUBLISHED->value => 'Published',
                    ]),

                Select::make('topic_id')
                    ->label('Topic')
                    ->options(Topic::all()->pluck('name_en', 'id'))
                    ->searchable()
                    ->required(),

                RichEditor::make('content_en')
                    ->label('English Details')
                    ->required(),

                RichEditor::make('content_ar')
                    ->label('Arabic Details')
                    ->required(),

                FileUpload::make('image')
                    ->disk('s3')
                    ->image()
                    ->required()
                    ->directory('articles/images')
                    ->visibility('public'),

                TextInput::make('meta_title_en')
                    ->label('English Meta Title')
                    ->required()
                    ->maxLength(300)
                    ->minLength(2),

                TextInput::make('meta_title_ar')
                    ->label('Arabic Meta Title')
                    ->required()
                    ->maxLength(300)
                    ->minLength(2),

                TextInput::make('keywords_en')
                    ->label('English Keywords')
                    ->maxLength(255)
                    ->required(),

                TextInput::make('keywords_ar')
                    ->label('Arabic Keywords')
                    ->maxLength(255)
                    ->required(),

                TextInput::make('meta_description_en')
                    ->label('English Meta Description')
                    ->maxLength(255)
                    ->required(),

                TextInput::make('meta_description_ar')
                    ->label('Arabic Meta Description')
                    ->maxLength(255)
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title_en')
                    ->label('English Title'),
                TextColumn::make('title_ar')
                    ->label('Arabic Title'),
                TextColumn::make('topic.name_en'),
                TextColumn::make('status'),
                TextColumn::make('created_at')->dateTime(),
                ImageColumn::make('image'),
            ])
            ->filters([

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Action::make('publish')
                    ->label('Publish')
                    ->action(fn (Article $record) => $record->publish())
                    ->hidden(fn (Article $record) => ArticleStatus::PUBLISHED === $record->status)
                    ->successNotification(
                        Notification::make()
                            ->success()
                            ->title('Article Published')
                            ->body('The article has been published successfully.'),
                    ),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [

        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListArticles::route('/'),
            'create' => Pages\CreateArticle::route('/create'),
            'edit' => Pages\EditArticle::route('/{record}/edit'),
        ];
    }
}
