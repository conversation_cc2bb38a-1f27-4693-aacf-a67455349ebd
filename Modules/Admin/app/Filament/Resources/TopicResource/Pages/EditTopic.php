<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\TopicResource\Pages;

use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Modules\Admin\Entities\Topic;
use Modules\Admin\Filament\Resources\TopicResource;

final class EditTopic extends EditRecord
{
    protected static string $resource = TopicResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->failureNotificationTitle('Cannot delete topic with articles')
                ->before(function (Action $action, Topic $record): void {
                    if ($record->articles()->count() > 0) {
                        $action->failure();
                        $action->cancel();
                    }
                }),
        ];
    }
}
