<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\TopicResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Admin\Filament\Resources\TopicResource;

final class ListTopics extends ListRecords
{
    protected static string $resource = TopicResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
