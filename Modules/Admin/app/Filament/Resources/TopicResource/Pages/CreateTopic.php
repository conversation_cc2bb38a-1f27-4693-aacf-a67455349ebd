<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\TopicResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Modules\Admin\Filament\Resources\TopicResource;

final class CreateTopic extends CreateRecord
{
    protected static string $resource = TopicResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
