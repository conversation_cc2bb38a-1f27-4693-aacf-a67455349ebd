<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Modules\Admin\Entities\Organizer;
use Modules\Admin\Filament\Resources\OrganizerResource\Pages;

final class OrganizerResource extends Resource
{
    protected static ?string $model = Organizer::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    public static function canCreate(): bool
    {
        return false;
    }

    public static function canDelete(Model $record): bool
    {
        return false;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->regex('/^[\pL\s\-]+$/u'),
                TextInput::make('type')
                    ->required()
                    ->readOnly()
                    ->maxLength(255),
                TextInput::make('email')
                    ->email()
                    ->unique(ignoreRecord:true)
                    ->required()
                    ->maxLength(255),
                TextInput::make('mobile')
                    ->unique(ignoreRecord:true)
                    ->maxLength(15),
                Select::make('locale')
                    ->options([
                        'en' => 'en',
                        'ar' => 'ar'
                    ]),
                Toggle::make('status')
                    ->default(1)
                    ->required(),
                Select::make('country_id')
                    ->relationship('country', 'code')
                    ->preload()
                    ->searchable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->sortable(),
                TextColumn::make('name')->searchable()->sortable(),
                TextColumn::make('type')->sortable(),
                TextColumn::make('email'),
                TextColumn::make('mobile')->default('No Phone'),
                TextColumn::make('locale'),
                TextColumn::make('status')->badge()
                    ->formatStateUsing(fn ($state) => $state ? 'Active' : 'Not Active')
                    ->color(fn (string $state): string => $state ? 'success' : 'danger'),
                TextColumn::make('organization.name')->searchable(),
                TextColumn::make('country.code')->searchable(),

            ])
            ->filters([
                SelectFilter::make('name')
                    ->relationship('organization', 'name')
                    ->preload()
                    ->searchable(),

            ], layout: FiltersLayout::AboveContent)
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrganizers::route('/'),
            'create' => Pages\CreateOrganizer::route('/create'),
            'edit' => Pages\EditOrganizer::route('/{record}/edit'),
        ];
    }

}
