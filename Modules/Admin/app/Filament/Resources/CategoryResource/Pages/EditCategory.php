<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\CategoryResource\Pages;

use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Modules\Admin\Entities\Category;
use Modules\Admin\Filament\Resources\CategoryResource;

final class EditCategory extends EditRecord
{
    protected static string $resource = CategoryResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->failureNotificationTitle('Cannot delete category with success stories')
                ->before(function (Action $action, Category $record): void {
                    if ($record->successStories()->count() > 0) {
                        $action->failure();
                        $action->cancel();
                    }
                }),
        ];
    }
}
