<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Split;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Modules\Admin\Entities\Organization;
use Modules\Admin\Entities\Organizer;
use Modules\Admin\Filament\Resources\OrganizationResource\Pages;
use Modules\Admin\Filament\Resources\OrganizationResource\RelationManagers\OrganizersRelationManager;

final class OrganizationResource extends Resource
{
    protected static ?string $model = Organization::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';

    public static function canDelete(Model $record): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Organization')->schema([
                    Split::make([
                        TextInput::make('name')
                            ->required()
                            ->regex('/^[\pL\s\-]+$/u')
                            ->maxLength(255),
                        TextInput::make('email')
                            ->email()
                            ->unique(ignoreRecord: true)
                            ->dehydrateStateUsing(fn ($state) => mb_strtolower($state))
                            ->required()
                            ->maxLength(255),
                    ]),
                    Toggle::make('status')
                        ->default(1)
                        ->required(),
                ]),
                Section::make('Owner')->model(Organizer::class)->schema([
                    Split::make([
                        TextInput::make('owner_name')
                            ->required()
                            ->maxLength(255)
                            ->regex('/^[\pL\s\-]+$/u'),
                        TextInput::make('type')
                            ->readOnly()
                            ->default('owner')
                            ->maxLength(255),
                        TextInput::make('owner_email')
                            ->email()
                            ->unique(column: 'email')
                            ->dehydrateStateUsing(fn ($state) => mb_strtolower($state))
                            ->required()
                            ->maxLength(255),
                        Select::make('locale')
                            ->options(['ar', 'en'])
                            ->default('ar')
                            ->required()
                    ]),
                    Split::make([
                        TextInput::make('password')
                            ->password()
                            ->confirmed()
                            ->required(),
                        TextInput::make('password_confirmation')
                            ->password()
                            ->required()
                            ->maxLength(255)
                            ->same('password')
                            ->label('Confirm Password'),
                        TextInput::make('mobile')
                            ->required()
                            ->unique()
                            ->maxLength(15),
                        Select::make('country_id')
                            ->relationship('country', 'code')
                            ->searchable()
                            ->preload(),
                    ]),
                    Toggle::make('owner_status')
                        ->default(1)
                        ->required(),
                ])->hiddenOn(['edit', 'view'])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->sortable(),
                TextColumn::make('name')->searchable()->sortable(),
                TextColumn::make('email')->sortable(),
                TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(fn ($state) => $state ? 'Active' : 'Not Active')
                    ->color(fn (string $state): string => $state ? 'success' : 'danger'),
                TextColumn::make('owner.name')->sortable(),
            ])->defaultSort(
                'created_at',
                'desc'
            )->filters([])->actions([
                Tables\Actions\EditAction::make(),
            ])->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            OrganizersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrganizations::route('/'),
            'create' => Pages\CreateOrganization::route('/create'),
            'edit' => Pages\EditOrganization::route('/{record}/edit'),
            'view' => Pages\ViewOrganization::route('/{record}'),
        ];
    }
}
