<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\TestimonyResource\Pages;

use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Modules\Admin\Filament\Resources\TestimonyResource;

final class ListTestimonies extends ListRecords
{
    protected static string $resource = TestimonyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
