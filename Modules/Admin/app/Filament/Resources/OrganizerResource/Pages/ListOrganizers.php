<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\OrganizerResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Admin\Filament\Resources\OrganizerResource;

final class ListOrganizers extends ListRecords
{
    protected static string $resource = OrganizerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
