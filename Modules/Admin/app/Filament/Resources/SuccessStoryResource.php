<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources;

use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Modules\Admin\Entities\Category;
use Modules\Admin\Entities\SuccessStory;
use Modules\Admin\Enums\EventSize;
use Modules\Admin\Filament\Resources\SuccessStoryResource\Pages;

final class SuccessStoryResource extends Resource
{
    protected static ?string $model = SuccessStory::class;

    protected static ?string $navigationIcon = 'heroicon-s-sparkles';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('company_name_en')
                    ->label('English Company Name')
                    ->minLength(3)
                    ->required(),

                TextInput::make('company_name_ar')
                    ->label('Arabic Company Name')
                    ->minLength(3)
                    ->required(),

                FileUpload::make('logo')
                    ->disk('s3')
                    ->image()
                    ->maxSize(2048)
                    ->required()
                    ->directory('stories/logos')
                    ->visibility('public'),

                Select::make('category_id')
                    ->label('Category')
                    ->options(Category::all()->pluck('name_en', 'id'))
                    ->searchable()
                    ->required(),

                TextInput::make('preview_en')
                    ->maxLength(150)
                    ->minLength(3)
                    ->label('English Preview')
                    ->required(),

                TextInput::make('preview_ar')
                    ->maxLength(300)
                    ->minLength(3)
                    ->label('Arabic Preview')
                    ->required(),

                RichEditor::make('content_en')
                    ->minLength(3)
                    ->label('English Content')
                    ->required(),

                RichEditor::make('content_ar')
                    ->minLength(3)
                    ->label('Arabic Content')
                    ->required(),

                TextInput::make('event_type_en')
                    ->label('English Event Type')
                    ->required(),

                TextInput::make('event_type_ar')
                    ->label('Arabic Event Type')
                    ->required(),

                Section::make('Statistics')
                    ->schema([
                        Repeater::make('statistics')
                            ->schema([
                                TextInput::make('title_en')
                                    ->label('English Title')
                                    ->required(),
                                TextInput::make('title_ar')
                                    ->label('Arabic Title')
                                    ->required(),
                                TextInput::make('count')
                                    ->label('Number')
                                    ->numeric()
                                    ->required(),
                            ])
                            ->columns(3)
                            ->required()
                            ->reorderable(),
                    ]),

                TextInput::make('company_website')
                    ->url()
                    ->nullable(),

                Select::make('event_size')
                    ->required()
                    ->options([
                        EventSize::Small->value => 'Small (less than 100 attendees)',
                        EventSize::Medium->value => 'Medium (less than 1000 attendees)',
                        EventSize::Large->value => 'Large (over 1000 attendees)',
                    ]),

                FileUpload::make('images')
                    ->disk('s3')
                    ->image()
                    ->maxSize(2048)
                    ->minFiles(2)
                    ->multiple()
                    ->directory('stories/images')
                    ->visibility('public'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('company_name_en')
                    ->label('English Company Name')
                    ->searchable(),

                TextColumn::make('company_name_ar')
                    ->label('Arabic Company Name')
                    ->searchable(),

                TextColumn::make('category.name_en')
                    ->label('Category'),

                TextColumn::make('event_size')
                    ->label('Event Size'),

                TextColumn::make('event_type_en')
                    ->label('Event Type'),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSuccessStories::route('/'),
            'create' => Pages\CreateSuccessStory::route('/create'),
            'edit' => Pages\EditSuccessStory::route('/{record}/edit'),
        ];
    }
}
