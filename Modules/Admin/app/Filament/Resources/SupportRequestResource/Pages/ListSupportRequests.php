<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\SupportRequestResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Admin\Filament\Resources\SupportRequestResource;

final class ListSupportRequests extends ListRecords
{
    protected static string $resource = SupportRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
