<?php

declare(strict_types=1);

namespace Modules\Admin\Filament\Resources\SupportRequestResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Admin\Filament\Resources\SupportRequestResource;

final class EditSupportRequest extends EditRecord
{
    protected static string $resource = SupportRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
