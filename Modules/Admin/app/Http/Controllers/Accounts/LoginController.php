<?php

declare(strict_types=1);

namespace Modules\Admin\Http\Controllers\Accounts;

use App\Http\Controllers\Controller;
use Illuminate\Http\Client\HttpClientException;
use Illuminate\Http\JsonResponse;
use Modules\Admin\Actions\Accounts\LoginAction;
use Modules\Admin\DataTransferObjects\LoginData;
use Modules\Admin\Http\Requests\Accounts\LoginRequest;
use Modules\Admin\Transformers\LoginResource;

final class LoginController extends Controller
{
    /**
     * Display a listing of the resource.
     * @throws HttpClientException
     */
    public function __invoke(LoginRequest $request): JsonResponse
    {
        $data = LoginData::from($request->validated());

        $admin = app(LoginAction::class)($data);

        return sendSuccessResponse(__('auth.success_login'), LoginResource::make($admin));
    }
}
