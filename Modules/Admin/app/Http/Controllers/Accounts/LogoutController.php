<?php

declare(strict_types=1);

namespace Modules\Admin\Http\Controllers\Accounts;

use App\Http\Controllers\Controller;
use Illuminate\Http\Client\HttpClientException;
use Illuminate\Http\JsonResponse;
use Modules\Admin\Actions\Accounts\LogoutAction;

final class LogoutController extends Controller
{
    /**
     * Display a listing of the resource.
     * @throws HttpClientException
     */
    public function __invoke(): JsonResponse
    {
        app(LogoutAction::class)();

        return sendSuccessResponse(__('auth.logout'));
    }
}
