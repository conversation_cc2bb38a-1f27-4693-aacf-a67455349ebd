<?php

declare(strict_types=1);

namespace Modules\Admin\Http\Requests\Accounts;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

final class LoginRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email', Rule::exists('admins', 'email')],
            'password' => ['required'],
            'remember_me' => ['required', 'boolean'],
        ];
    }
}
