<?php

declare(strict_types=1);

namespace Modules\Admin\Actions\Accounts;

use App\Exceptions\LogicalException;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Facades\Auth;
use Modules\Admin\DataTransferObjects\LoginData;

final class LoginAction
{
    /**
     * Login Action.
     */
    public function __invoke(LoginData $data): Authenticatable|null
    {
        Auth::shouldUse(config('auth.admins_login_guard_name'));

        if ( ! Auth::attempt(['email' => $data->email, 'password' => $data->password])) {
            throw new LogicalException(__('auth.failed'), 403);
        }

        $admin = Auth::user();

        $expiredToken = $data->remember_me ? now()->addWeek() : now()->addDay();

        $admin->token = $admin?->createToken('Dashboard Token', expiresAt: $expiredToken)->plainTextToken;

        return $admin;
    }
}
