<?php

declare(strict_types=1);

namespace Modules\Admin\Entities;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Modules\Admin\Database\Factories\AdminFactory;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

/**
 * @property string $name
 * @property string $locale
 * @property string $password
 */
final class Admin extends Authenticatable implements FilamentUser
{
    use HasApiTokens;
    use HasFactory;
    use Snowflakes;

    /**
     * @var mixed|null
     */
    public mixed $token;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'email',
        'mobile',
        'status',
        'locale',
        'password',
    ];

    public function canAccessPanel(Panel $panel): bool
    {
        return true;
    }

    protected static function newFactory(): AdminFactory
    {
        return AdminFactory::new();
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'status' => 'boolean',
        ];
    }


}
