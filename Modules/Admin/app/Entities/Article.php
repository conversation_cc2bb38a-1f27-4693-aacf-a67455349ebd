<?php

declare(strict_types=1);

namespace Modules\Admin\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Admin\Enums\ArticleStatus;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Article extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title_en',
        'title_ar',
        'content_en',
        'content_ar',
        'preview_en',
        'preview_ar',
        'status',
        'topic_id',
        'image',
        'published_at',
        'meta_description_en',
        'meta_description_ar',
        'keywords_en',
        'keywords_ar',
        'meta_title_en',
        'meta_title_ar',
    ];

    public function topic(): BelongsTo
    {
        return $this->belongsTo(Topic::class);
    }

    public function publish(): bool
    {
        return $this->update([
            'status' => ArticleStatus::PUBLISHED->value,
            'published_at' => now(),
        ]);
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'topic_id' => SnowflakeCast::class,
            'status' => ArticleStatus::class,

        ];
    }
}
