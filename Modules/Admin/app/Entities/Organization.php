<?php

declare(strict_types=1);

namespace Modules\Admin\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Admin\Database\Factories\OrganizationFactory;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Organization extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'email',
        'status',
        'owner_id',
    ];

    public function owner(): BelongsTo
    {
        return $this->belongsTo(
            Organizer::class,
            'owner_id'
        );
    }

    public function organizers(): HasMany
    {
        return $this->hasMany(Organizer::class);
    }

    protected static function newFactory(): OrganizationFactory
    {
        return OrganizationFactory::new();
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'owner_id' => SnowflakeCast::class,
            'status' => 'boolean'
        ];
    }
}
