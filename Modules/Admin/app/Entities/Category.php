<?php

declare(strict_types=1);

namespace Modules\Admin\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Category extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name_ar',
        'name_en',
    ];

    public function successStories(): HasMany
    {
        return $this->hasMany(SuccessStory::class);
    }


    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
        ];
    }
}
