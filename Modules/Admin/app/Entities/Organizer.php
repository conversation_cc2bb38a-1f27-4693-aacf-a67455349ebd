<?php

declare(strict_types=1);

namespace Modules\Admin\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Snowflake\Snowflakes;

final class Organizer extends Model
{
    use Snowflakes;

    protected $fillable = [
        'name',
        'type',
        'email',
        'mobile',
        'locale',
        'status',
        'password',
        'organization_id',
        'country_id',
    ];

    public function country(): BelongsTo
    {
        return $this->belongsTo(
            Country::class
        );
    }


    public function organization(): BelongsTo
    {
        return $this->belongsTo(
            Organization::class
        );
    }
}
