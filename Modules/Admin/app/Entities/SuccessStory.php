<?php

declare(strict_types=1);

namespace Modules\Admin\Entities;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class SuccessStory extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'company_name_en',
        'company_name_ar',
        'logo',
        'preview_en',
        'preview_ar',
        'company_website',
        'content_en',
        'content_ar',
        'event_size',
        'event_type_en',
        'event_type_ar',
        'category_id',
        'statistics',
        'images',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'statistics' => 'array',
            'images' => 'array',
        ];
    }

    protected function imagesUrl(): Attribute
    {
        return Attribute::make(
            get: fn (): array => array_map(fn ($image) => Storage::url($image), $this->images)
        );
    }
}
