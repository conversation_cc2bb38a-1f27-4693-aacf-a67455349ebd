<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Modules\Admin\Http\Controllers\Accounts\LoginController;
use Modules\Admin\Http\Controllers\Accounts\LogoutController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::name('admins.')->prefix('admins')->group(function (): void {
    Route::name('accounts.')->prefix('accounts')->group(function (): void {
        Route::post(
            'login',
            LoginController::class
        )->name('login');

        Route::middleware(['auth:admin-api'])->group(function (): void {
            Route::post('logout', LogoutController::class)->name('logout');
        });
    });
});
