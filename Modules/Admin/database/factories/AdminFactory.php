<?php

declare(strict_types=1);

namespace Modules\Admin\Database\Factories;

use App\Utils\PasswordGenerator;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Modules\Admin\Entities\Admin;

final class AdminFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Admin::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->safeEmail(),
            'mobile' => fake()->phoneNumber(),
            'status' => fake()->boolean,
            'email_verified_at' => now(),
            'password' => PasswordGenerator::make()->default()->hashed()->generate(),
            'remember_token' => Str::random(10),
        ];
    }
}
