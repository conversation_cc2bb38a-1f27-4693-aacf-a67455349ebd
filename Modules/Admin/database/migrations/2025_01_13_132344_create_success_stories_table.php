<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('success_stories', function (Blueprint $table): void {
            $table->snowflake()->primary();
            $table->string('company_name_en');
            $table->string('company_name_ar');
            $table->string('logo');
            $table->string('preview_en', 300);
            $table->string('preview_ar', 300);
            $table->string('company_website')->nullable();
            $table->text('content_en');
            $table->text('content_ar');
            $table->string('event_size');
            $table->string('event_type_en');
            $table->string('event_type_ar');
            $table->json('statistics');
            $table->json('images');

            $table->foreignSnowflake('category_id')->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('success_stories');
    }
};
