<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organizations', static function (Blueprint $table): void {
            $table->snowflake()->primary();

            $table->string('name');
            $table->string('email')->unique();

            $table->boolean('status')->default(true);

            $table->foreignSnowflake('owner_id')->index()->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organizers');
        Schema::dropIfExists('events');
        Schema::dropIfExists('organizations');
    }
};
