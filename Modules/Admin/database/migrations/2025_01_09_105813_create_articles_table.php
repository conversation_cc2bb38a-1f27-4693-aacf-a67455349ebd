<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Admin\Enums\ArticleStatus;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('articles', function (Blueprint $table): void {
            $table->id();
            $table->string('title_en', 300);
            $table->string('title_ar', 300);
            $table->string('image');
            $table->text('content_en');
            $table->text('content_ar');
            $table->string('status')->default(ArticleStatus::DRAFT->value);
            $table->string('keywords_ar');
            $table->string('keywords_en');
            $table->string('meta_description_en');
            $table->string('meta_description_ar');

            $table->timestamp('published_at')->nullable();
            $table->foreignSnowflake('topic_id')->index();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('articles');
    }
};
