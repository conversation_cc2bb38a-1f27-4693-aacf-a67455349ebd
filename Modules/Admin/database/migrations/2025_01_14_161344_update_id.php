<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('topics', function (Blueprint $table): void {
            $table->dropPrimary();
            $table->snowflake()->primary()->change();
        });

        Schema::table('articles', function (Blueprint $table): void {
            $table->dropPrimary();
            $table->snowflake()->primary()->change();
        });

        Schema::table('industries', function (Blueprint $table): void {
            $table->dropPrimary();
            $table->snowflake()->primary()->change();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('topics', function (Blueprint $table): void {
            $table->id()->change();
        });

        Schema::table('articles', function (Blueprint $table): void {
            $table->id()->change();
        });

        Schema::table('industries', function (Blueprint $table): void {
            $table->id()->change();
        });
    }
};
