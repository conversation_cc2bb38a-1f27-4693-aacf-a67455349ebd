<?php

declare(strict_types=1);

namespace Modules\Admin\Database\Seeders;

use App\Utils\PasswordGenerator;
use Illuminate\Database\Seeder;
use Modules\Admin\Entities\Admin;

final class DefaultAdminAccountsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $accounts = [
            [
                'name' => 'Ammar',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Abdulaziz',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Bin Metan',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Omer Baflah',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Othman',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Bahomoddah',
                'email' => '<EMAIL>',
            ],
            [
                'name' => '<PERSON><PERSON>',
                'email' => 'moham<PERSON><PERSON>@admin.app',
            ],
            [
                'name' => '<PERSON><PERSON><PERSON>',
                'email' => 'abdulk<PERSON><EMAIL>',
            ],
            [
                'name' => 'Ali',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Ammar Khaled',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Bakry',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'noor',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Fatima Alhamed',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Maria',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Raghad',
                'email' => '<EMAIL>',
            ],
        ];

        foreach ($accounts as $key => $value) {
            Admin::factory()->create([
                'name' => $value['name'],
                'email' => $value['email'],
                'status' => true,
                'password' => PasswordGenerator::make()->default()->hashed()->generate(),
            ]);
        }
    }
}
