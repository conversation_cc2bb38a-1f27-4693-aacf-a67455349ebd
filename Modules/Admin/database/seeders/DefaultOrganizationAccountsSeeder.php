<?php

declare(strict_types=1);

namespace Modules\Admin\Database\Seeders;

use App\Utils\PasswordGenerator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Modules\Admin\Entities\Organization;
use Modules\Admin\Entities\Organizer;

final class DefaultOrganizationAccountsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $accounts = [
            [
                'name' => 'Ammar',
                'email' => '<EMAIL>',
            ],
            [
                'name' => '<PERSON><PERSON><PERSON> Mrzoog',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Bin Metan',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Omer Baflah',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Othman',
                'email' => '<EMAIL>',
            ],
            [
                'name' => '<PERSON>',
                'email' => 'moham<PERSON><PERSON><PERSON><PERSON><PERSON>@bootfi.com',
            ],
            [
                'name' => '<PERSON> al<PERSON>',
                'email' => 'moham<PERSON><PERSON>@bootfi.com',
            ],
            [
                'name' => '<PERSON>karim',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Ali Hesham',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Mohammed Bamatraf',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Ammar Khaled',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Bakry',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Naif Aldira',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'noor',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Fatima Alhamed',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Maria',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Raghad Ali',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Abdullahi Addow',
                'email' => '<EMAIL>',
            ],
        ];

        Model::unguard();

        Organization::query()->truncate();
        Organizer::query()->truncate();

        foreach ($accounts as $key => $value) {
            $organization = Organization::query()->create([
                'name' => $value['name'],
                'email' => $value['email'],
                'status' => true,
            ]);

            $organizer = Organizer::query()->create([
                'type' => 'owner',
                'name' => $value['name'],
                'email' => $value['email'],
                'status' => true,
                'email_verified_at' => now(),
                'password' => PasswordGenerator::make()->default()->hashed()->generate(),
                'remember_token' => Str::random(10),
                'organization_id' => $organization->id,
            ]);

            $organization->update([
                'owner_id' => $organizer->id,
            ]);
        }
    }
}
