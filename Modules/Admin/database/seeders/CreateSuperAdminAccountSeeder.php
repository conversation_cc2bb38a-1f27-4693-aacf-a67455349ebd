<?php

declare(strict_types=1);

namespace Modules\Admin\Database\Seeders;

use App\Utils\PasswordGenerator;
use Illuminate\Database\Seeder;
use Modules\Admin\Entities\Admin;

final class CreateSuperAdminAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (Admin::query()->where('email', '=', '<EMAIL>')->doesntExist()) {
            Admin::query()->create([
                'name' => 'EventApp Admin',
                'email' => '<EMAIL>',
                'mobile' => '*********',
                'status' => true,
                'is_super_admin' => true,
                'password' => PasswordGenerator::make()->default()->hashed()->generate(),
            ]);
        }
    }
}
