<?php

declare(strict_types=1);

return [
    'forbidden' => 'You are not authorized to do this action',
    'user' => [
        'attendee' => [
            'already_exists' => 'This user has already been added to this event',
            'invalid_attendee_access' => 'You cannot view the information of the invitees to this event',
            'attendee_not_checked_in_exception' => 'You have to check into the event to view the other attendees',
        ],
    ],
    'settings_not_found' => 'Settings not found',
    'agenda' => [
        'speaker_not_found' => 'Uninvited speakers cannot be added to this agenda',
        'supervisor_not_found' => 'Uninvited supervisors cannot be added to this agenda',
        'not_upcoming' => 'Agenda is not upcoming',
        'ended' => 'This session has ended',
    ],
    'invalid_ending_time' => 'The ending time must be after the starting time.',
    'start_time_can_not_updated' => 'The start time for a current event cannot be updated.',
    'sessions' => [
        'pending_invitation' => 'You have to check into the event before joining',
        'ended' => 'This session has ended',
        'not_started' => 'This session has not started yet.',
        'full_seats' => 'There are no more available seats',
        'already_in_session' => 'You are already in this session',
    ],

    'checked_in' => [
        'already_check_in' => 'You have already checked into this event',
    ],
    'engagement' => [
        'poll' => [
            'options_less_than_two' => 'You should enter at least two answers',
            'pending_invitation' => 'You have to check into the event to show engagement',
        ],
        'speaker_not_allowed' => 'You are not authorized to perform this action',
        'engagement_is_published' => 'You cannot edit a published engagement',
        'engagement_is_not_published' => 'The engagement has not been published yet.',
        'engagement_time_is_not_finish' => 'You cannot view the results of an engagement that is not ended',
        'engagement_time_is_finish' => 'time is finished',
        'question_has_ended' => 'Engagement question time has ended',
        'already_answered' => 'You have already answered this engagement',
        'no_option' => 'the option in not fond',
        'not_allow_to_accept' => 'You cannot approve of an approved question',
        'not_allow_to_delete' => 'You cannot delete an approved question',
        'like_already_exists' => 'You have already liked this question',
        'like_not_exists' => 'You have not liked this question',
        'cannot_delete_published_engagement' => 'You cannot delete a published engagement',
        'terminate_engagement_is_not_published' => 'You cannot end an unpublished engagement',
        'engagement_has_ended' => 'You cannot delete an ended engagement',
    ],
    'memories' => [
        'not_checked_in' => 'You have to check into the event to view the memories',
        'not_allowed_to_delete' => 'You are not authorized to delete this memory',
    ],
    'material' => [
        'cannot_delete_published_material' => 'You cannot delete an unpublished material',
    ],
    'announcement' => [
        'invalid_send_method_for_in_app' => 'The event is in-app, so the send method should be app.',
        'invalid_send_method_for_out_app' => 'The event is out-app, so the send method should be mail, whatsapp or both.',
        'outside_allowed_window' => 'You cannot send this notification now. Allowed time is from 24h before the event to 48h after it ends',
    ],
    'settings' => [
        'private_event_settings' => 'Template settings are only available for public events',
        'public_event_settings_template' => 'Template settings are required for public events',
        'public_event_ticket_price' => 'Public events are free and cannot set a ticket price',
    ],
];
