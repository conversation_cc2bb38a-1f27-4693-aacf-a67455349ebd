<?php

declare(strict_types=1);

namespace Modules\Event\Communicators;

use Facades\Modules\User\Facades\UserModule;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Modules\Event\Contracts\UserCommunicatorContract;
use Modules\Event\DataTransferObjects\Attendees\ImportAttendeeData;
use Modules\Event\DataTransferObjects\Attendees\IndexAttendeeData;
use Modules\Event\DataTransferObjects\Attendees\StoreAttendeeData;

final class UserCommunicator implements UserCommunicatorContract
{
    public static function storeAttendee(StoreAttendeeData $data): Model
    {
        return UserModule::storeAttendee($data->toArray());
    }

    public static function indexAttendee(IndexAttendeeData $data): LengthAwarePaginator
    {
        return UserModule::indexAttendee($data->toArray());
    }

    public static function importAttendee(ImportAttendeeData $data): void
    {
        UserModule::importAttendee($data->toArray());
    }

    public static function getAgendaSpeakers(array $speakersIds): Collection
    {
        return UserModule::getAgendaSpeakers($speakersIds);
    }

    public static function deleteAttendee(int $id, int $user_id): void
    {
        UserModule::deleteAttendee($id, $user_id);
    }
}
