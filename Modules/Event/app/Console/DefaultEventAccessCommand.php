<?php

declare(strict_types=1);

namespace Modules\Event\Console;

use Illuminate\Console\Command;
use Modules\Event\Entities\Organizer;

final class DefaultEventAccessCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'event:access';

    /**
     * The console command description.
     */
    protected $description = 'Add default event access for each organizer';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        Organizer::get()->each(function ($organizer): void {
            $events = $organizer->events()->pluck('events.id')->toArray();
            $organizer->eventAccess()->syncWithoutDetaching($events);
        });

        $this->info('Event access has been successfully updated for all organizers.');
    }

}
