<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Platform\Events\Wizard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class EventWizardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'description_ar' => $this->description_ar,
            'description_en' => $this->description_en,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'location' => $this->location,
            'logo' => $this->logo,
            'banner' => $this->banner,
            'primary_color' => $this->primary_color,
            'secondary_color' => $this->secondary_color,
            'ticket_price' => $this->ticket_price,
            'template_id' => $this->template_id,
            'badge_id' => $this->badge_id,
            'attendee_badge_color' => $this->attendee_badge_color,
            'speaker_badge_color' => $this->speaker_badge_color,
            'supervisor_badge_color' => $this->supervisor_badge_color,
            'invitation_method' => $this->invitation_method,
            'out_app_communication_method' => $this->out_app_communication_method,
        ];
    }
}
