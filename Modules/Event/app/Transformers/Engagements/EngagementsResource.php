<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Engagements;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Enums\EngagementType;

final class EngagementsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'all_count' => $this->engagements_count,
            'poll_count' => $this->polls_count,
            'questions_count' => $this->questions_count,
            'poll' => EngagementResource::collection($this->engagements?->where('engagement_type', EngagementType::POLL->value) ?? []),
            'questions' => EngagementResource::collection($this->engagements?->where('engagement_type', EngagementType::QUESTIONS->value) ?? []),
        ];
    }
}
