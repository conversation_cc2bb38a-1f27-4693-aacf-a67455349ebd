<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Engagements\Questions\Responses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class IndexQuestionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'title' => $this->title,
            'unaccepted_questions_count' => $this->unaccepted_questions_count,
            'accepted_questions_count' => $this->accepted_questions_count,
            'status' => $this->status,
            'data' => IndexQuestionsResource::collection($this->responses),
            'is_stopped' => $this->is_stopped,
        ];
    }
}
