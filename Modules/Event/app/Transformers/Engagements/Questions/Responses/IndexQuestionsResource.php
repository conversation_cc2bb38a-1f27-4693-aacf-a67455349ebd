<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Engagements\Questions\Responses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\Users\UserResource;

final class IndexQuestionsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'count' => $this->count,
            'before' => $this->updated_at,
            'user' => UserResource::make($this->createdBy),
        ];
    }
}
