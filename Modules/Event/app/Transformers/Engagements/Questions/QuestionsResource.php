<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Engagements\Questions;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class QuestionsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'duration' => $this->duration,
            'type' => $this->engagement_type,
        ];
    }
}
