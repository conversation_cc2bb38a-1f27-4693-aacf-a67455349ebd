<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Engagements;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Enums\PollType;
use Modules\Event\Transformers\Engagements\Polls\OptionsResource;

final class EngagementResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'duration' => $this->duration,
            'is_published' => $this->is_published,
            'engagement_type' => $this->engagement_type,
            'poll_type' => $this->poll_type,
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'status' => $this->translate_status,
            'type' => $this->type,
            'options' => $this->when($this->poll_type === PollType::OPTIONS->value, OptionsResource::collection($this->responses)),
        ];
    }
}
