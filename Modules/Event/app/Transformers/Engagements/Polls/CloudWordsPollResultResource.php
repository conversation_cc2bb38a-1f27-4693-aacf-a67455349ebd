<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Engagements\Polls;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class CloudWordsPollResultResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'total' => count($this->responses),
            'result' => CloudWordsResource::collection($this->responses),

        ];
    }
}
