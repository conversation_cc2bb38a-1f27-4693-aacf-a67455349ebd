<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Engagements\Polls;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Enums\PollType;

final class PollResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'duration' => $this->duration,
            'type' => $this->poll_type,
            'status' => $this->status,
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'is_answered' => $this->is_answered,
            'options' => $this->when($this->poll_type === PollType::OPTIONS->value, OptionsResource::collection($this->responses)),
        ];
    }
}
