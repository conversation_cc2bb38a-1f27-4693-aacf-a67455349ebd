<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Engagements\Polls;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class OptionResultResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'count' => $this->count,
            'percentage' => $this->getPercentage($this->total),
        ];
    }
}
