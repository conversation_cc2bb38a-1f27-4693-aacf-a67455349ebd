<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Materials;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class MediaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->file_name,
            'url' => $this->getUrl(),
            'type' => $this->extension,
            'size' => $this->human_readable_size,
        ];
    }
}
