<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Materials;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class MaterialsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'is_published' => $this->is_published,
            'media' => MediaResource::make($this->mediaCollection())
        ];
    }
}
