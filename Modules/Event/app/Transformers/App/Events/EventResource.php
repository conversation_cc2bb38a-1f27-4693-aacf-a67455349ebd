<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\App\Events;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\App\Events\Agenda\AgendasResource;
use Modules\Event\Transformers\App\Events\Attendees\AttendeesResource;
use Modules\Event\Transformers\App\Events\Speakers\SpeakersResource;
use Modules\Event\Transformers\App\Events\Supervisors\SupervisorsResource;

final class EventResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'check_in_type' => $this->check_in_type,
            'logo' => $this->settings?->logo,
            'location' => $this->settings?->location,
            'attendee_badge_color' => $this->attendee_badge_color,
            'speaker_badge_color' => $this->speaker_badge_color,
            'supervisor_badge_color' => $this->supervisor_badge_color,
            'attendees_count' => $this->attendees_count,
            'badge_id' => $this->settings?->badge_id,
            'agendas' => AgendasResource::collection($this->agendas),
            'speakers' => SpeakersResource::collection($this->invitedSpeakers),
            'supervisors' => SupervisorsResource::collection($this->invitedSupervisors),
            'attendees' => AttendeesResource::collection($this->invitedAttendees),
            'user' => UserInvitationDetailsResource::make($this->invitations->first()),
        ];
    }
}
