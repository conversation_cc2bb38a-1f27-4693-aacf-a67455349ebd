<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\App\Events;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\App\Events\Agenda\AgendasResource;

final class SpeakerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'profile' => ProfileResource::make($this->profile),
            'agendas' => AgendasResource::collection($this->agendas),
        ];
    }
}
