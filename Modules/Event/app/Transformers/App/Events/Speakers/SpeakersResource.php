<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\App\Events\Speakers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class SpeakersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'gender' => $this->profile?->gender,
            'job' => $this->profile?->job,
            'bio' => $this->profile?->bio,
            'company' => $this->profile?->company,
            'picture' => $this->profile?->picture,
        ];
    }
}
