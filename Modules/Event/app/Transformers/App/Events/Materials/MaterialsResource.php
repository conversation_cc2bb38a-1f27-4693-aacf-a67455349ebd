<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\App\Events\Materials;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class MaterialsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'file_link' => $this->file_url,
            'file_format' => $this->file_extension,
        ];
    }
}
