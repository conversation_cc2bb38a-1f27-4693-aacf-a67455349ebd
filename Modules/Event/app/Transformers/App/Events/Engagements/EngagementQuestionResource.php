<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\App\Events\Engagements;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class EngagementQuestionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'duration' => $this->duration,
            'engagement_type' => $this->engagement_type,
            'poll_type' => $this->poll_type,
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
