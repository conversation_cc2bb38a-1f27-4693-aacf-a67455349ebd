<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\App\Events\Engagements\Responses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\Users\UserResource;

final class EngagementResponsesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'likes_count' => $this->count,
            'is_liked' => $this->is_liked,
            'created_at' => $this->created_at,
            'created_by' => UserResource::make($this->createdBy),
        ];
    }
}
