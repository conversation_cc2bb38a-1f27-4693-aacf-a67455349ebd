<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\App\Events;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class AgendasResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'color' => $this->color,
            'location' => $this->location,
            'description' => $this->description,
            'status' => $this->app_status,
            'date' => $this->formattedDate,
            'start_at' => $this->formattedStartAt,
            'end_at' => $this->formattedEndAt,
            'seats_number' => $this->seats_number,
            'is_seats_limited' => $this->is_seats_limited,
        ];
    }
}
