<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\App\Events;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class EventsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'start_date' => $this->start_at,
            'end_date' => $this->end_at,
            'main_color' => $this->settings?->primary_color,
            'secondary_color' => $this->settings?->secondary_color,
            'logo' => $this->settings->logo,
            'can_see_attendee' => $this->can_see_attendee,
            'can_have_speaker' => $this->can_have_speaker,
            'can_have_supervisor' => $this->can_have_supervisor,
            'last_check_in_at' => $this->checkIns->last()?->checked_in_at,
            'has_materials' => $this->has_materials,
            'allow_chatting' => $this->allow_chatting,
            'is_active' => $this->invitations->first()->is_active,
        ];
    }
}
