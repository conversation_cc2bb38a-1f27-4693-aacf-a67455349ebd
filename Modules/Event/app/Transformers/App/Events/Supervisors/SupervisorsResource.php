<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\App\Events\Supervisors;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class SupervisorsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'job' => $this->profile?->job,
            'gender' => $this->profile?->gender,
            'bio' => $this->profile?->bio,
            'company' => $this->profile?->company,
            'picture' => $this->profile?->picture,
        ];
    }
}
