<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\App\Events\Agenda;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\App\Events\Agenda\Speakers\SpeakersResource;
use Modules\Event\Transformers\App\Events\Supervisors\SupervisorsResource;

final class AgendasResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'date' => now()->toDateString(),//TODO: Delete it
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'location' => $this->location,
            'is_seats_limited' => $this->is_seats_limited,
            'seats_number' => $this->seats_number ?? 0,
            'seats_available' => $this->seats_available,
            'status' => $this->app_status,
            'is_joined' => $this->is_joined,
            'speakers' => SpeakersResource::collection($this->whenLoaded('speakers')),
            'supervisors' => SupervisorsResource::collection($this->whenLoaded('supervisors')),
        ];
    }
}
