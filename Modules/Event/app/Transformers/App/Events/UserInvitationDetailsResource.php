<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\App\Events;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\Tags\TagResource;

final class UserInvitationDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->user->id,
            'name' => $this->user->name,
            'type' => $this->type,
            'tag' => TagResource::make($this->whenLoaded('tag')),
            'attendance_code' => $this->attendance_code,
        ];
    }
}
