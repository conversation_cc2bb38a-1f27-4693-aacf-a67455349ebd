<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Events;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\Categories\CategoryResource;

final class EventResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name_ar' => $this->name_ar,
            'name_en' => $this->name_en,
            'type' => $this->type,
            'status' => $this->translated_status,
            'published' => $this->published,
            'check_in_type' => $this->check_in_type,
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'category' => CategoryResource::make($this->category),
            'has_settings' => $this->has_settings,
            'event_accessibility' => [
                'can_see_attendee' => $this->can_see_attendee,
                'can_have_speaker' => $this->can_have_speaker,
                'can_have_supervisor' => $this->can_have_supervisor,
                'allow_chatting' => $this->allow_chatting,
            ],
        ];
    }
}
