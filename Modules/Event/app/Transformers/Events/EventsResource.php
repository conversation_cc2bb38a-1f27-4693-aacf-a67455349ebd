<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Events;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\Categories\CategoryResource;

final class EventsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'name_ar' => $this->name_ar,
            'name_en' => $this->name_en,
            'category' => CategoryResource::make($this->whenLoaded('category')),
            'status' => $this->translated_status,
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'domain' => $this->domain,
            'check_in_type' => $this->check_in_type,
            'has_settings' => $this->has_settings,
        ];
    }
}
