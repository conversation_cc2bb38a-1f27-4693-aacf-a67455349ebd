<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Events\Statistics\General;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class SpeakerStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'name' => $this->name,
            'picture' => $this->profile?->picture,
            'total_sessions' => $this->total_sessions ?? 0,
        ];
    }
}
