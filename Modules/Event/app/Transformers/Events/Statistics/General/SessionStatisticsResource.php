<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Events\Statistics\General;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class SessionStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'title' => $this->title,
            'total_attendees' => $this->total_attendees ?? 0,
            'total_engagements' => $this->total_engagements ?? 0,
        ];
    }
}
