<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Events\Statistics\General;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class AttendeeStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'total_attendees' => $this->total_attendees ?? 0,
            'total_checked_in_attendees' => $this->total_checked_in_attendees ?? 0,
            'total_absent_attendees' => $this->total_absent_attendees ?? 0,
        ];
    }
}
