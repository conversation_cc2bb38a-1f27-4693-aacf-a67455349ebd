<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Memories;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\Users\UserResource;

final class MemoriesResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'image_url' => $this->image_url,
            'created_at' => $this->created_at,
            'interactions_count' => $this->interactions_count,
            'is_reacted' => null !== $this->reaction,
            'emoji_reaction' => $this->reaction?->emoji,
            'emojis' => $this->emojis,
            'user' => UserResource::make($this->user),
        ];
    }
}
