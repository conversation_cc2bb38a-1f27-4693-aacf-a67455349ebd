<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Memories;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class MemoryResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'image_url' => $this->image_url,
            'created_at' => $this->created_at,
        ];
    }
}
