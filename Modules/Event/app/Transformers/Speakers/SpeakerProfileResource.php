<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Speakers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class SpeakerProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'bio' => $this->bio,
            'picture' => $this->picture,
            'job' => $this->job,
            'company' => $this->company,
            'social_accounts' => $this->social_accounts,

        ];
    }
}
