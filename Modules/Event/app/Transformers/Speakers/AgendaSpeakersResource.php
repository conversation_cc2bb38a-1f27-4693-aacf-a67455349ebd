<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Speakers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class AgendaSpeakersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'gender' => $this->profile?->gender,
            'picture' => $this->profile?->picture,
        ];
    }
}
