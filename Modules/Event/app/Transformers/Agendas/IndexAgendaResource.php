<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Agendas;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\App\Events\Supervisors\SupervisorsResource;
use Modules\Event\Transformers\Speakers\AgendaSpeakersResource;

final class IndexAgendaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'color' => $this->color,
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'status' => $this->translated_status,
            'is_notification_enabled' => $this->is_notification_enabled,
            'speakers' => AgendaSpeakersResource::collection($this->speakers),
            'supervisors' => SupervisorsResource::collection($this->supervisors),
        ];
    }
}
