<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Agendas;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\Speakers\AgendaSpeakersResource;
use Modules\Event\Transformers\Supervisors\AgendaSupervisorsResource;

final class AgendaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'color' => $this->color,
            'location' => $this->location,
            'description' => $this->description,
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'seats_number' => $this->seats_number,
            'is_notification_enabled' => $this->is_notification_enabled,
            'speakers' => AgendaSpeakersResource::collection($this->speakers),
            'supervisors' => AgendaSupervisorsResource::collection($this->supervisors),
        ];
    }
}
