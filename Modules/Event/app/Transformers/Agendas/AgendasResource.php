<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Agendas;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\App\Events\Supervisors\SupervisorsResource;
use Modules\Event\Transformers\Speakers\AgendaSpeakersResource;

final class AgendasResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'color' => $this->color,
            'location' => $this->location,
            'description' => $this->description,
            'status' => $this->status,
            'is_seats_limited' => $this->is_seats_limited,
            'is_joined' => $this->is_joined,
            'date' => now()->toDateString(),//TODO: Delete it
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'seats_number' => $this->seats_number,
            'seats_available' => $this->seats_available,
            'speakers' => AgendaSpeakersResource::collection($this->whenLoaded('speakers')),
            'supervisors' => SupervisorsResource::collection($this->whenLoaded('supervisors')),
        ];
    }
}
