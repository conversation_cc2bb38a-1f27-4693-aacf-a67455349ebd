<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Attendees;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use Modules\Event\Transformers\Countries\CountryResource;
use Modules\Event\Transformers\Tags\TagResource;

final class IndexAttendeeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $invitation = $this->events()->where('id', request('id'))->first()->pivot;

        return [
            'id' => $this->id,
            'name' => $this->name,
            'mobile' => $this->mobile,
            'email' => $this->email,
            'picture' => $this->picture,
            'status' => $invitation->is_active,
            'tag' => TagResource::make($invitation->tag),
            'invitation_status' => $invitation->status,
            'extra' => $invitation->extra,
            'attachment' => $invitation->attachment ? Storage::url($invitation->attachment) : null,
            'country' => CountryResource::make($this->whenLoaded('country')),
            'attendance_code' => $this->pivot?->attendance_code,
        ];
    }
}
