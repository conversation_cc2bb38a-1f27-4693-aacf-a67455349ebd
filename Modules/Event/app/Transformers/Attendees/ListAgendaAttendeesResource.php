<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Attendees;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class ListAgendaAttendeesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'seats_available' => data_get($this, 'seats_available'),
            'attendees_count' => data_get($this, 'attendees_count'),
            'attendees' => AttendeeResource::collection(data_get($this, 'attendees')),
        ];
    }
}
