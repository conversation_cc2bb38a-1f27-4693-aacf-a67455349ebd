<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Attendees;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Event\Transformers\Countries\CountryResource;
use Modules\Event\Transformers\Tags\TagResource;

final class AttendeeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'picture' => $this->profile?->picture,
            'tag' => TagResource::make($this->tag),
            'country' => CountryResource::make($this->whenLoaded('country')),
        ];
    }
}
