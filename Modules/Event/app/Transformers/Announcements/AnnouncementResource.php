<?php

declare(strict_types=1);

namespace Modules\Event\Transformers\Announcements;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class AnnouncementResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title_ar,
            'content' => $this->content_ar,
            'send_method' => $this->send_method,
            'published_at' => $this->created_at,
        ];
    }
}
