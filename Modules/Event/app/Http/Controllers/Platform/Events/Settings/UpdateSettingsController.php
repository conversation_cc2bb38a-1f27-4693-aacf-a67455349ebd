<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Platform\Events\Settings;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Platform\Events\Settings\UpdateSettingsAction;
use Modules\Event\DataTransferObjects\Platform\Events\Settings\UpdateSettingsData;
use Modules\Event\Http\Requests\Platform\Events\Settings\UpdateSettingsRequest;
use Modules\Event\Transformers\Platform\Events\Settings\EventSettingsResource;

final class UpdateSettingsController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param UpdateSettingsRequest $request
     *
     * @return JsonResponse
     */
    public function __invoke(UpdateSettingsRequest $request, int $id): JsonResponse
    {
        $settingsData = UpdateSettingsData::from($request);

        $settings = app(UpdateSettingsAction::class)($settingsData, $id);

        return sendSuccessResponse(
            message: __('messages.update_data'),
            data: EventSettingsResource::make($settings)
        );
    }
}
