<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Platform\Events\Settings;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\Platform\Events\Settings\ShowSettingsAction;
use Modules\Event\Transformers\Platform\Events\Settings\EventSettingsResource;

final class ShowSettingsController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function __invoke(Request $request, int $id): JsonResponse
    {
        $settings = app(ShowSettingsAction::class)($id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: EventSettingsResource::make($settings)
        );
    }
}
