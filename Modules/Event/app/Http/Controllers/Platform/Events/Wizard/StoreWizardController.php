<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Platform\Events\Wizard;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Platform\Events\Wizard\StoreWizardAction;
use Modules\Event\DataTransferObjects\Platform\Events\Wizard\StoreWizardData;
use Modules\Event\Http\Requests\Platform\Events\Wizard\StoreWizardRequest;
use Modules\Event\Transformers\Platform\Events\Wizard\EventWizardResource;

final class StoreWizardController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param StoreWizardRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function __invoke(StoreWizardRequest $request, int $id): JsonResponse
    {
        $data = StoreWizardData::from($request);

        $settings = app(StoreWizardAction::class)($data, $id);

        return sendSuccessResponse(
            message: __('messages.update_data'),
            data: EventWizardResource::make($settings)
        );
    }
}
