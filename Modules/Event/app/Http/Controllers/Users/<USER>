<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Users;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Users\EventUserAttendingAction;
use Modules\Event\DataTransferObjects\Users\EventUserAttendingData;
use Modules\Event\Http\Requests\Users\EventUserAttendingRequest;

final class EventUserAttendingController extends Controller
{
    public function __invoke(EventUserAttendingRequest $request): JsonResponse
    {
        $data = EventUserAttendingData::from($request->validated());
        app(EventUserAttendingAction::class)($data);

        return sendSuccessResponse(__('messages.create_data'));
    }

}
