<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Materials\Sessions;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Materials\Sessions\UpdateMaterialAction;
use Modules\Event\DataTransferObjects\Materials\Sessions\UpdateMaterialData;
use Modules\Event\Http\Requests\Materials\Sessions\UpdateMaterialRequest;
use Modules\Event\Transformers\Materials\MaterialsResource;

final class UpdateMaterialController extends Controller
{
    public function __invoke(UpdateMaterialRequest $request): JsonResponse
    {
        $data = UpdateMaterialData::from($request->validated());
        $material = app(UpdateMaterialAction::class)($data);

        return sendSuccessResponse(__('messages.update_data'), MaterialsResource::make($material));
    }
}
