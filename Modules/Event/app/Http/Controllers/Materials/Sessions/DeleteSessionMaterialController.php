<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Materials\Sessions;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Materials\Sessions\DeleteSessionMaterialAction;

final class DeleteSessionMaterialController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(int $event_id, int $session_id, int $material_id): JsonResponse
    {
        app(DeleteSessionMaterialAction::class)($event_id, $session_id, $material_id);

        return sendSuccessResponse(
            message: __('messages.delete_data')
        );
    }
}
