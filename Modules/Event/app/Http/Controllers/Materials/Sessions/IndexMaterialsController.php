<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Materials\Sessions;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Materials\Sessions\IndexMaterialsAction;
use Modules\Event\DataTransferObjects\Materials\Sessions\IndexMaterialData;
use Modules\Event\Http\Requests\Materials\Sessions\IndexMaterialRequest;
use Modules\Event\Transformers\Materials\MaterialsResource;

final class IndexMaterialsController extends Controller
{
    public function __invoke(IndexMaterialRequest $request): JsonResponse
    {
        $data = IndexMaterialData::from($request->validated());
        $materials = app(IndexMaterialsAction::class)($data);

        return sendSuccessResponse(__('messages.get_data'), MaterialsResource::collection($materials));
    }
}
