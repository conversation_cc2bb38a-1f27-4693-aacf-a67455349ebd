<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Materials\Sessions\Status;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Materials\Sessions\PublishStatus\UnpublishMaterialAction;

final class UnpublishMaterialController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(int $event_id, int $session_id, int $material_id): JsonResponse
    {
        app(UnpublishMaterialAction::class)(
            $event_id,
            $session_id,
            $material_id
        );

        return sendSuccessResponse(
            message: __('messages.update_data')
        );
    }
}
