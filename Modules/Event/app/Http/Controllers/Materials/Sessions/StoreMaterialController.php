<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Materials\Sessions;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Materials\Sessions\StoreMaterialAction;
use Modules\Event\DataTransferObjects\Materials\Sessions\StoreMaterialData;
use Modules\Event\Http\Requests\Materials\Sessions\StoreMaterialRequest;

final class StoreMaterialController extends Controller
{
    public function __invoke(StoreMaterialRequest $request): JsonResponse
    {
        $data = StoreMaterialData::from($request->validated());
        app(StoreMaterialAction::class)($data);

        return sendSuccessResponse(__('messages.create_data'));
    }
}
