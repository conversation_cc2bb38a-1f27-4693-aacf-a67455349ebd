<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Materials\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Materials\Events\IndexMaterialAction;
use Modules\Event\DataTransferObjects\Materials\Events\IndexMaterialData;
use Modules\Event\Http\Requests\Materials\Events\IndexMaterialRequest;
use Modules\Event\Transformers\Materials\MaterialsResource;

final class IndexMaterialController extends Controller
{
    public function __invoke(IndexMaterialRequest $request): JsonResponse
    {
        $data = IndexMaterialData::from($request->validated());

        $materials = app(IndexMaterialAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: MaterialsResource::collection($materials)->appends(
                $request->query()
            ),
        );
    }
}
