<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Materials\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Materials\Events\DeleteMaterialAction;

final class DeleteMaterialController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(int $event_id, int $material_id): JsonResponse
    {
        app(DeleteMaterialAction::class)(
            $event_id,
            $material_id
        );

        return sendSuccessResponse(
            message: __('messages.delete_data')
        );
    }
}
