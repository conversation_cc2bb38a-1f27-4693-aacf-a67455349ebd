<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Materials\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Materials\Events\UpdateMaterialAction;
use Modules\Event\DataTransferObjects\Materials\Events\UpdateMaterialData;
use Modules\Event\Http\Requests\Materials\Events\UpdateMaterialRequest;
use Modules\Event\Transformers\Materials\MaterialsResource;

final class UpdateMaterialController extends Controller
{
    public function __invoke(UpdateMaterialRequest $request): JsonResponse
    {
        $data = UpdateMaterialData::from($request->validated());

        $material = app(UpdateMaterialAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.update_data'),
            data: MaterialsResource::make($material),
        );
    }
}
