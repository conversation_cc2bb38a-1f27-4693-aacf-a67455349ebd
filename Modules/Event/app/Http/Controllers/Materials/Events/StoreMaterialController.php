<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Materials\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Materials\Events\StoreMaterialAction;
use Modules\Event\DataTransferObjects\Materials\Events\StoreMaterialData;
use Modules\Event\Http\Requests\Materials\Events\StoreMaterialRequest;

final class StoreMaterialController extends Controller
{
    public function __invoke(StoreMaterialRequest $request): JsonResponse
    {
        $data = StoreMaterialData::from($request->validated());

        app(StoreMaterialAction::class)($data);

        return sendSuccessResponse(
            message:__('messages.create_data'),
        );
    }
}
