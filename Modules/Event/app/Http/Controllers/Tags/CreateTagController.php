<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Tags;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Tags\CreateTagsAction;
use Modules\Event\DataTransferObjects\Tags\TagData;
use Modules\Event\Http\Requests\Tags\CreateTagRequest;
use Modules\Event\Transformers\Tags\TagResource;

final class CreateTagController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param CreateTagRequest $request
     * @param int $id
     *
     * @return JsonResponse
     */
    public function __invoke(CreateTagRequest $request, int $id): JsonResponse
    {
        $data = TagData::from($request->validated());

        $tag = app(CreateTagsAction::class)(
            $id,
            $data
        );

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: TagResource::make($tag)
        );
    }
}
