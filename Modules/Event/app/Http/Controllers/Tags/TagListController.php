<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Tags;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Tags\TagsListAction;
use Modules\Event\Transformers\Tags\TagsListResource;

final class TagListController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(int $id): JsonResponse
    {
        $tags = app(TagsListAction::class)($id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: TagsListResource::collection($tags)
        );
    }
}
