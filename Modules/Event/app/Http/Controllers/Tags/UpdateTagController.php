<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Tags;

use App\Exceptions\LogicalException;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Tags\UpdateTagsAction;
use Modules\Event\DataTransferObjects\Tags\TagData;
use Modules\Event\Http\Requests\Tags\CreateTagRequest;
use Modules\Event\Transformers\Tags\TagResource;

final class UpdateTagController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param CreateTagRequest $request
     * @param int $id
     * @param int $tag_id
     *
     * @return JsonResponse
     * @throws LogicalException
     */
    public function __invoke(CreateTagRequest $request, int $id, int $tag_id): JsonResponse
    {
        $data = TagData::from($request->validated());

        $tag = app(UpdateTagsAction::class)(
            $id,
            $tag_id,
            $data
        );

        return sendSuccessResponse(
            message: __('messages.update_data'),
            data: TagResource::make($tag)
        );
    }
}
