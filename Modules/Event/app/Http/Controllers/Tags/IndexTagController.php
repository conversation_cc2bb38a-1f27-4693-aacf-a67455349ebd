<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Tags;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Tags\TagsIndexAction;
use Modules\Event\DataTransferObjects\Tags\IndexTagData;
use Modules\Event\Http\Requests\Tags\IndexTagsRequest;
use Modules\Event\Transformers\Tags\TagsResource;

final class IndexTagController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(IndexTagsRequest $request, int $id): JsonResponse
    {
        $tags = app(TagsIndexAction::class)(
            $id,
            IndexTagData::from($request->validated()),
        );

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: TagsResource::collection(
                $tags
            )->appends(
                $request->query()
            )
        );
    }
}
