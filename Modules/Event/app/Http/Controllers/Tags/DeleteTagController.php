<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Tags;

use App\Exceptions\LogicalException;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Tags\DeleteTagsAction;

final class DeleteTagController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param int $id
     * @param int $tag_id
     *
     * @return JsonResponse
     * @throws LogicalException
     */
    public function __invoke(int $id, int $tag_id): JsonResponse
    {
        app(DeleteTagsAction::class)(
            $id,
            $tag_id,
        );

        return sendSuccessResponse(
            message: __('messages.delete_data'),
        );
    }
}
