<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Categories;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Categories\CategoriesListAction;
use Modules\Event\Transformers\Categories\CategoriesListResource;

final class CategoryListController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(): JsonResponse
    {
        $categories = app(CategoriesListAction::class)();

        return sendSuccessResponse(data: CategoriesListResource::collection($categories));
    }
}
