<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Attendees;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Attendees\IndexAttendeeAction;
use Modules\Event\DataTransferObjects\Attendees\IndexAttendeeData;
use Modules\Event\Http\Requests\Attendees\IndexAttendeeRequest;
use Modules\Event\Transformers\Attendees\IndexAttendeeResource;

final class IndexAttendeeController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(IndexAttendeeRequest $request): JsonResponse
    {
        $data = IndexAttendeeData::from($request->validated());

        $users = app(IndexAttendeeAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: IndexAttendeeResource::collection($users)
                ->appends(
                    $request->query(),
                )
        );
    }
}
