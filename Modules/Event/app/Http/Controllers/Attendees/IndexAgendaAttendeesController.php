<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Attendees;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Attendees\IndexAgendaAttendeesAction;
use Modules\Event\Transformers\Attendees\ListAgendaAttendeesResource;

final class IndexAgendaAttendeesController extends Controller
{
    public function __invoke(int $event_id, int $agenda_id): JsonResponse
    {
        $result = app(IndexAgendaAttendeesAction::class)($event_id, $agenda_id);

        return sendSuccessResponse(data: ListAgendaAttendeesResource::make($result));
    }
}
