<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Attendees;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Attendees\DeleteAttendeeAction;

final class DeleteAttendeeController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(int $id, int $user_id): JsonResponse
    {

        app(DeleteAttendeeAction::class)($id, $user_id);

        return sendSuccessResponse(
            message: __('messages.delete_data'),
        );
    }
}
