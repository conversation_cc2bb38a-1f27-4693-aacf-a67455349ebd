<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Attendees;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Attendees\ImportAttendeeAction;
use Modules\Event\DataTransferObjects\Attendees\ImportAttendeeData;
use Modules\Event\Http\Requests\Attendees\ImportAttendeeRequest;

final class ImportAttendeeController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(ImportAttendeeRequest $request): JsonResponse
    {
        $data = ImportAttendeeData::from($request);

        app(ImportAttendeeAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.importing_data'),
        );
    }
}
