<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Attendees;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Attendees\StoreAttendeeAction;
use Modules\Event\DataTransferObjects\Attendees\StoreAttendeeData;
use Modules\Event\Http\Requests\Attendees\StoreAttendeeRequest;
use Modules\Event\Transformers\Attendees\AttendeeResource;

final class StoreAttendeeController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(StoreAttendeeRequest $request): JsonResponse
    {
        $data = StoreAttendeeData::from($request->validated());

        $user = app(StoreAttendeeAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: AttendeeResource::make($user)
        );
    }
}
