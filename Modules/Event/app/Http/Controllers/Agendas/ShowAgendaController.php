<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Agendas;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Agendas\ShowAgendaAction;
use Modules\Event\Transformers\Agendas\ShowAgendaResource;

final class ShowAgendaController extends Controller
{
    public function __invoke(int $id, int $agenda_id): JsonResponse
    {
        $agenda = app(ShowAgendaAction::class)($id, $agenda_id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: ShowAgendaResource::make($agenda)
        );
    }
}
