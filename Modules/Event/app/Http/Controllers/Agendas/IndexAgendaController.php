<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Agendas;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Agendas\IndexAgendaAction;
use Modules\Event\DataTransferObjects\Agendas\IndexAgendaData;
use Modules\Event\Http\Requests\Agendas\IndexAgendaRequest;
use Modules\Event\Transformers\Agendas\IndexAgendaResource;

final class IndexAgendaController extends Controller
{
    public function __invoke(IndexAgendaRequest $request): JsonResponse
    {
        $data = IndexAgendaData::from($request->validated());
        $agendas = app(IndexAgendaAction::class)($data);

        return sendSuccessResponse(data: IndexAgendaResource::collection($agendas));
    }
}
