<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Agendas;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Agendas\UpdateAgendaAction;
use Modules\Event\DataTransferObjects\Agendas\UpdateAgendaData;
use Modules\Event\Http\Requests\Agendas\UpdateAgendaRequest;
use Modules\Event\Transformers\Agendas\ShowAgendaResource;

final class UpdateAgendaController extends Controller
{
    public function __invoke(UpdateAgendaRequest $request): JsonResponse
    {
        $data = UpdateAgendaData::from($request->validated());

        $agenda = app(UpdateAgendaAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.update_data'),
            data: ShowAgendaResource::make($agenda),
        );
    }
}
