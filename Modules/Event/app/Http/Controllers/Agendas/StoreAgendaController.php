<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Agendas;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Agendas\StoreAgendaAction;
use Modules\Event\DataTransferObjects\Agendas\StoreAgendaData;
use Modules\Event\Http\Requests\Agendas\StoreAgendaRequest;
use Modules\Event\Transformers\Agendas\AgendaResource;

final class StoreAgendaController extends Controller
{
    public function __invoke(StoreAgendaRequest $request): JsonResponse
    {
        $data = StoreAgendaData::from($request->validated());

        $agenda = app(StoreAgendaAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: AgendaResource::make($agenda),
        );
    }
}
