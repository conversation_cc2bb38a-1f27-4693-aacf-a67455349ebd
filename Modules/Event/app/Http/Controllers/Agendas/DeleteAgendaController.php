<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Agendas;

use App\Exceptions\LogicalException;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Agendas\DeleteAgendaAction;

final class DeleteAgendaController extends Controller
{
    /**
     * @throws LogicalException
     */
    public function __invoke(int $id, int $agenda_id): JsonResponse
    {
        app(DeleteAgendaAction::class)($id, $agenda_id);

        return sendSuccessResponse(
            message: __('messages.delete_data')
        );
    }
}
