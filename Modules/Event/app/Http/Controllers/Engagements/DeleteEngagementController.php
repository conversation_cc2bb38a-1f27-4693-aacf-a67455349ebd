<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Engagements\DeleteEngagementAction;
use Modules\Event\Http\Requests\Engagements\DeleteEngagementRequest;

final class DeleteEngagementController extends Controller
{
    public function __invoke(DeleteEngagementRequest $request): JsonResponse
    {
        app(DeleteEngagementAction::class)($request->validated());

        return sendSuccessResponse(
            message: __('messages.delete_data'),
        );
    }
}
