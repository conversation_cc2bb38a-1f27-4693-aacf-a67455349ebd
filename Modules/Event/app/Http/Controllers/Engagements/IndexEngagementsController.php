<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Engagements\IndexEngagementAction;
use Modules\Event\DataTransferObjects\Engagements\IndexEngagementData;
use Modules\Event\Http\Requests\Engagements\IndexEngagementRequest;
use Modules\Event\Transformers\Engagements\EngagementsResource;

final class IndexEngagementsController extends Controller
{
    public function __invoke(IndexEngagementRequest $request): JsonResponse
    {
        $data = IndexEngagementData::from($request->validated());
        $engagements = app(IndexEngagementAction::class)($data);

        return sendSuccessResponse(
            __('messages.get_data'),
            EngagementsResource::make($engagements),
        );
    }
}
