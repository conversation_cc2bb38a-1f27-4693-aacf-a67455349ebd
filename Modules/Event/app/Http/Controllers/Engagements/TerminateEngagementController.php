<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\Engagements\TerminateEngagementAction;

final class TerminateEngagementController extends Controller
{
    public function __invoke(Request $request, int $id, int $agenda_id, int $engagement_id): JsonResponse
    {
        app(TerminateEngagementAction::class)($id, $agenda_id, $engagement_id);

        return sendSuccessResponse(
            message: __('event::messages.terminate_engagement'),
        );
    }
}
