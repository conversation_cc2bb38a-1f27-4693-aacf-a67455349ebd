<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\Engagements\PublishEngagementAction;
use Modules\Event\Transformers\Engagements\EngagementResource;

final class PublishEngagementController extends Controller
{
    public function __invoke(Request $request, int $id, int $agenda_id, int $engagement_id): JsonResponse
    {
        $engagement = app(PublishEngagementAction::class)($id, $agenda_id, $engagement_id);

        return sendSuccessResponse(
            message: __('event::messages.engagement_published'),
            data: EngagementResource::make($engagement),
        );
    }
}
