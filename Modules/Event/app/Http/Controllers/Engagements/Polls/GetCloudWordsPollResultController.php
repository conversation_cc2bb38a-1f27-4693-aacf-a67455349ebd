<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements\Polls;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Engagements\Polls\GetCloudWordsPollResultAction;
use Modules\Event\DataTransferObjects\Engagements\Polls\GetCloudWordsPollResultData;
use Modules\Event\Http\Requests\Engagements\Polls\GetCloudWordsPollResultRequest;
use Modules\Event\Transformers\Engagements\Polls\CloudWordsPollResultResource;

final class GetCloudWordsPollResultController extends Controller
{
    public function __invoke(GetCloudWordsPollResultRequest $request): JsonResponse
    {

        $data = GetCloudWordsPollResultData::from($request->validated());

        $engagement = app(GetCloudWordsPollResultAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: CloudWordsPollResultResource::make($engagement),
        );
    }
}
