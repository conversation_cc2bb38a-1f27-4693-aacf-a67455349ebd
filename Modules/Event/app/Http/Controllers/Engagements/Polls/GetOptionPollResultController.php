<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements\Polls;

use App\Exceptions\LogicalException;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Engagements\Polls\GetOptionPollResultAction;
use Modules\Event\DataTransferObjects\Engagements\Polls\GetOptionPollResultData;
use Modules\Event\Http\Requests\Engagements\Polls\GetOptionPollResultRequest;
use Modules\Event\Transformers\Engagements\Polls\OptionPollResultResource;

final class GetOptionPollResultController extends Controller
{
    /**
     * @throws LogicalException
     */
    public function __invoke(GetOptionPollResultRequest $request): JsonResponse
    {

        $data = GetOptionPollResultData::from($request->validated());

        $engagement = app(GetOptionPollResultAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: OptionPollResultResource::make($engagement),
        );
    }
}
