<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements\Polls;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Engagements\Polls\UpdatePollAction;
use Modules\Event\DataTransferObjects\Engagements\Polls\UpdatePollData;
use Modules\Event\Http\Requests\Engagements\Polls\UpdatePollRequest;
use Modules\Event\Transformers\Engagements\Polls\PollResource;

final class UpdatePollController extends Controller
{
    public function __invoke(UpdatePollRequest $request): JsonResponse
    {

        $data = UpdatePollData::from($request->validated());

        $engagement = app(UpdatePollAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: PollResource::make($engagement),
        );
    }
}
