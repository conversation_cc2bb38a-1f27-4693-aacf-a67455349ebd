<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements\Polls;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Engagements\Polls\StorePollAction;
use Modules\Event\DataTransferObjects\Engagements\Polls\StorePollData;
use Modules\Event\Http\Requests\Engagements\Polls\StorePollRequest;
use Modules\Event\Transformers\Engagements\Polls\PollResource;

final class StorePollController extends Controller
{
    public function __invoke(StorePollRequest $request): JsonResponse
    {

        $data = StorePollData::from($request->validated());

        $engagement = app(StorePollAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: PollResource::make($engagement),
        );
    }
}
