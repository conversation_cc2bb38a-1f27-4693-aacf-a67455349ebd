<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements\Questions;

use App\Exceptions\LogicalException;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Engagements\Questions\UpdateQuestionsAction;
use Modules\Event\DataTransferObjects\Engagements\Questions\UpdateQuestionsData;
use Modules\Event\Http\Requests\Engagements\Questions\UpdateQuestionsRequest;
use Modules\Event\Transformers\Engagements\Questions\QuestionsResource;

final class UpdateQuestionsController extends Controller
{
    /**
     * @throws LogicalException
     */
    public function __invoke(UpdateQuestionsRequest $request): JsonResponse
    {
        $data = UpdateQuestionsData::from($request->validated());

        $engagement = app(UpdateQuestionsAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: QuestionsResource::make($engagement),
        );
    }
}
