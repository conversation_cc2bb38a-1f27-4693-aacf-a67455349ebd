<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements\Questions\Responses;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Engagements\Questions\Responses\IndexQuestionsAction;
use Modules\Event\DataTransferObjects\Engagements\Questions\Responses\IndexQuestionsData;
use Modules\Event\Http\Requests\Engagements\Questions\Responses\IndexQuestionsRequest;
use Modules\Event\Transformers\Engagements\Questions\Responses\IndexQuestionResource;

final class IndexQuestionsController extends Controller
{
    public function __invoke(IndexQuestionsRequest $request): JsonResponse
    {
        $data = IndexQuestionsData::from($request->validated());

        $engagement = app(IndexQuestionsAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: IndexQuestionResource::make($engagement),
        );
    }
}
