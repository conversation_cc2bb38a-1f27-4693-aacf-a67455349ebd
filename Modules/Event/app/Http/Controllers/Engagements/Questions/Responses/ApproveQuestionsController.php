<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements\Questions\Responses;

use App\Exceptions\LogicalException;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Engagements\Questions\Responses\ApproveQuestionsAction;
use Modules\Event\DataTransferObjects\Engagements\Questions\Responses\ApproveQuestionsData;
use Modules\Event\Http\Requests\Engagements\Questions\Responses\ApproveQuestionsRequest;

final class ApproveQuestionsController extends Controller
{
    /**
     * @throws LogicalException
     */
    public function __invoke(ApproveQuestionsRequest $request): JsonResponse
    {
        $data = ApproveQuestionsData::from($request->validated());

        app(ApproveQuestionsAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.update_data'),
        );
    }
}
