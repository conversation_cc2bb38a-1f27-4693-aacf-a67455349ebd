<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements\Questions\Responses;

use App\Exceptions\LogicalException;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Engagements\Questions\Responses\RejectQuestionsAction;
use Modules\Event\DataTransferObjects\Engagements\Questions\Responses\RejectQuestionsData;
use Modules\Event\Http\Requests\Engagements\Questions\Responses\RejectQuestionsRequest;

final class RejectQuestionsController extends Controller
{
    /**
     * @throws LogicalException
     */
    public function __invoke(RejectQuestionsRequest $request): JsonResponse
    {
        $data = RejectQuestionsData::from($request->validated());

        app(RejectQuestionsAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.update_data'),
        );
    }
}
