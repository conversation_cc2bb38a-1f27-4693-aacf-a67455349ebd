<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Engagements\Questions;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Engagements\Questions\StoreQuestionsAction;
use Modules\Event\DataTransferObjects\Engagements\Questions\StoreQuestionsData;
use Modules\Event\Http\Requests\Engagements\Questions\StoreQuestionsRequest;
use Modules\Event\Transformers\Engagements\Questions\QuestionsResource;

final class StoreQuestionsController extends Controller
{
    public function __invoke(StoreQuestionsRequest $request): JsonResponse
    {
        $data = StoreQuestionsData::from($request->validated());

        $engagement = app(StoreQuestionsAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: QuestionsResource::make($engagement),
        );
    }
}
