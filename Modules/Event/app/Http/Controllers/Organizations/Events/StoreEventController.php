<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Organizations\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Events\StoreEventAction;
use Modules\Event\DataTransferObjects\StoreEventData;
use Modules\Event\Http\Requests\Organizations\Events\StoreEventRequest;
use Modules\Event\Transformers\Events\EventResource;

final class StoreEventController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param StoreEventRequest $request
     *
     * @return JsonResponse
     */
    public function __invoke(StoreEventRequest $request): JsonResponse
    {
        $eventData = StoreEventData::from($request);

        $event = app(StoreEventAction::class)($eventData);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: EventResource::make($event)
        );
    }
}
