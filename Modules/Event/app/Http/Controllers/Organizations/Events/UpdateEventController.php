<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Organizations\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Events\UpdateEventAction;
use Modules\Event\DataTransferObjects\UpdateEventData;
use Modules\Event\Http\Requests\Organizations\Events\UpdateEventRequest;
use Modules\Event\Transformers\Events\EventResource;

final class UpdateEventController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param UpdateEventRequest $request
     *
     * @param int $id
     *
     * @return JsonResponse
     */
    public function __invoke(UpdateEventRequest $request, int $id): JsonResponse
    {
        $eventData = UpdateEventData::from($request);

        $event = app(UpdateEventAction::class)($eventData, $id);

        return sendSuccessResponse(
            message: __('messages.update_data'),
            data: EventResource::make($event)
        );
    }
}
