<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Memories;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Memories\DeleteMemoryAction;
use Modules\Event\Http\Requests\App\Events\Memories\DeleteMemoryRequest;

final class DeleteMemoryController extends Controller
{
    public function __invoke(DeleteMemoryRequest $request): JsonResponse
    {
        app(DeleteMemoryAction::class)($request->validated());

        return sendSuccessResponse(
            message: __('messages.delete_data'),
        );
    }
}
