<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Memories;

use App\Exceptions\LogicalException;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Memories\IndexMemoryAction;
use Modules\Event\DataTransferObjects\App\Events\Memories\IndexMemoryData;
use Modules\Event\Http\Requests\App\Events\Memories\IndexMemoryRequest;
use Modules\Event\Transformers\Memories\MemoriesResource;

final class IndexMemoryController extends Controller
{
    /**
     * @throws LogicalException
     */
    public function __invoke(IndexMemoryRequest $request): JsonResponse
    {
        $data = IndexMemoryData::from($request->validated());

        $memories = app(IndexMemoryAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: MemoriesResource::collection($memories)->appends($request->query())
        );
    }
}
