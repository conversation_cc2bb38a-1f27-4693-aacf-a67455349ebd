<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Memories;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Memories\StoreMemoryAction;
use Modules\Event\DataTransferObjects\App\Events\Memories\StoreMemoryData;
use Modules\Event\Http\Requests\App\Events\Memories\StoreMemoryRequest;
use Modules\Event\Transformers\Memories\MemoryResource;

final class StoreMemoryController extends Controller
{
    public function __invoke(StoreMemoryRequest $request): JsonResponse
    {
        $data = StoreMemoryData::from($request->validated());

        $memory = app(StoreMemoryAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: MemoryResource::make($memory)
        );
    }
}
