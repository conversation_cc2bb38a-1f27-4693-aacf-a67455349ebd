<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Memories\Interactions;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Memories\Interactions\StoreInteractionAction;
use Modules\Event\DataTransferObjects\App\Events\Interactions\StoreInteractionData;
use Modules\Event\Http\Requests\App\Events\Memories\Interactions\StoreInteractionRequest;

final class StoreInteractionController extends Controller
{
    public function __invoke(StoreInteractionRequest $request): JsonResponse
    {
        $data = StoreInteractionData::from($request->validated());

        app(StoreInteractionAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.create_data'),
        );
    }
}
