<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Polls\CloudWords;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Agendas\Engagements\Polls\CloudWords\SendCloudWordAnswerAction;
use Modules\Event\DataTransferObjects\App\Events\Engagements\Polls\SendCloudWordAnswerData;
use Modules\Event\Http\Requests\App\Events\Sessions\Engagements\Responses\SendCloudWordAnswerRequest;

final class SendCloudWordAnswerController extends Controller
{
    public function __invoke(SendCloudWordAnswerRequest $request): JsonResponse
    {
        $data = SendCloudWordAnswerData::from($request->validated());

        app(SendCloudWordAnswerAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.successfully_sent'),
            data: null
        );
    }
}
