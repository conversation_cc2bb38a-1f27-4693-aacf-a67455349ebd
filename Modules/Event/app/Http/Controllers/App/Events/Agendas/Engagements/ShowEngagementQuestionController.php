<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Agendas\Engagements;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\App\Events\Agendas\EngagementQuestions\ShowEngagementQuestionAction;
use Modules\Event\Transformers\App\Events\Engagements\EngagementQuestionResource;

final class ShowEngagementQuestionController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request;
     *
     * @return JsonResponse
     */
    public function __invoke(Request $request, int $eventId, int $sessionId, int $engagementId): JsonResponse
    {
        $engagement = app(ShowEngagementQuestionAction::class)($eventId, $sessionId, $engagementId);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: EngagementQuestionResource::make($engagement)
        );
    }
}
