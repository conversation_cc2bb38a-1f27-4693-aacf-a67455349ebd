<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Agendas\Speakers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\App\Events\Agendas\Speakers\ListAgendaSpeakerAction;
use Modules\Event\Transformers\Speakers\ListAgendaSpeakersResource;

final class ListAgendaSpeakerController extends Controller
{
    public function __invoke(Request $request, int $event_id, int $agenda_id): JsonResponse
    {
        $speakers = app(ListAgendaSpeakerAction::class)($request, $event_id, $agenda_id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: ListAgendaSpeakersResource::collection($speakers)
                ->appends($request->query()),
        );
    }
}
