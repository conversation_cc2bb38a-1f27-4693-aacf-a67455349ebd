<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Polls\Options;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Agendas\Engagements\Polls\Options\ShowOptionEngagementAction;
use Modules\Event\DataTransferObjects\App\Events\Engagements\ShowEngagementData;
use Modules\Event\Http\Requests\App\Events\Engagements\ShowEngagementRequest;
use Modules\Event\Transformers\Engagements\Polls\PollResource;

final class ShowOptionEngagementController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(ShowEngagementRequest $request): JsonResponse
    {
        $data = ShowEngagementData::from($request->validated());
        $engagement = app(ShowOptionEngagementAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: PollResource::make($engagement),
        );
    }
}
