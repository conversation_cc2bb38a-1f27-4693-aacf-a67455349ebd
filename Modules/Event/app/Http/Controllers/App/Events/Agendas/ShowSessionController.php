<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Agendas;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\App\Events\Agendas\ShowAgendaAction;
use Modules\Event\Transformers\App\Events\Agenda\AgendaResource;

final class ShowSessionController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, int $id, int $agenda_id): JsonResponse
    {
        $agenda = app(ShowAgendaAction::class)($id, $agenda_id);

        return sendSuccessResponse(__('messages.get_data'), AgendaResource::make($agenda));
    }
}
