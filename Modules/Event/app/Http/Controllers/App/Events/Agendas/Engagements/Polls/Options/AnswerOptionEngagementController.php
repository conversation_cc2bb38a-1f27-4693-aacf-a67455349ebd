<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Polls\Options;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Agendas\Engagements\Polls\Options\AnswerOptionEngagementAction;
use Modules\Event\DataTransferObjects\App\Events\Engagements\AnswerOptionEngagementData;
use Modules\Event\Http\Requests\App\Events\Engagements\AnswerOptionEngagementRequest;

final class AnswerOptionEngagementController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(AnswerOptionEngagementRequest $request): JsonResponse
    {
        $data = AnswerOptionEngagementData::from($request->validated());
        app(AnswerOptionEngagementAction::class)($data);

        return sendSuccessResponse(__('messages.create_data'));
    }
}
