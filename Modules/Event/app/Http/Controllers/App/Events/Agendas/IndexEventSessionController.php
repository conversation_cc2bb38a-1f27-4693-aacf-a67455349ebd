<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Agendas;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\App\Events\Agendas\IndexEventAgendaAction;
use Modules\Event\DataTransferObjects\Agendas\App\IndexAgendaData;
use Modules\Event\Http\Requests\App\Events\Sessions\IndexEventSessionRequest;
use Modules\Event\Transformers\Agendas\AgendasResource;

final class IndexEventSessionController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(IndexEventSessionRequest $request, int $id): JsonResponse
    {
        $data = IndexAgendaData::from($request);

        $agendas = app(IndexEventAgendaAction::class)($data, $id);

        return sendSuccessResponse(__('messages.get_data'), AgendasResource::collection($agendas));
    }
}
