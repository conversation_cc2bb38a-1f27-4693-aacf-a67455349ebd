<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Agendas;

use App\Exceptions\LogicalException;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\App\Events\Agendas\ShowCurrentSessionAction;
use Modules\Event\Transformers\App\Events\Agenda\AgendasResource;

final class ShowCurrentSessionController extends Controller
{
    /**
     * Handle the incoming request.
     * @throws LogicalException
     */
    public function __invoke(Request $request, int $id): JsonResponse
    {
        $agenda = app(ShowCurrentSessionAction::class)($id);

        return sendSuccessResponse(
            __('messages.get_data'),
            data: $agenda ? AgendasResource::make($agenda) : null
        );
    }
}
