<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Responses;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Agendas\EngagementQuestions\Responses\IndexEngagementResponseAction;
use Modules\Event\DataTransferObjects\App\Events\EngagementQuestions\IndexEngagementResponseData;
use Modules\Event\Http\Requests\App\Events\Sessions\Engagements\IndexEngagementResponsesRequest;
use Modules\Event\Transformers\App\Events\Engagements\Responses\EngagementResponsesResource;

final class IndexEngagementResponsesController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param IndexEngagementResponsesRequest $request
     * @param int $eventId
     * @param int $sessionId
     * @param int $engagementId
     * @return JsonResponse
     */
    public function __invoke(IndexEngagementResponsesRequest $request, int $eventId, int $sessionId, int $engagementId): JsonResponse
    {
        $data = IndexEngagementResponseData::from($request->validated());

        $responses = app(IndexEngagementResponseAction::class)($data, $eventId, $sessionId, $engagementId);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: EngagementResponsesResource::collection($responses)->appends(request()->query())
        );
    }
}
