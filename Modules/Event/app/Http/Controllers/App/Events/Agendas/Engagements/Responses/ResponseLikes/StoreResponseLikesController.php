<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Responses\ResponseLikes;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\App\Events\Agendas\EngagementQuestions\Responses\Likes\StoreResponseLikesAction;

final class StoreResponseLikesController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param int $eventId
     * @param int $sessionId
     * @param int $engagementId
     * @param int $responseId
     *
     * @return JsonResponse
     */
    public function __invoke(Request $request, int $eventId, int $sessionId, int $engagementId, int $responseId): JsonResponse
    {
        app(StoreResponseLikesAction::class)($request, $eventId, $sessionId, $engagementId, $responseId);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: null
        );
    }
}
