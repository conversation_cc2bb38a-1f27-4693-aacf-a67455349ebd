<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Responses;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Agendas\EngagementQuestions\Responses\StoreEngagementResponseAction;
use Modules\Event\DataTransferObjects\App\Events\EngagementQuestions\EngagementResponseData;
use Modules\Event\Http\Requests\App\Events\Sessions\Engagements\Responses\StoreEngagementResponsesRequest;

final class StoreEngagementResponsesController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param StoreEngagementResponsesRequest $request
     * @param int $eventId
     * @param int $sessionId
     * @param int $engagementId
     * @return JsonResponse
     */
    public function __invoke(StoreEngagementResponsesRequest $request, int $eventId, int $sessionId, int $engagementId): JsonResponse
    {
        $data = EngagementResponseData::from($request->validated());

        app(StoreEngagementResponseAction::class)($data, $eventId, $sessionId, $engagementId);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: null
        );
    }
}
