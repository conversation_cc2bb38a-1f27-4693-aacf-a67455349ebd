<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Speakers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Speakers\ShowSpeakerAction;
use Modules\Event\Transformers\App\Events\SpeakerResource;

final class ShowSpeakerController extends Controller
{
    public function __invoke(int $event_id, int $agenda_id, int $speaker_id): JsonResponse
    {
        $speaker = app(ShowSpeakerAction::class)($event_id, $agenda_id, $speaker_id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: SpeakerResource::make($speaker)
        );
    }
}
