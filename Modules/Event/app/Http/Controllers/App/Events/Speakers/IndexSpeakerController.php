<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Speakers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Speakers\IndexSpeakersAction;
use Modules\Event\DataTransferObjects\App\Events\Speakers\IndexSpeakerData;
use Modules\Event\Http\Requests\App\Events\Speakers\IndexSpeakersRequest;
use Modules\Event\Transformers\App\Events\Speakers\SpeakersResource;

final class IndexSpeakerController extends Controller
{
    public function __invoke(IndexSpeakersRequest $request): JsonResponse
    {
        $data = IndexSpeakerData::from($request->validated());

        $speakers = app(IndexSpeakersAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: SpeakersResource::collection($speakers)->appends(request()->query())
        );
    }
}
