<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Supervisors;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Supervisors\IndexSupervisorsAction;
use Modules\Event\DataTransferObjects\App\Events\Supervisors\IndexSupervisorData;
use Modules\Event\Http\Requests\App\Events\Supervisors\IndexSupervisorsRequest;
use Modules\Event\Transformers\App\Events\Supervisors\SupervisorsResource;

final class IndexSupervisorController extends Controller
{
    public function __invoke(IndexSupervisorsRequest $request): JsonResponse
    {
        $data = IndexSupervisorData::from($request->validated());

        $supervisors = app(IndexSupervisorsAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: SupervisorsResource::collection($supervisors)->appends(request()->query())
        );
    }
}
