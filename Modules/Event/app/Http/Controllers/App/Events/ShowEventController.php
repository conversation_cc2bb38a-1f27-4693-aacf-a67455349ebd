<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\ShowEventAction;
use Modules\Event\Transformers\App\Events\EventResource;

final class ShowEventController extends Controller
{
    public function __invoke(int $id): JsonResponse
    {
        $event = app(ShowEventAction::class)($id);

        return sendSuccessResponse(data: EventResource::make($event));
    }
}
