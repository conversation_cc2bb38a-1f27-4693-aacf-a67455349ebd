<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Materials;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Materials\IndexMaterialAction;
use Modules\Event\DataTransferObjects\App\Events\Materials\IndexMaterialData;
use Modules\Event\Http\Requests\App\Events\Materials\IndexMaterialRequest;
use Modules\Event\Transformers\App\Events\Materials\MaterialsResource;

final class IndexMaterialController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param IndexMaterialRequest $request;
     *
     * @return JsonResponse
     */
    public function __invoke(IndexMaterialRequest $request): JsonResponse
    {
        $data = IndexMaterialData::from($request);

        $materials = app(IndexMaterialAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: MaterialsResource::collection($materials)->appends(request()->query())
        );
    }
}
