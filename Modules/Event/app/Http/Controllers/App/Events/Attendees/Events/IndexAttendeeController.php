<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events\Attendees\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\Attendees\Events\IndexAttendeeAction;
use Modules\Event\DataTransferObjects\App\Events\Attendees\IndexAttendeesData;
use Modules\Event\Http\Requests\App\Events\Attendees\IndexAttendeesRequest;
use Modules\Event\Transformers\App\Events\Attendees\AttendeesResource;

final class IndexAttendeeController extends Controller
{
    public function __invoke(IndexAttendeesRequest $request): JsonResponse
    {
        $data = IndexAttendeesData::from($request->validated());

        $attendees = app(IndexAttendeeAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: AttendeesResource::collection($attendees)->appends(request()->query())
        );
    }
}
