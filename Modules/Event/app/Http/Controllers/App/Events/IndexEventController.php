<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\App\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\App\Events\IndexEventAction;
use Modules\Event\DataTransferObjects\App\Events\IndexEventData;
use Modules\Event\Http\Requests\App\Events\IndexEventsRequest;
use Modules\Event\Transformers\App\Events\EventsResource;

final class IndexEventController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(IndexEventsRequest $request): JsonResponse
    {

        $request = $request->validated();
        $user_id = getCurrentUser()->id;
        $data = IndexEventData::from($request + ['user_id' => $user_id]);
        $events = app(IndexEventAction::class)($data);

        return sendSuccessResponse(
            data: EventsResource::collection($events)->appends(request()->query())
        );
    }
}
