<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\Events\ShowEventAction;
use Modules\Event\Transformers\Events\EventResource;

final class ShowEventController extends Controller
{
    public function __invoke(Request $request, int $id): JsonResponse
    {
        $event = app(ShowEventAction::class)($id);

        return sendSuccessResponse(
            data: EventResource::make(
                $event
            )
        );
    }
}
