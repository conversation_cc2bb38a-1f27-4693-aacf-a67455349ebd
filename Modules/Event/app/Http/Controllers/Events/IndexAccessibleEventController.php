<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Events\IndexAccessibleEventsAction;
use Modules\Event\Transformers\Events\AccessibleEventsResource;

final class IndexAccessibleEventController extends Controller
{
    public function __invoke(): JsonResponse
    {
        $events = app(IndexAccessibleEventsAction::class)();

        return sendSuccessResponse(
            data: AccessibleEventsResource::collection(
                $events
            ),
        );
    }
}
