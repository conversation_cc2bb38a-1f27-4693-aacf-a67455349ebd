<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Events\IndexEventsAction;
use Modules\Event\DataTransferObjects\Events\IndexEventsData;
use Modules\Event\Http\Requests\Events\IndexEventsRequest;
use Modules\Event\Transformers\Events\EventsResource;

final class IndexEventController extends Controller
{
    public function __invoke(IndexEventsRequest $request): JsonResponse
    {
        $data = IndexEventsData::from($request->validated());

        $events = app(IndexEventsAction::class)($data);

        return sendSuccessResponse(
            data: EventsResource::collection(
                $events
            )->appends(
                $request->query()
            )
        );
    }
}
