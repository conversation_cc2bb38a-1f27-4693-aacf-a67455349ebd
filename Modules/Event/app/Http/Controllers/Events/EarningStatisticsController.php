<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Events;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Events\GetEarningStatisticsAction;
use Modules\Event\Http\Resources\Events\EarningStatisticsResource;

final class EarningStatisticsController extends Controller
{
    public function __invoke(int $id): JsonResponse
    {
        $statistics = app(GetEarningStatisticsAction::class)($id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: EarningStatisticsResource::make($statistics)
        );
    }
}
