<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Events\Statistics\General;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\Events\Statistics\General\SpeakersAction;
use Modules\Event\Transformers\Events\Statistics\General\SpeakerStatisticsResource;

final class SpeakerStatisticsController extends Controller
{
    public function __invoke(Request $request, int $id): JsonResponse
    {
        $speakers = app(SpeakersAction::class)($request, $id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: SpeakerStatisticsResource::collection(
                $speakers
            )->appends(request()->query())
        );
    }
}
