<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Events\Statistics\General;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\Events\Statistics\General\SessionsAction;
use Modules\Event\Transformers\Events\Statistics\General\SessionStatisticsResource;

final class SessionStatisticsController extends Controller
{
    public function __invoke(Request $request, int $id): JsonResponse
    {
        $sessions = app(SessionsAction::class)($request, $id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: SessionStatisticsResource::collection(
                $sessions
            )->appends(request()->query())
        );
    }
}
