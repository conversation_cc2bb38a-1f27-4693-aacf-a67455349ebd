<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Events\Statistics\General;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\Events\Statistics\General\SupervisorsAction;
use Modules\Event\Transformers\Events\Statistics\General\SupervisorStatisticsResource;

final class SupervisorStatisticsController extends Controller
{
    public function __invoke(Request $request, int $id): JsonResponse
    {
        $supervisors = app(SupervisorsAction::class)($request, $id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: SupervisorStatisticsResource::collection(
                $supervisors
            )->appends(request()->query())
        );
    }
}
