<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Events\Statistics\General;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Event\Actions\Events\Statistics\General\AttendeesAction;
use Modules\Event\Transformers\Events\Statistics\General\AttendeeStatisticsResource;

final class AttendeeStatisticsController extends Controller
{
    public function __invoke(Request $request, int $id): JsonResponse
    {
        $attendees = app(AttendeesAction::class)($id);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: AttendeeStatisticsResource::make(
                $attendees
            )
        );
    }
}
