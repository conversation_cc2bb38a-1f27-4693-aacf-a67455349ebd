<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Announcements;

use App\Exceptions\LogicalException;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Announcements\StoreAnnouncementAction;
use Modules\Event\DataTransferObjects\Announcements\StoreAnnouncementData;
use Modules\Event\Http\Requests\Announcements\StoreAnnouncementRequest;
use Modules\Event\Transformers\Announcements\AnnouncementResource;

final class StoreAnnouncementController extends Controller
{
    /**
     * @throws LogicalException
     */
    public function __invoke(StoreAnnouncementRequest $request): JsonResponse
    {
        $data = StoreAnnouncementData::from($request->validated());

        $announcement = app(StoreAnnouncementAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.create_data'),
            data: AnnouncementResource::make($announcement),
        );
    }
}
