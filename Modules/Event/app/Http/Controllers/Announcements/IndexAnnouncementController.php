<?php

declare(strict_types=1);

namespace Modules\Event\Http\Controllers\Announcements;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Event\Actions\Announcements\IndexAnnouncementAction;
use Modules\Event\DataTransferObjects\Announcements\IndexAnnouncementData;
use Modules\Event\Http\Requests\Announcements\IndexAnnouncementRequest;
use Modules\Event\Transformers\Announcements\AnnouncementsResource;

final class IndexAnnouncementController extends Controller
{
    public function __invoke(IndexAnnouncementRequest $request): JsonResponse
    {
        $data = IndexAnnouncementData::from($request->validated());

        $announcement = app(IndexAnnouncementAction::class)($data);

        return sendSuccessResponse(
            message: __('messages.get_data'),
            data: AnnouncementsResource::collection($announcement)->appends(request()->query()),
        );
    }
}
