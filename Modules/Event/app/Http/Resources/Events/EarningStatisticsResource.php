<?php

declare(strict_types=1);

namespace Modules\Event\Http\Resources\Events;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class EarningStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'total_tickets_sold' => $this['total_tickets_sold'] ?? 0,
            'total_income' => $this['total_income'] ?? 0,
            'first_date' => $this['first_date'] ?? null,
            'last_date' => $this['last_date'] ?? null,
            'max_income' => $this['max_income'] ?? 0,
            'income_details' => $this['income_details'] ?? [],
        ];
    }
}
