<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Agendas;

use App\Http\Requests\BaseFormRequest;

final class UpdateAgendaRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => [
                'sometimes',
                'required',
                'string',
                'min:3',
                'max:500',
            ],
            'color' => [
                'sometimes',
                'required',
                'string',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i',
            ],
            'location' => [
                'sometimes',
                'required',
                'string',
                'min:3',
                'max:100',
            ],
            'description' => [
                'sometimes',
                'required',
                'string',
                'min:3',
                'max:1000',
            ],

            'end_at' => [
                'sometimes',
                'required',
                'date_format:Y-m-d H:i:s',
                'after:start_at',
            ],
            'start_at' => [
                'sometimes',
                'required',
                'date_format:Y-m-d H:i:s',
                'before:end_at',
            ],
            'seats_number' => [
                'nullable',
                'integer',
                'numeric',
                'min:1',
                'max:1000',
            ],
            'speaker_ids' => [
                'nullable',
                'array',
            ],
            'speaker_ids.*' => [
                'integer',
            ],
            'supervisors_ids' => [
                'nullable',
                'array',
            ],
            'supervisors_ids.*' => [
                'integer',
            ],
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
            'is_notification_enabled' => [
                'sometimes',
                'required',
                'boolean',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
            ]
        );
    }
}
