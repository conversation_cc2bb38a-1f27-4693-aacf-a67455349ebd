<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Agendas;

use App\Http\Requests\BaseFormRequest;

final class IndexAgendaRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                'integer',
            ],
            'search' => [
                'nullable',
                'string',
            ]
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
            ]
        );
    }
}
