<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Agendas;

use App\Http\Requests\BaseFormRequest;

final class StoreAgendaRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => [
                'required',
                'string',
                'min:3',
                'max:500',
            ],
            'color' => [
                'required',
                'string',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i',
            ],
            'location' => [
                'required',
                'string',
                'min:3',
                'max:100',
            ],
            'description' => [
                'required',
                'string',
                'min:3',
                'max:1000',
            ],

            'end_at' => [
                'required',
                'date_format:Y-m-d H:i:s',
                'after:start_at',
            ],
            'start_at' => [
                'date_format:Y-m-d H:i:s',
                'required',
                'after_or_equal:now',
                'before:end_at',
            ],
            'seats_number' => [
                'nullable',
                'integer',
                'numeric',
                'min:1',
                'max:1000',
            ],
            'speaker_ids' => [
                'array',
            ],
            'speaker_ids.*' => [
                'integer',
            ],
            'supervisor_ids' => [
                'array',
            ],
            'supervisor_ids.*' => [
                'integer',
            ],
            'event_id' => [
                'required',
                'integer',
            ],
            'is_notification_enabled' => [
                'boolean',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
            ]
        );
    }
}
