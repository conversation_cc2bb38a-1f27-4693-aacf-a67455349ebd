<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Events;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Enums\EventCheckInType;
use Modules\Event\Enums\EventStatus;

final class IndexEventsRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'search' => ['string', 'max:255'],
            'check_in_type' => ['string', Rule::enum(EventCheckInType::class)],
            'status' => ['string', Rule::enum(EventStatus::class)],
            'category_id' => ['integer', 'exists:categories,id'],
            'order_by' => ['string', 'in:start_at,end_at'],
            'sort' => ['string', 'in:asc,desc'],
            'date_from' => ['date', 'required_with:date_to'],
            'date_to' => ['date', 'required_with:date_from'],
            'records_per_page' => ['integer', 'min:1'],
            'page' => ['integer', 'min:1'],
        ];
    }

}
