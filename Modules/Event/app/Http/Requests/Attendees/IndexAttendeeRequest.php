<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Attendees;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Enums\InvitationStatus;

final class IndexAttendeeRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                Rule::exists('events', 'id'),
            ],
            'sort' => [
                'in:asc,desc',
            ],
            'search' => [
                'string',
                'nullable',
            ],
            'records_per_page' => [
                'int',
                'min:1',
            ],
            'invitation_status' => [
                'string',
                Rule::in(InvitationStatus::cases()),
            ],
            'status' => [
                'string',
                Rule::in([true, false, 0, 1, '0', '1']),
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
            ]
        );
    }
}
