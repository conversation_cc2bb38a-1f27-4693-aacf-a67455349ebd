<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Attendees;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;

final class ImportAttendeeRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file' => [
                'required',
                File::types('xlsx,xls')->max(1024 * 2),
            ],
            'event_id' => [
                'required',
                Rule::exists('events', 'id')
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
            ]
        );
    }
}
