<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Attendees;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

final class StoreAttendeeRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'name' => [
                'required',
                'string',
                'min:3',
                'max:150',
                'regex:/^[\p{L} .-]*$/u',
            ],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
            ],
            'mobile' => [
                'nullable',
                'numeric',
                'min_digits:8',
                'max_digits:15'
            ],
            'country_code' => [
                'nullable',
                Rule::exists('countries', 'code'),
            ],
            'event_id' => [
                'required',
                Rule::exists('events', 'id'),
            ],
            'tag_id' => [
                'required',
                Rule::exists('tags', 'id')->where(
                    fn ($query) => $query->where('event_id', $this->event_id)
                ),
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'email' => mb_strtolower($this->email ?? ''),
            ]
        );
    }
}
