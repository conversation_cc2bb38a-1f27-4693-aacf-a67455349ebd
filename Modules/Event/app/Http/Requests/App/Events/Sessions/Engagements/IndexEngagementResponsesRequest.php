<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\App\Events\Sessions\Engagements;

use App\Http\Requests\BaseFormRequest;

final class IndexEngagementResponsesRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'order_by' => [
                'nullable',
                'string',
                'in:count,created_at',
            ],
            'sort' => [
                'nullable',
                'string',
                'in:asc,desc',
            ],
            'per_page' => [
                'nullable',
                'integer',
                'min:1',
                'max:100',
            ],
        ];
    }
}
