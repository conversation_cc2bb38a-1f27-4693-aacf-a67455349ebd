<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\App\Events\Sessions\Engagements\Responses;

use App\Http\Requests\BaseFormRequest;

final class StoreEngagementResponsesRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                'integer'
            ],
            'session_id' => [
                'required',
                'integer'
            ],
            'engagement_id' => [
                'required',
                'integer'
            ],
            'title' => [
                'required',
                'string',
                'min:1',
                'max:255',
            ],
            'is_anonymous' => [
                'required',
                'boolean',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'session_id' => $this->route('session_id'),
                'engagement_id' => $this->route('engagement_id'),
            ]
        );
    }
}
