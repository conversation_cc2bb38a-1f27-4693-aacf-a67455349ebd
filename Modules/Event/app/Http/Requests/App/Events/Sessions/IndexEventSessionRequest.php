<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\App\Events\Sessions;

use App\Http\Requests\BaseFormRequest;

final class IndexEventSessionRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                'integer'
            ],
            'date' => [
                'date',
            ],
            'order_by' => [
                'string',
            ],
            'sort' => [
                'string',
                'in:asc,desc',
            ],
            'per_page' => [
                'integer',
            ],
            'is_joined' => [
                'boolean',
            ],
            'page' => [
                'integer',
            ],

        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'is_joined' => filter_var($this->is_joined, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
            ]
        );
    }
}
