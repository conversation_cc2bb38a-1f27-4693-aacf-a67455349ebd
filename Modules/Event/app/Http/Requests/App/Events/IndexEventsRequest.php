<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\App\Events;

use App\Http\Requests\BaseFormRequest;

final class IndexEventsRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'records_per_page' => ['integer', 'min:1'],
            'page' => ['integer', 'min:1'],
        ];
    }
}
