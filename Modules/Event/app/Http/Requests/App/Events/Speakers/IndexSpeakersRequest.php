<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\App\Events\Speakers;

use App\Http\Requests\BaseFormRequest;

final class IndexSpeakersRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'event_id' => ['required', 'integer'],
            'records_per_page' => ['integer', 'min:1'],
            'page' => ['integer', 'min:1'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
            ]
        );
    }
}
