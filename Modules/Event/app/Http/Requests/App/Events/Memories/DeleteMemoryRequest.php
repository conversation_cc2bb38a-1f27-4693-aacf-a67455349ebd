<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\App\Events\Memories;

use App\Http\Requests\BaseFormRequest;

final class DeleteMemoryRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'memory_id' => [
                'required',
                'integer'
            ],
            'event_id' => [
                'required',
                'integer'
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'memory_id' => $this->route('memory_id'),
                'event_id' => $this->route('id'),
            ]
        );
    }
}
