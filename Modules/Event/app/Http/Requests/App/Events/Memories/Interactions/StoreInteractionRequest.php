<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\App\Events\Memories\Interactions;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Enums\EmojiType;

final class StoreInteractionRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                'integer',
            ],
            'memory_id' => [
                'required',
                'integer',
            ],
            'emoji' => [
                'required',
                Rule::in(EmojiType::cases()),
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'event_id' => $this->route('id'),
            'memory_id' => $this->route('memory_id'),
        ]);
    }
}
