<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\App\Events\Memories;

use App\Http\Requests\BaseFormRequest;

final class IndexMemoryRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                'integer',
            ],
            'my_memories' => [
                'boolean',
            ],
            'order_by' => [
                'string',
                'in:created_at,interactions_count',
            ],
            'sort' => [
                'string',
                'in:asc,desc',
            ],
            'records_per_page' => [
                'integer',
                'min:1',
            ],
            'page' => [
                'integer',
                'min:1',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'event_id' => $this->route('id'),
        ]);
    }
}
