<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\App\Events\Memories;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\File;

final class StoreMemoryRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'image' => [
                'required',
                File::image()->max(50 * 1024),
            ],
            'event_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'event_id' => $this->route('id'),
        ]);
    }
}
