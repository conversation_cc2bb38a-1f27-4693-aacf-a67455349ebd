<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Organizations\Events;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Enums\EventCheckInType;

final class StoreEventRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name_ar' => [
                'required',
                'string',
                'min:3',
                'max:500',
            ],
            'name_en' => [
                'nullable',
                'string',
                'min:3',
                'max:500',
            ],
            'check_in_type' => [
                'required',
                'string',
                Rule::in(EventCheckInType::cases()),
            ],
            'start_at' => [
                'required',
                'date:Y-m-d h:i:s',
                'after_or_equal:now',
            ],
            'end_at' => [
                'required',
                'date:Y-m-d h:i:s',
                'after:start_at',
            ],
            'can_see_attendee' => [
                'required',
                'boolean',
            ],
            'can_have_speaker' => [
                'required',
                'boolean',
            ],
            'can_have_supervisor' => [
                'required',
                'boolean',
            ],
            'is_private' => [
                'sometimes',
                'boolean',
            ],
            'category_id' => [
                'required',
                'integer',
                Rule::exists('categories', 'id'),
            ],
            'allow_chatting' => [
                'required',
                'boolean',
            ],
        ];
    }
}
