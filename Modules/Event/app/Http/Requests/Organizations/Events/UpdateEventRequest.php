<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Organizations\Events;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Enums\EventCheckInType;

final class UpdateEventRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name_ar' => [
                'sometimes',
                'string',
                'min:3',
                'max:500'
            ],
            'name_en' => [
                'nullable',
                'string',
                'min:3',
                'max:500'
            ],
            'check_in_type' => [
                'sometimes',
                'string',
                Rule::in(EventCheckInType::cases())
            ],
            'start_at' => [
                'sometimes',
                'date:Y-m-d h:i:s',
                'after_or_equal:now',
            ],
            'end_at' => [
                'sometimes',
                'date:Y-m-d h:i:s',
                'after:start_at'
            ],
            'category_id' => [
                'sometimes',
                'integer',
                Rule::exists('categories', 'id')
            ],
        ];
    }
}
