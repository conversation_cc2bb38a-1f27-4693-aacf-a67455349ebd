<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Materials\Sessions;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Entities\Agenda;

final class StoreMaterialRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'materials' => [
                'required',
                'array',
            ],
            'materials.*.title' => [
                'required',
                'distinct',
                'string',
                'min:3',
                'max:50',
                Rule::unique('materials', 'title')
                    ->where('materialable_id', $this->route('agenda_id'))
                    ->where('materialable_type', Agenda::class)
            ],
            'materials.*.file' => [
                'required',
                'mimes:pdf,doc,docx,xls,xlsx,ppt,pptx',
                'max:4096',
            ],
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
            ]
        );
    }
}
