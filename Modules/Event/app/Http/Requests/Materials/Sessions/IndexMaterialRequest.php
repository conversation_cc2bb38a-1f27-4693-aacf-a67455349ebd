<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Materials\Sessions;

use App\Http\Requests\BaseFormRequest;

final class IndexMaterialRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
            'sort' => [
                'in:asc,desc'
            ]
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
            ]
        );
    }
}
