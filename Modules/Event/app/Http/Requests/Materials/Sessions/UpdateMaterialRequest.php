<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Materials\Sessions;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Entities\Agenda;

final class UpdateMaterialRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
            'title' => [
                'required',
                Rule::unique('materials', 'title')
                    ->where('materialable_id', $this->route('agenda_id'))
                    ->where('materialable_type', Agenda::class)
                    ->ignore($this->route('material_id')),
                'string',
                'min:3',
                'max:50',
            ],
            'file' => [
                'nullable',
                'file',
                'mimes:pdf,doc,docx,xls,xlsx,ppt,pptx',
                'max:4096',
            ],
            'material_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
                'material_id' => $this->route('material_id'),
            ]
        );
    }
}
