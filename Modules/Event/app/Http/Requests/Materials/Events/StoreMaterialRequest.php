<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Materials\Events;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Entities\Event;

final class StoreMaterialRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'materials' => [
                'required',
                'array',
            ],
            'materials.*.title' => [
                'required',
                'distinct',
                Rule::unique('materials', 'title')
                    ->where('materialable_id', $this->route('id'))
                    ->where('materialable_type', Event::class),
                'string',
                'min:3',
                'max:255',
            ],
            'materials.*.file' => [
                'required',
                'mimes:pdf,doc,docx,xls,xlsx,ppt,pptx',
                'max:4096',

            ],
            'event_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
            ]
        );
    }
}
