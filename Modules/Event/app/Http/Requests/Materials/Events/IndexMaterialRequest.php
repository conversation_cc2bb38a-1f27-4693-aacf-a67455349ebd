<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Materials\Events;

use App\Http\Requests\BaseFormRequest;

final class IndexMaterialRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'search' => [
                'sometimes',
                'string',
                'max:255',
            ],
            'order_by' => [
                'sometimes',
                'string',
                'in:title',
            ],
            'sort' => [
                'sometimes',
                'string',
                'in:asc,desc',
            ],
            'records_per_page' => [
                'sometimes',
                'integer',
                'min:1',
            ],
            'page' => [
                'integer',
                'min:1'
            ],
            'event_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
            ]
        );
    }

}
