<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Announcements;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

final class IndexAnnouncementRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'search' => [
                'sometimes',
                'string',
            ],
            'sort' => [
                'sometimes',
                'string',
                Rule::in(['asc', 'desc'])
            ],
            'per_page' => [
                'sometimes',
                'integer',
            ],
            'page' => [
                'sometimes',
                'integer',
            ],
            'event_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
            ]
        );
    }
}
