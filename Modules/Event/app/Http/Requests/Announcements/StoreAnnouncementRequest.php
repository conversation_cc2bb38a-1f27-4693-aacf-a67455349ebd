<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Announcements;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Entities\Event;
use Modules\Event\Enums\InvitationMethod;

final class StoreAnnouncementRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title_ar' => [
                'required',
                'string',
                'min:3',
                'max:50',
            ],
            'content_ar' => [
                'required',
                'string',
                'min:3',
                'max:1000',
            ],
            'send_method' => [
                'required',
                'string',
                Rule::in([
                    'in_app',
                    'mail',
                    'whatsapp',
                    'both',
                ]),
                function ($attribute, $value, $fail): void {
                    $event = Event::find($this->route('id'));

                    if ($event->settings->invitation_method === InvitationMethod::IN_APP->value && 'in_app' !== $value) {
                        $fail(__('event::exceptions.announcement.invalid_send_method_for_in_app'));
                    }

                    if ($event->settings->invitation_method === InvitationMethod::OUT_APP->value && ! in_array($value, ['mail', 'whatsapp', 'both'])) {
                        $fail(__('event::exceptions.announcement.invalid_send_method_for_out_app'));
                    }
                },
            ],
            'event_id' => [
                'required',
                'integer',
            ],
            'tag_ids' => [
                'required',
                'array',
                'distinct',
            ],
            'tag_ids.*' => [
                'integer',
                Rule::exists('tags', 'id'),
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'title_ar' => $this->input('title'),
                'content_ar' => $this->input('content'),
            ]
        );
    }
}
