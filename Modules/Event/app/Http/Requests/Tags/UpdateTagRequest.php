<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Tags;

use App\Http\Requests\BaseFormRequest;

final class UpdateTagRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'sometimes',
                'required',
                'string',
                'max:255',
            ],
            'color' => [
                'sometimes',
                'required',
                'string',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i',
                'max:255',
            ],
        ];
    }
}
