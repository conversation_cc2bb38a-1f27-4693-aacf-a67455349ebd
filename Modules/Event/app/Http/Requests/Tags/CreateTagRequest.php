<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Tags;

use App\Http\Requests\BaseFormRequest;

final class CreateTagRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'color' => [
                'required',
                'string',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i',
                'max:255',
            ],
        ];
    }
}
