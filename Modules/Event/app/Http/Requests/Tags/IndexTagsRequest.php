<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Tags;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

final class IndexTagsRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'search' => [
                'nullable',
                'string',
                'max:255',
            ],
            'per_page' => [
                'nullable',
                'integer',
                'min:1',
                'max:100',
            ],
            'sort_direction' => [
                'sometimes',
                'string',
                Rule::in(['asc', 'desc']),
            ],
            'sort_by' => [
                'sometimes',
                'string',
                Rule::in([
                    'created_at',
                    'name',
                ]),
            ],
        ];
    }
}
