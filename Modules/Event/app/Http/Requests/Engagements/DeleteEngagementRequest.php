<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Engagements;

use App\Http\Requests\BaseFormRequest;

final class DeleteEngagementRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
            'engagement_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
                'engagement_id' => $this->route('engagement_id'),
            ]
        );
    }
}
