<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Engagements\Polls;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Enums\PollType;

final class UpdatePollRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
            'poll_id' => [
                'required',
                'integer',
            ],
            'title' => [
                'sometimes',
                'string',
                'min:1',
                'max:255',
            ],
            'duration' => [
                'sometimes',
                'integer',
            ],
            'poll_type' => [
                'required',
                Rule::in(PollType::cases())
            ],
            'options' => [
                'required_if:poll_type,' . PollType::OPTIONS->value,
                'array',
            ],
            'options.*' => [
                'string',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
                'poll_id' => $this->route('poll_id'),
            ]
        );
    }
}
