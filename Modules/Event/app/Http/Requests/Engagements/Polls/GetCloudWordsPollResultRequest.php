<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Engagements\Polls;

use App\Http\Requests\BaseFormRequest;

final class GetCloudWordsPollResultRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
            'poll_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
                'poll_id' => $this->route('poll_id'),
            ]
        );
    }
}
