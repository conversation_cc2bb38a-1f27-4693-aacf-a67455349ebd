<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Engagements\Polls;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Enums\PollType;

final class StorePollRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
            'title' => [
                'required',
                'string',
                'min:1',
                'max:255',
            ],
            'duration' => [
                'required',
                'integer',
                'min:1',
            ],
            'poll_type' => [
                'required',
                Rule::in(PollType::cases())
            ],
            'options' => [
                Rule::requiredIf(fn () => $this->poll_type === PollType::OPTIONS->value),
                'array',
            ],
            'options.*' => [
                'string',
                'min:1',
                'max:200',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
            ]
        );
    }
}
