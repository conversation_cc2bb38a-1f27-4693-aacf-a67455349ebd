<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Engagements\Questions\Responses;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Event\Enums\AttendeeQuestionStatus;

final class IndexQuestionsRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'question_status' => [
                'required',
                Rule::in(AttendeeQuestionStatus::cases()),
            ],
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
            'question_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
                'question_id' => $this->route('question_id'),
            ]
        );
    }
}
