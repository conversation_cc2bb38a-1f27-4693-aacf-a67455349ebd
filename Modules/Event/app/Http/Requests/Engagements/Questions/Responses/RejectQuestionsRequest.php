<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Engagements\Questions\Responses;

use App\Http\Requests\BaseFormRequest;

final class RejectQuestionsRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'deleted_questions' => [
                'sometimes',
                'array',
            ],
            'deleted_questions.*' => [
                'integer',
            ],
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
            'question_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
                'question_id' => $this->route('question_id'),
            ]
        );
    }
}
