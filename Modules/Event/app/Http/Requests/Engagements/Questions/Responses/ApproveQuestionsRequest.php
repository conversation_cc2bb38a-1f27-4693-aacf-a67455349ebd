<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Engagements\Questions\Responses;

use App\Http\Requests\BaseFormRequest;

final class ApproveQuestionsRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'accepted_questions' => [
                'sometimes',
                'array',
            ],
            'accepted_questions.*' => [
                'integer',
            ],
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
            'question_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
                'question_id' => $this->route('question_id'),
            ]
        );
    }
}
