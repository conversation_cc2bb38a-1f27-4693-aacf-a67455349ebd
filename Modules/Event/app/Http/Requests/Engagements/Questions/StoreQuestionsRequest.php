<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Engagements\Questions;

use App\Http\Requests\BaseFormRequest;

final class StoreQuestionsRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                'integer',
            ],
            'agenda_id' => [
                'required',
                'integer',
            ],
            'title' => [
                'required',
                'string',
                'min:1',
                'max:255',
            ],
            'duration' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
                'agenda_id' => $this->route('agenda_id'),
            ]
        );
    }
}
