<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Platform\Events\Settings;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\File;

final class UpdateSettingsRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'description_ar' => [
                'sometimes',
                'string',
                'min:3',
                'max:5000',
            ],
            'description_en' => [
                'nullable',
                'string',
                'min:3',
                'max:5000',
            ],
            'latitude' => [
                'sometimes',
                'numeric',
            ],
            'longitude' => [
                'sometimes',
                'numeric',
            ],
            'location' => [
                'sometimes',
                'string',
                'min:3',
                'max:255',
            ],
            'logo' => [
                'sometimes',
                File::image()->max(1 * 1024),
            ],
            'banner' => [
                'sometimes',
                File::image()->max(3 * 1024),
            ],
            'primary_color' => [
                'sometimes',
                'string',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i',
            ],
            'secondary_color' => [
                'nullable',
                'string',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i',
            ],
            'reminder_period' => [
                'nullable',
                'min:5',
                'integer',
            ],
            'ticket_price' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000000',
            ],
            'badge_id' => [
                'sometimes',
                'integer',
                'min:1',
                'max:255',
            ],
            'template_id' => [
                'nullable',
                'integer',
                'min:1',
                'max:255',
            ],
        ];
    }
}
