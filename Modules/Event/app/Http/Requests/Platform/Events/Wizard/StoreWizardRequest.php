<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Platform\Events\Wizard;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;
use Modules\Event\Enums\InvitationMethod;
use Modules\Event\Enums\OutAppCommunicationMethod;

final class StoreWizardRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'description_ar' => [
                'required',
                'string',
                'min:3',
                'max:5000',
            ],
            'description_en' => [
                'sometimes',
                'string',
                'min:3',
                'max:5000',
            ],
            'latitude' => [
                'required',
                'numeric',
            ],
            'longitude' => [
                'required',
                'numeric',
            ],
            'location' => [
                'required',
                'string',
                'min:3',
                'max:255',
            ],
            'logo' => [
                'required',
                File::image()->max(1024),
            ],
            'banner' => [
                'nullable',
                File::image()->max(3 * 1024),
            ],
            'primary_color' => [
                'required',
                'string',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i',
            ],
            'secondary_color' => [
                'nullable',
                'string',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i',
            ],
            'ticket_price' => [
                'nullable',
                'numeric',
                'min:0',
                'max:1000000',
            ],
            'template_id' => [
                'nullable',
                'integer',
                'min:1',
                'max:255',
            ],
            'badge_id' => [
                'required',
                'integer',
                'min:1',
                'max:255',
            ],
            'attendee_badge_color' => [
                'required',
                'string',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i',
            ],
            'speaker_badge_color' => [
                'required',
                'string',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i',
            ],
            'supervisor_badge_color' => [
                'required',
                'string',
                'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i',
            ],
            'invitation_method' => [
                'required',
                'string',
                Rule::in(InvitationMethod::cases()),
            ],
            'out_app_communication_method' => [
                'required_if:invitation_method,out_app',
                'string',
                Rule::in(OutAppCommunicationMethod::cases()),
            ],
        ];
    }
}
