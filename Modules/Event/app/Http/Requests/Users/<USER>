<?php

declare(strict_types=1);

namespace Modules\Event\Http\Requests\Users;

use App\Http\Requests\BaseFormRequest;

final class EventUserAttendingRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'event_id' => [
                'required',
                'integer',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
            ]
        );
    }
}
