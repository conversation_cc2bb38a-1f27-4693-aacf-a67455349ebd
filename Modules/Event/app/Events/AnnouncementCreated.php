<?php

declare(strict_types=1);

namespace Modules\Event\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

final class AnnouncementCreated
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public int $event_id,
        public string $title,
        public string $content,
        public string $send_method,
        public array $tag_ids,
    ) {}
}
