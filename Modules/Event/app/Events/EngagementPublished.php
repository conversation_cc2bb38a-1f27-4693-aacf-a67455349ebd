<?php

declare(strict_types=1);

namespace Modules\Event\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

final class EngagementPublished
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public function __construct(
        public int $event_id,
        public int $agenda_id,
        public int $engagement_id
    ) {}
}
