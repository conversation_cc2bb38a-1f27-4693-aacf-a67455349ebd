<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements\Polls;

use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Modules\Event\DataTransferObjects\Engagements\Polls\UpdatePollData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Engagement;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Organizer;
use Modules\Event\Enums\AgendaStatus;
use Modules\Event\Enums\EngagementType;
use Modules\Event\Enums\PollType;

final class UpdatePollAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(UpdatePollData $data): Model
    {
        Log::alert($data->toArray());
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        /** @var Event $event */
        $event = Event::query()->findOrFail($data->event_id);

        /** @var Agenda $agenda */
        $agenda = $event->agendas()
            ->active()
            ->findOrFail($data->agenda_id);

        /** @var Engagement $engagement */
        $engagement = $agenda->engagements()->findOrFail($data->poll_id);

        if ($engagement->engagement_type === EngagementType::QUESTIONS->value) {
            throw new LogicalException(__('exceptions.bad_request'));
        }

        if (AgendaStatus::Ended === $agenda->status) {
            throw new LogicalException(__('event::exceptions.sessions.ended'));
        }

        if ($engagement->is_published) {
            throw new LogicalException(__('event::exceptions.engagement.engagement_is_published'));
        }

        if ($engagement->created_by_id !== $organizer->id && $organizer->isSpeaker()) {
            throw new LogicalException(__('event::exceptions.engagement.speaker_not_allowed'));
        }

        if (PollType::OPTIONS->value === $data->poll_type) {

            if (count($data->options) < 2) {
                throw new LogicalException(__('event::exceptions.engagement.poll.options_less_than_two'));
            }
            $engagement->responses()->delete();

            foreach ($data->options as $option) {
                $engagement->responses()->create([
                    'title' => $option,
                    'count' => 0,
                    'type' => PollType::OPTIONS->value,
                ]);
            }
        }

        if (PollType::WORDS->value === $data->poll_type) {

            $engagement->responses()->delete();
        }

        $engagement->update($data->only('title', 'duration', 'poll_type')->toArray());

        return $engagement;
    }
}
