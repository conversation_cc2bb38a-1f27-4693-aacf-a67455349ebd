<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements\Polls;

use App\Exceptions\LogicalException;
use Carbon\Carbon;
use Modules\Event\DataTransferObjects\Engagements\Polls\GetCloudWordsPollResultData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Engagement;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Organizer;
use Modules\Event\Enums\PollType;
use Symfony\Component\HttpFoundation\Response;

final class GetCloudWordsPollResultAction
{
    public function __invoke(GetCloudWordsPollResultData $data): Engagement
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        /** @var Event $event */
        $event = Event::query()->findOrFail($data->event_id);

        /** @var Agenda $agenda */
        $agenda = $event->agendas()->findOrFail($data->agenda_id);

        /** @var Engagement $engagement */
        $engagement = $agenda
            ->engagements()
            ->where('poll_type', PollType::WORDS->value)
            ->with(['responses' => function ($query): void {
                $query->orderBy('count', 'desc');
            }])
            ->findOrFail($data->poll_id);

        if ($engagement->created_by_id !== $organizer->id && $organizer->isSpeaker()) {
            throw new LogicalException(__('event::exceptions.engagement.speaker_not_allowed'));
        }

        if ( ! $engagement->is_published) {
            throw new LogicalException(__('event::exceptions.engagement.engagement_is_not_published'));
        }

        if ( ! Carbon::parse($engagement->end_at)->isPast()) {

            throw new LogicalException(
                __('event::exceptions.engagement.engagement_time_is_not_finish'),
                statusCode: Response::HTTP_NOT_ACCEPTABLE,
                error: [
                    'id' => $engagement->id,
                    'title' => $engagement->title,
                    'start_at' => $engagement->start_at,
                    'end_at' => $engagement->end_at,
                ]
            );
        }

        return $engagement;
    }
}
