<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements\Polls;

use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Modules\Event\DataTransferObjects\Engagements\Polls\StorePollData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Event;
use Modules\Event\Enums\PollType;
use Modules\Event\Enums\ResponseType;

final class StorePollAction
{
    public function __invoke(StorePollData $data): Model
    {

        $organizer = getCurrentOrganizer();

        $event = Event::query()->findOrFail($data->event_id);

        /** @var Agenda $agenda */
        $agenda = $event->agendas()
            ->findOrFail($data->agenda_id);

        if ($agenda->isEnded()) {
            throw new LogicalException(__('event::exceptions.agenda.ended'));
        }

        $data = array_merge($data->toArray(), [
            'created_by_id' => $organizer->id,
        ]);

        $engagement = DB::transaction(function () use ($agenda, $data) {

            $engagement = $agenda->engagements()->create($data);

            if ($data['poll_type'] === PollType::OPTIONS->value) {

                if (count($data['options']) < 2) {
                    throw new LogicalException(__('event::exceptions.engagement.poll.options_less_than_two'));
                }

                foreach ($data['options'] as $option) {
                    $engagement->responses()->create([
                        'title' => $option,
                        'type' => ResponseType::OPTIONS->value,
                        'count' => 0,
                    ]);
                }
            }

            return $engagement;
        });

        return $engagement;
    }
}
