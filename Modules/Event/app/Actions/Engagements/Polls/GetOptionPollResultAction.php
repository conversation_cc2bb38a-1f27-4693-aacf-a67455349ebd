<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements\Polls;

use App\Exceptions\LogicalException;
use Carbon\Carbon;
use Modules\Event\DataTransferObjects\Engagements\Polls\GetOptionPollResultData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Engagement;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Organizer;
use Modules\Event\Entities\Response;
use Modules\Event\Enums\PollType;
use Symfony\Component\HttpFoundation\Response as HttpResponse;

final class GetOptionPollResultAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(GetOptionPollResultData $data): Engagement
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        /** @var Event $event */
        $event = Event::query()->findOrFail($data->event_id);

        /** @var Agenda $agenda */
        $agenda = $event->agendas()->findOrFail($data->agenda_id);

        /** @var Engagement $engagement */
        $engagement = $agenda->engagements()
            ->when($organizer->isSpeaker(), fn ($query) => $query->where('created_by_id', $organizer->id))
            ->where('poll_type', PollType::OPTIONS->value)
            ->with('responses')
            ->withSum('responses', 'count')
            ->findOrFail($data->poll_id);

        $engagement->responses->each(function (Response $response) use ($engagement): void {
            $response->total = $engagement->responses_sum_count;
        });

        if ( ! $engagement->is_published) {
            throw new LogicalException(__('event::exceptions.engagement.engagement_is_not_published'));
        }

        if ( ! Carbon::parse($engagement->end_at)->isPast()) {
            throw new LogicalException(
                __('event::exceptions.engagement.engagement_time_is_not_finish'),
                statusCode: HttpResponse::HTTP_NOT_ACCEPTABLE,
                error: [
                    'id' => $engagement->id,
                    'title' => $engagement->title,
                    'start_at' => $engagement->start_at,
                    'end_at' => $engagement->end_at,
                ]
            );
        }

        return $engagement;
    }
}
