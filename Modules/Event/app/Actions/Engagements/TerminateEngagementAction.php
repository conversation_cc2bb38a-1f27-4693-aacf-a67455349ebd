<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements;

use App\Exceptions\LogicalException;
use Illuminate\Support\Carbon;
use Modules\Event\Entities\Engagement;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Organizer;

final class TerminateEngagementAction
{
    /**
     * @param int $eventId
     * @param int $agendaId
     * @param int $engagementId
     * @return Engagement
     *
     * @throws LogicalException
     */
    public function __invoke(int $eventId, int $agendaId, int $engagementId): Engagement
    {
        $user = Organizer::findOrFail(getCurrentOrganizer()->id);
        $now = Carbon::now();
        $event = Event::findOrFail($eventId);
        $agenda = $event->agendas()
            ->where([
                [
                    'start_at','<=', $now
                ],
                [
                    'end_at','>=', $now
                ]])
            ->findOrFail($agendaId);
        $engagement = $agenda->engagements()
            ->findOrFail($engagementId);

        if ($engagement->created_by_id !== $user->id && $user->isSpeaker()) {
            throw new LogicalException(__('exceptions.speaker_not_allowed'));
        }

        if ( ! $engagement->is_published) {
            throw new LogicalException(__('event::exceptions.engagement.terminate_engagement_is_not_published'));
        }

        $engagement->update(
            [
                'end_at' => $now,
                'is_stopped' => true,
            ]
        );

        return $engagement;
    }
}
