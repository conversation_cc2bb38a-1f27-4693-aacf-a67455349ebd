<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements\Questions;

use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Modules\Event\DataTransferObjects\Engagements\Questions\UpdateQuestionsData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Engagement;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Organizer;
use Modules\Event\Enums\AgendaStatus;
use Modules\Event\Enums\EngagementType;

final class UpdateQuestionsAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(UpdateQuestionsData $data): Model
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        /** @var Event $event */
        $event = Event::query()->findOrFail($data->event_id);

        /** @var Agenda $agenda */
        $agenda = $event->agendas()
            ->active()
            ->findOrFail($data->agenda_id);

        /** @var Engagement $engagement */
        $engagement = $agenda->engagements()->findOrFail($data->question_id);

        if ($engagement->engagement_type === EngagementType::POLL->value) {
            throw new LogicalException(__('exceptions.bad_request'));
        }

        if (AgendaStatus::Ended === $agenda->status) {
            throw new LogicalException(__('event::exceptions.sessions.ended'));
        }

        if ($engagement->is_published) {
            throw new LogicalException(__('event::exceptions.engagement.engagement_is_published'));
        }

        if ($engagement->created_by_id !== $organizer->id && $organizer->isSpeaker()) {
            throw new LogicalException(__('event::exceptions.engagement.speaker_not_allowed'));
        }

        $engagement->update($data->only('title', 'duration')->toArray());

        return $engagement;
    }
}
