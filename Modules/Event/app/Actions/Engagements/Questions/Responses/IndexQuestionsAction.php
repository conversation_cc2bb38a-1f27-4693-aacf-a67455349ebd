<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements\Questions\Responses;

use Illuminate\Database\Eloquent\Model;
use Modules\Event\DataTransferObjects\Engagements\Questions\Responses\IndexQuestionsData;
use Modules\Event\Entities\Event;
use Modules\Event\Enums\AttendeeQuestionStatus;

final class IndexQuestionsAction
{
    public function __invoke(IndexQuestionsData $data): Model
    {
        $event = Event::query()->findOrFail($data->event_id);

        $agenda = $event->agendas()->findOrFail($data->agenda_id);

        $engagement = $agenda->engagements()
            ->with([
                'responses' => fn ($query) => $query->where('status', $data->question_status)->with('createdBy.profile')
            ])
            ->withCount([
                'responses as accepted_questions_count' => fn ($query) => $query->where('status', AttendeeQuestionStatus::ACCEPTED->value),
                'responses as unaccepted_questions_count' => fn ($query) => $query->where('status', AttendeeQuestionStatus::REJECTED->value),
            ])
            ->findOrFail($data->question_id);

        return $engagement;
    }
}
