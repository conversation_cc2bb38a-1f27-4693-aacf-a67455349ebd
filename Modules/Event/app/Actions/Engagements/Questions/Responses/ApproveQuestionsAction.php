<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements\Questions\Responses;

use App\Exceptions\LogicalException;
use Modules\Event\DataTransferObjects\Engagements\Questions\Responses\ApproveQuestionsData;
use Modules\Event\Entities\Event;
use Modules\Event\Enums\AgendaStatus;
use Modules\Event\Enums\AttendeeQuestionStatus;
use Modules\Event\Enums\UserType;

final class ApproveQuestionsAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(ApproveQuestionsData $data): void
    {
        $user = getCurrentOrganizer();

        $event = Event::query()->findOrFail($data->event_id);

        $agenda = $event->agendas()->findOrFail($data->agenda_id);

        $engagement = $agenda->engagements()->findOrFail($data->question_id);

        if ($engagement->created_by_id !== $user->id && $user->type === UserType::SPEAKER->value) {
            throw new LogicalException(__('event::exceptions.engagement.speaker_not_allowed'));
        }

        if ($agenda->status->value === AgendaStatus::Ended->value) {
            throw new LogicalException(__('event::exceptions.sessions.ended'));
        }

        if ($data->accepted_questions) {
            $allowedResponseAcceptIds = $engagement->responses()->pluck('id');
            $invalidResponses = array_diff($data->accepted_questions, $allowedResponseAcceptIds->toArray());

            if ( ! empty($invalidResponses)) {
                throw new LogicalException(__('exceptions.record_not_found'));
            }

            $hasAcceptedResponses = $engagement->responses()
                ->whereIn('id', $data->accepted_questions)
                ->where('status', AttendeeQuestionStatus::ACCEPTED->value)
                ->exists();

            if ($hasAcceptedResponses) {
                throw new LogicalException(__('event::exceptions.engagement.not_allow_to_accept'));
            }

            $engagement->responses()->whereIn(
                'id',
                $data->accepted_questions
            )->update([
                'status' => AttendeeQuestionStatus::ACCEPTED->value,
            ]);
        }
    }
}
