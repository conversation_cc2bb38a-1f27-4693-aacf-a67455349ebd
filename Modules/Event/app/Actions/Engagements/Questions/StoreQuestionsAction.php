<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements\Questions;

use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Modules\Event\DataTransferObjects\Engagements\Questions\StoreQuestionsData;
use Modules\Event\Entities\Event;

final class StoreQuestionsAction
{
    public function __invoke(StoreQuestionsData $data): Model
    {
        $organizer = getCurrentOrganizer();

        $event = Event::query()->findOrFail($data->event_id);

        $agenda = $event->agendas()
            ->findOrFail($data->agenda_id);

        if ($agenda->isEnded()) {
            throw new LogicalException(__('event::exceptions.agenda.ended'));
        }

        $data = array_merge($data->toArray(), [
            'created_by_id' => $organizer->id,
        ]);

        $engagement = $agenda->engagements()->create($data);

        return $engagement;
    }
}
