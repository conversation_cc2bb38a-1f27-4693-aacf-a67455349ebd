<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements;

use Illuminate\Database\Eloquent\Model;
use Modules\Event\DataTransferObjects\Engagements\IndexEngagementData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Organizer;
use Modules\Event\Enums\EngagementType;
use Modules\Event\Enums\ResponseType;

final class IndexEngagementAction
{
    public function __invoke(IndexEngagementData $data): Model
    {
        /** @var Organizer $organizer */
        $organizer = Organizer::query()->findOrFail(getCurrentOrganizer()->id);

        /** @var Event $event */
        $event = $organizer->eventAccess()->findOrFail($data->event_id);

        /** @var Agenda $agenda */
        $agenda = $event->agendas()->with([
            'engagements' => fn ($query) => $query
                ->when($data->type, fn ($q) => $q->where('engagement_type', $data->type))
                ->when($organizer->isSpeaker(), fn ($query) => $query->where('created_by_id', $organizer->id))
                ->with(['responses' => function ($query): void {
                    $query->where('type', ResponseType::OPTIONS->value);
                }])
        ])->withCount([
            'engagements as engagements_count' => fn ($query) => $query->when($organizer->isSpeaker(), fn ($query) => $query->where('created_by_id', $organizer->id)),
            'engagements as polls_count' => fn ($query) => $query->where('engagement_type', EngagementType::POLL)
                ->when($organizer->isSpeaker(), fn ($query) => $query->where('created_by_id', $organizer->id)),
            'engagements as questions_count' => fn ($query) => $query->where('engagement_type', EngagementType::QUESTIONS)
                ->when($organizer->isSpeaker(), fn ($query) => $query->where('created_by_id', $organizer->id)),
        ])->findOrFail($data->agenda_id);

        return $agenda;
    }
}
