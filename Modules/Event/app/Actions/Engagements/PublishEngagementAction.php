<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements;

use App\Exceptions\LogicalException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Modules\Event\Entities\Engagement;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Organizer;
use Modules\Event\Enums\AgendaStatus;
use Modules\Event\Events\EngagementPublished;

final class PublishEngagementAction
{
    /**
     * @param int $eventId
     * @param int $agendaId
     * @param int $engagementId
     * @return Engagement
     *
     * @throws LogicalException
     */
    public function __invoke(int $eventId, int $agendaId, int $engagementId): Engagement
    {
        $now = Carbon::now();

        $user = Organizer::findOrFail(getCurrentOrganizer()->id);

        $event = Event::findOrFail($eventId);
        $agenda = $event->agendas()->findOrFail($agendaId);

        if (AgendaStatus::Ended === $agenda->status) {
            throw new LogicalException(__('event::messages.agenda_ended'));
        }

        if (AgendaStatus::Upcoming === $agenda->status) {
            throw new LogicalException(__('event::messages.agenda_upcoming'));
        }

        $engagement = $agenda->engagements()->findOrFail($engagementId);

        if ($engagement->created_by_id !== $user->id && $user->isSpeaker()) {
            throw new LogicalException(__('exceptions.speaker_not_allowed'));
        }

        if ($engagement->is_published) {
            throw new LogicalException(__('event::messages.engagement_already_published'));
        }

        $engagementEndAt = $now->copy()->addSeconds($engagement->duration);

        if ($engagementEndAt->isAfter($agenda->end_at)) {
            throw new LogicalException(__('event::messages.engagement_exceeds_agenda_time'));
        }

        Log::info('$now:', [$now]);
        Log::info('$engagementEndAt:', [$engagementEndAt]);

        $engagement->update(
            [
                'is_published' => true,
                'start_at' => $now,
                'end_at' => $engagementEndAt,
            ]
        );

        EngagementPublished::dispatch($event->id, $agenda->id, $engagement->id);

        return $engagement;
    }
}
