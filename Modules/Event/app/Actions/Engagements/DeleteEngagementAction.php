<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Engagements;

use App\Exceptions\LogicalException;
use Carbon\Carbon;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Engagement;
use Modules\Event\Entities\Event;
use Modules\Event\Enums\AgendaStatus;
use Modules\Event\Enums\PollType;
use Modules\Event\Enums\UserType;

final class DeleteEngagementAction
{
    public function __invoke(array $data): void
    {
        $user = getCurrentOrganizer();

        $event = Event::query()->findOrFail($data['event_id']);

        /** @var Agenda $agenda */
        $agenda = $event->agendas()->findOrFail($data['agenda_id']);

        /** @var Engagement $engagement */
        $engagement = $agenda->engagements()->findOrFail($data['engagement_id']);

        if ($engagement->created_by_id !== $user->id && $user->type === UserType::SPEAKER->value) {
            throw new LogicalException(__('event::exceptions.engagement.speaker_not_allowed'));
        }

        if (AgendaStatus::Ended === $agenda->status) {
            throw new LogicalException(__('event::exceptions.sessions.ended'));
        }

        if ($engagement->is_published) {
            throw new LogicalException(__('event::exceptions.engagement.cannot_delete_published_engagement'));
        }

        $is_engagement_ended = $engagement->end_at ? Carbon::parse($engagement->end_at)->isPast() : false ;

        if ($is_engagement_ended) {
            throw new LogicalException(__('event::exceptions.engagement.engagement_has_ended'));
        }

        if ($engagement->poll_type === PollType::OPTIONS->value) {
            $engagement->responses()->delete();
        }

        $engagement->delete();
    }
}
