<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Agendas;

use App\Exceptions\LogicalException;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Modules\Event\DataTransferObjects\Agendas\StoreAgendaData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Event;
use Modules\Event\Events\AgendaNotificationEnabled;
use Spatie\LaravelData\Optional;

final class StoreAgendaAction
{
    public function __invoke(StoreAgendaData $data): Model
    {
        return DB::transaction(function () use ($data) {
            $event = Event::query()
                ->where('organization_id', getCurrentOrganizer()->organization_id)
                ->findOrFail($data->event_id);

            $settings = $event->settings;

            if ( ! $settings?->is_notification_enabled) {
                $data->is_notification_enabled = false;
            }

            $agenda_data = $data->except('speaker_ids', 'supervisor_ids')->toArray();

            if ($data->seats_number) {
                $agenda_data['seats_available'] = $data->seats_number;
            }

            $agenda = Agenda::create($agenda_data);

            if ( ! $event->can_have_speaker && ! $data->speaker_ids instanceof Optional) {
                throw new LogicalException(__('exceptions.agenda.speaker_not_allowed'));
            }

            if ($event->can_have_speaker && ! $data->speaker_ids instanceof Optional) {
                $allowedSpeakerIds = $event->invitedSpeakers()->pluck('id');
                $invalidSpeakers = array_diff($data->speaker_ids, $allowedSpeakerIds->toArray());

                if ( ! empty($invalidSpeakers)) {
                    throw new LogicalException(__('exceptions.agenda.speaker_not_found'));
                }

                $agenda->speakers()->sync($data->speaker_ids);
            }

            if ( ! $event->can_have_supervisor && ! $data->supervisor_ids instanceof Optional) {
                throw new LogicalException(__('exceptions.agenda.supervisor_not_allowed'));
            }

            if ($event->can_have_supervisor && ! $data->supervisor_ids instanceof Optional) {
                $allowedSupervisorIds = $event->invitedSupervisors()->pluck('id');
                $invalidSupervisors = array_diff($data->supervisor_ids, $allowedSupervisorIds->toArray());

                if ( ! empty($invalidSupervisors)) {
                    throw new LogicalException(__('exceptions.agenda.supervisor_not_found'));
                }

                $agenda->supervisors()->sync($data->supervisor_ids);
            }

            if ($agenda->is_notification_enabled) {
                AgendaNotificationEnabled::dispatch($event->id, $agenda->id, $agenda->start_at, $settings->reminder_period);
                $agenda->update([
                    'reminder_scheduled_at' => Carbon::parse($agenda->start_at)->subMinutes($settings->reminder_period),
                ]);
            }

            return $agenda->fresh();
        });
    }
}
