<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Agendas;

use App\Exceptions\LogicalException;
use Modules\Event\Entities\Event;

final class DeleteAgendaAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(int $id, int $agenda_id): void
    {
        $event = Event::query()->findOrFail($id);

        $agenda = $event->agendas()->findOrFail($agenda_id);

        if ( ! $agenda->isUpComing()) {
            throw new LogicalException(__('event::messages.delete_agenda'));
        }

        $agenda->speakers()->detach();
        $agenda->attendees()->detach();
        $agenda->delete();
    }
}
