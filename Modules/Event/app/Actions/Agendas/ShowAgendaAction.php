<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Agendas;

use Illuminate\Database\Eloquent\Model;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Organizer;
use Modules\Event\Entities\User;
use Modules\Event\Enums\OrganizerType;

final class ShowAgendaAction
{
    public function __invoke(int $id, int $agenda_id): Model
    {
        $organizer = getCurrentOrganizer();

        if (OrganizerType::SPEAKER === $organizer->type) {
            /** @var User $speaker */
            $speaker = Organizer::query()->findOrFail($organizer->id)->speaker;

            return $speaker->agendas()
                ->withCount('attendees as attendees_count')
                ->with(['speakers.profile', 'supervisors.profile'])
                ->findOrFail($agenda_id);
        }
        $event = Event::query()->findOrFail($id);

        $agenda = $event->agendas()
            ->withCount('attendees as attendees_count')
            ->with(['speakers.profile', 'supervisors.profile'])
            ->findOrFail($agenda_id);

        return $agenda;
    }
}
