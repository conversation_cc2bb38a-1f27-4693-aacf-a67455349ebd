<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Agendas;

use App\Exceptions\LogicalException;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Modules\Event\DataTransferObjects\Agendas\UpdateAgendaData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Event;
use Modules\Event\Enums\AgendaStatus;
use Modules\Event\Events\AgendaNotificationEnabled;
use Spatie\LaravelData\Optional;

final class UpdateAgendaAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(UpdateAgendaData $data): Model
    {
        /** @var Event $event */
        $event = Event::query()
            ->where('organization_id', getCurrentOrganizer()->organization_id)
            ->findOrFail($data->event_id);

        $settings = $event->settings;

        /** @var Agenda $agenda */
        $agenda = $event->agendas()->findOrFail($data->agenda_id);

        if (AgendaStatus::Upcoming !== $agenda->status) {
            throw new LogicalException(__('event::exceptions.agenda.not_upcoming'));
        }

        if ( ! $settings?->is_notification_enabled) {
            $data->is_notification_enabled = false;
        }

        $agenda->update($data->except('speaker_ids', 'event_id', 'agenda_id')->toArray());

        if ($event->can_have_speaker) {
            $speakers_ids = $event->invitedSpeakers()->pluck('id')->toArray();

            foreach ($data->speaker_ids ?? [] as $id) {
                if ( ! in_array($id, $speakers_ids)) {
                    throw new LogicalException(__('event::exceptions.agenda.speaker_not_found'));
                }
            }

            if ( ! $data->speaker_ids instanceof Optional) {
                $ids = $data->speaker_ids ?? [];
                $agenda->speakers()->sync($ids);
            }
        }

        if ($event->can_have_supervisor) {
            $supervisors_ids = $event->invitedSupervisors()->pluck('id')->toArray();

            foreach ($data->supervisors_ids ?? [] as $id) {
                if ( ! in_array($id, $supervisors_ids)) {
                    throw new LogicalException(__('event::exceptions.agenda.supervisor_not_found'));
                }
            }

            if ( ! $data->supervisors_ids instanceof Optional) {
                $ids = $data->supervisors_ids ?? [];
                $agenda->supervisors()->sync($ids);
            }
        }

        if (
            $agenda->wasChanged(['is_notification_enabled', 'start_at'])
            && $agenda->is_notification_enabled
            && $agenda->reminder_scheduled_at !== Carbon::parse($agenda->start_at)->subMinutes($settings->reminder_period)
            && Carbon::parse($agenda->start_at)->subMinutes($settings->reminder_period)->isFuture()
        ) {
            AgendaNotificationEnabled::dispatch($event->id, $agenda->id, $agenda->start_at, $settings->reminder_period);
            $agenda->update(['reminder_scheduled_at' => Carbon::parse($agenda->start_at)->subMinutes($settings->reminder_period)]);
        }

        return $agenda;
    }
}
