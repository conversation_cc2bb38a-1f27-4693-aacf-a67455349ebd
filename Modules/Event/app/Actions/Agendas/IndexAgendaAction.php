<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Agendas;

use Illuminate\Support\Collection;
use Modules\Event\DataTransferObjects\Agendas\IndexAgendaData;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Organizer;
use Modules\Event\Enums\UserType;

final class IndexAgendaAction
{
    public function __invoke(IndexAgendaData $data): Collection
    {
        $auth = getCurrentOrganizer();

        $event = Event::query()
            ->where('organization_id', $auth->organization_id)
            ->findOrFail($data->event_id);

        /** @var Organizer $organizer */
        $organizer = Organizer::query()->findOrFail($auth->id);

        $agendas = $event->agendas()->with([
            'speakers.profile',
            'supervisors.profile'
        ])->when(
            UserType::SPEAKER->value === $organizer->type,
            function ($query) use ($organizer) {
                return $query->whereHas('speakers', function ($query) use ($organizer): void {
                    $query->where('user_id', $organizer->speaker->id);
                });
            }
        )->when($data->search, function ($query) use ($data): void {
            $query->where('title', 'ilike', '%' . $data->search . '%');
        })->get();

        return $agendas;
    }
}
