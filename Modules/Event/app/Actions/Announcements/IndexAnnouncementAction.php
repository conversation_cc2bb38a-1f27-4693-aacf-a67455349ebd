<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Announcements;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\Announcements\IndexAnnouncementData;
use Modules\Event\Entities\Event;

final class IndexAnnouncementAction
{
    public function __invoke(IndexAnnouncementData $data): LengthAwarePaginator
    {
        $event = Event::query()->where(
            'organization_id',
            getCurrentOrganizer()->organization_id
        )->findOrFail(
            $data->event_id
        );

        return $event->announcements()->with([
            'tags',
        ])->when($data->search, function ($query) use ($data): void {
            $query->where('title_ar', 'ilike', '%' . $data->search . '%');
        })->orderBy(
            'created_at',
            $data->sort
        )->paginate(
            $data->per_page
        );
    }
}
