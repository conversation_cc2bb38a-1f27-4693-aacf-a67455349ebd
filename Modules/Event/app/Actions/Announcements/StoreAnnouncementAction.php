<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Announcements;

use App\Enums\Http;
use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Modules\Event\DataTransferObjects\Announcements\StoreAnnouncementData;
use Modules\Event\Entities\Event;
use Modules\Event\Enums\InvitationMethod;
use Modules\Event\Events\AnnouncementCreated;

final class StoreAnnouncementAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(StoreAnnouncementData $data): Model
    {
        $event = Event::query()->with(
            'settings'
        )->findOrFail($data->event_id);

        if ($event->settings->invitation_method === InvitationMethod::OUT_APP->value) {
            $now = now();
            $eventStart = Carbon::parse($event->start_at);
            $eventEnd = Carbon::parse($event->end_at);

            $start = $eventStart->copy()->subHours(24);
            $end = $eventEnd->copy()->addHours(48);

            $isWithinNotificationWindow = $now->between($start, $end);

            if ( ! $isWithinNotificationWindow) {
                Log::error('Skipped notification: outside allowed window', [
                    'event_id' => $event->id,
                    'now' => $now,
                    'event_start' => $eventStart,
                    'event_end' => $eventEnd,
                    'notification_window_start' => $start,
                    'notification_window_end' => $end,
                    'is_within_notification_window' => $isWithinNotificationWindow,
                ]);

                throw new LogicalException(
                    __('event::exceptions.announcement.outside_allowed_window'),
                    Http::UNPROCESSABLE_ENTITY->value,
                );
            }
        }

        $announcement = $event->announcements()->create($data->toArray());

        $announcement->tags()->attach($data->tag_ids);

        AnnouncementCreated::dispatch(
            $event->id,
            $announcement->title_ar,
            $announcement->content_ar,
            $announcement->send_method,
            $announcement->tags->modelKeys()
        );

        return $announcement;
    }
}
