<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Platform\Events\Wizard;

use Illuminate\Http\UploadedFile;
use Modules\Event\Actions\Tags\CreateDefaultTagsAction;
use Modules\Event\DataTransferObjects\Platform\Events\Wizard\StoreWizardData;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\EventSetting;
use Modules\Event\Enums\EventType;
use Modules\Event\Enums\InvitationMethod;

final class StoreWizardAction
{
    /**
     * Handle the incoming data.
     */
    public function __invoke(StoreWizardData $data, int $id): EventSetting
    {
        $user = getCurrentOrganizer();

        $event = Event::query()->where(
            'organization_id',
            $user->organization_id
        )->findOrFail($id);

        $attributes = [];

        if ($data->logo instanceof UploadedFile) {
            $attributes['logo'] = storeFile($data->logo, 'events/logos');
        }

        if ($data->banner instanceof UploadedFile) {
            $attributes['banner'] = storeFile($data->banner, 'events/banners');
        }

        $dataArray = $data->except('logo', 'banner')->toArray();

        if ($data->invitation_method === InvitationMethod::IN_APP->value) {
            unset($dataArray['out_app_communication_method']);
        }

        if ($event->type === EventType::PRIVATE->value) {
            unset($dataArray['template_id'], $dataArray['ticket_price']);

        } elseif ($event->type === EventType::PUBLIC->value) {
            $dataArray['ticket_price'] ??= 0;
            $dataArray['template_id'] ??= 1;
        }

        $event->settings()->updateOrCreate(
            attributes: ['event_id' => $event->id],
            values: $dataArray + $attributes,
        );

        app(CreateDefaultTagsAction::class)($event->refresh());

        return $event->settings;
    }
}
