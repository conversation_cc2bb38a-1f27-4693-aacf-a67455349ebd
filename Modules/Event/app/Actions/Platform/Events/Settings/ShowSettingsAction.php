<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Platform\Events\Settings;

use App\Exceptions\LogicalException;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\EventSetting;

final class ShowSettingsAction
{
    /**
     * Handle the incoming data.
     *
     * @param  int $id
     *
     * @throws LogicalException
     * @return EventSetting
     */
    public function __invoke(int $id): EventSetting
    {
        $user = getCurrentOrganizer();

        $event = Event::query()->where('organization_id', $user->organization_id)
            ->findOrFail($id);

        $eventSettings = $event->settings;

        if (null === $eventSettings) {
            throw new LogicalException(__('event::exceptions.settings_not_found'), 404);
        }

        return $eventSettings;
    }
}
