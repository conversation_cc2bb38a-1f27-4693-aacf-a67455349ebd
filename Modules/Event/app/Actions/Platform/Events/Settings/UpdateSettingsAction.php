<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Platform\Events\Settings;

use App\Exceptions\LogicalException;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Modules\Event\DataTransferObjects\Platform\Events\Settings\UpdateSettingsData;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\EventSetting;
use Modules\Event\Enums\EventType;
use Modules\Event\Events\AgendaNotificationEnabled;

final class UpdateSettingsAction
{
    /**
     * Handle the incoming data.
     *
     * @param  UpdateSettingsData  $data
     * @param int $id
     *
     * @throws LogicalException
     * @return EventSetting
     */
    public function __invoke(UpdateSettingsData $data, int $id): EventSetting
    {
        $user = getCurrentOrganizer();

        $event = Event::query()->where(
            'organization_id',
            $user->organization_id
        )->findOrFail($id);

        $setting = $event->settings;

        if (null === $setting) {
            throw new LogicalException(
                message: __('event::exceptions.settings_not_found'),
                statusCode: 404
            );
        }

        $attributes = [];

        if ($data->logo instanceof UploadedFile) {
            $attributes['logo'] = storeFile($data->logo, 'events/logos');
        }

        if ($data->banner instanceof UploadedFile) {
            $attributes['banner'] = storeFile($data->banner, 'events/banners');
        }

        $updateData = $data->except('logo', 'banner')->toArray();

        if ($event->type === EventType::PRIVATE->value) {
            // For private events, remove template_id and ticket_price if they exist
            unset($updateData['template_id'], $updateData['ticket_price']);

        } elseif ($event->type === EventType::PUBLIC->value) {
            // For public events, set default values if not provided
            $updateData['ticket_price'] ??= 0;
            $updateData['template_id'] ??= 1;
        }

        $setting->update($updateData + $attributes);

        if ($setting->wasChanged(['reminder_period']) && $setting->is_notification_enabled) {
            $event->agendas()->update(['is_notification_enabled' => true]);

            $event->agendas->each(function ($agenda) use ($event, $setting): void {
                if (
                    $agenda->reminder_scheduled_at !== Carbon::parse($agenda->start_at)->subMinutes($setting->reminder_period)
                    && Carbon::parse($agenda->start_at)->subMinutes($setting->reminder_period)->isFuture()
                ) {
                    AgendaNotificationEnabled::dispatch($event->id, $agenda->id, $agenda->start_at, $setting->reminder_period);
                    $agenda->update(['reminder_scheduled_at' => Carbon::parse($agenda->start_at)->subMinutes($setting->reminder_period)]);
                }
            });
        }

        return $setting->fresh();
    }
}
