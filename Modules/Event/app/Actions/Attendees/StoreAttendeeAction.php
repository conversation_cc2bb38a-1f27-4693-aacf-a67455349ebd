<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Attendees;

use Illuminate\Database\Eloquent\Model;
use Modules\Event\Contracts\UserCommunicatorContract;
use Modules\Event\DataTransferObjects\Attendees\StoreAttendeeData;

final class StoreAttendeeAction
{
    public function __invoke(StoreAttendeeData $data): Model
    {
        return app(UserCommunicatorContract::class)::storeAttendee($data);
    }
}
