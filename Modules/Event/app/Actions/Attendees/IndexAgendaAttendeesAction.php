<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Attendees;

use Modules\Event\Entities\Event;

final class IndexAgendaAttendeesAction
{
    public function __invoke(int $event_id, int $agenda_id): array
    {
        $event = Event::query()
            ->where('organization_id', getCurrentOrganizer()->organization_id)
            ->findOrFail($event_id);

        $agenda = $event->agendas()
            ->withCount('attendees')
            ->findOrFail($agenda_id);

        $attendees = $agenda->attendees()
            ->with('profile')
            ->get();

        return [
            'attendees_count' => $agenda->attendees_count,
            'seats_available' => $agenda->seats_available,
            'attendees' => $attendees
        ];
    }
}
