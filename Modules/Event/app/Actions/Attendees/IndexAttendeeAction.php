<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Attendees;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Event\Contracts\UserCommunicatorContract;
use Modules\Event\DataTransferObjects\Attendees\IndexAttendeeData;

final class IndexAttendeeAction
{
    public function __invoke(IndexAttendeeData $data): LengthAwarePaginator
    {
        return app(UserCommunicatorContract::class)::indexAttendee($data);
    }
}
