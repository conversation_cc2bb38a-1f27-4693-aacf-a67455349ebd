<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Users;

use App\Exceptions\LogicalException;
use Modules\Event\DataTransferObjects\Users\EventUserAttendingData;
use Modules\Event\Entities\CheckIn;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Invitation;
use Modules\Event\Enums\InvitationStatus;

final class EventUserAttendingAction
{
    public function __invoke(EventUserAttendingData $data): void
    {
        $event = Event::query()->findOrFail($data->event_id);

        $user = $event->invitedUsers()->findOrFail(getCurrentUser()->id);

        $is_checked_in = Invitation::query()->where([
            ['user_id', $user->id],
            ['event_id', $event->id],
            ['status', InvitationStatus::CHECKED_IN->value],
        ])->exists();

        if ($is_checked_in) {
            if ($this->hasCheckedInToday($user->id, $event->id)) {
                throw new LogicalException(__('event::exceptions.checked_in.already_check_in'));
            }

            $this->recordCheckIn($user->id, $event->id);

            return;
        }

        $event->invitedUsers()->updateExistingPivot(
            $user->id,
            [
                'status' => InvitationStatus::CHECKED_IN,
                'checked_in_at' => now(),
            ]
        );
        $this->recordCheckIn($user->id, $event->id);
    }

    public function recordCheckIn(string|int $user_id, string|int $event_id): void
    {
        CheckIn::query()->create([
            'user_id' => $user_id,
            'event_id' => $event_id,
            'checked_in_at' => now(),
        ]);
    }

    private function hasCheckedInToday(string|int $user_id, string|int $event_id): bool
    {
        return CheckIn::query()
            ->where('user_id', $user_id)
            ->where('event_id', $event_id)
            ->whereDate('checked_in_at', now())
            ->exists();
    }
}
