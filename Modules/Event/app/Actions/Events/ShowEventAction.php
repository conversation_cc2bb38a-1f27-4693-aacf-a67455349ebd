<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Events;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Modules\Event\Entities\Event;

final class ShowEventAction
{
    public function __invoke(int $id): Builder|Model|Event
    {
        $user = getCurrentOrganizer();

        return Event::query()->with([
            'category',
        ])->where(
            'organization_id',
            $user->organization_id
        )
            ->withExists('settings as has_settings')
            ->findOrFail($id);
    }
}
