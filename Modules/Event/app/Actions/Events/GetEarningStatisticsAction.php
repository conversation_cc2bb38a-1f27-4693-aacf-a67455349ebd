<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Events;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Event\Entities\Event;
use Modules\Event\Enums\RegistrationSource;
use Modules\User\Enums\UserType;

final class GetEarningStatisticsAction
{
    public function __invoke(int $eventId): array
    {
        $event = Event::query()
            ->with(['settings', 'invitedUsers' => function ($query): void {
                $query->wherePivot('registration_source', RegistrationSource::SELF_REGISTERED->value)
                    ->wherePivot('type', UserType::ATTENDEE->value)
                    ->withPivot(['created_at', 'registration_source']);
            }])
            ->findOrFail($eventId);

        // Check if event has ticket price > 0
        if ( ! $event->settings || $event->settings->ticket_price <= 0) {
            return [
                'total_tickets_sold' => 0,
                'total_income' => 0,
                'max_income' => 0,
                'first_date' => null,
                'last_date' => null,
                'income_details' => [],
            ];
        }

        $selfRegisteredAttendees = $event->invitedUsers;
        $ticketPrice = $event->settings->ticket_price;

        // Calculate total tickets sold and income
        $totalTicketsSold = $selfRegisteredAttendees->count();
        $totalIncome = $totalTicketsSold * $ticketPrice;

        // Calculate income details by day
        $incomeDetails = $this->calculateIncomeDetails($selfRegisteredAttendees, $ticketPrice);

        // Calculate max income from daily income details
        $maxIncome = 0;

        if ( ! empty($incomeDetails)) {
            $maxIncome = max(array_column($incomeDetails, 'income'));
        }

        // Get first and last registration dates
        $firstDate = null;
        $lastDate = null;

        if ($selfRegisteredAttendees->isNotEmpty()) {
            $dates = $selfRegisteredAttendees->pluck('pivot.created_at')->map(fn ($date) => Carbon::parse($date)->format('Y-m-d'))->sort();
            $firstDate = $dates->first();
            $lastDate = $dates->last();
        }

        return [
            'total_tickets_sold' => $totalTicketsSold,
            'total_income' => $totalIncome,
            'max_income' => $maxIncome,
            'first_date' => $firstDate,
            'last_date' => $lastDate,
            'income_details' => $incomeDetails,
        ];
    }

    private function calculateIncomeDetails(Collection $attendees, float $ticketPrice): array
    {
        if ($attendees->isEmpty()) {
            return [];
        }

        // Group attendees by registration date
        $attendeesByDate = $attendees->groupBy(fn ($attendee) => Carbon::parse($attendee->pivot->created_at)->format('Y-m-d'));

        // Get date range from first to last registration
        $dates = $attendeesByDate->keys()->sort();
        $startDate = Carbon::parse($dates->first());
        $endDate = Carbon::parse($dates->last());

        $incomeDetails = [];

        // Generate data for each day in the range
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $dateString = $date->format('Y-m-d');
            $ticketsSoldOnDate = $attendeesByDate->get($dateString, collect())->count();
            $incomeOnDate = $ticketsSoldOnDate * $ticketPrice;

            $incomeDetails[] = [
                'date' => $dateString,
                'tickets_sold' => $ticketsSoldOnDate,
                'income' => $incomeOnDate,
            ];
        }

        return $incomeDetails;
    }
}
