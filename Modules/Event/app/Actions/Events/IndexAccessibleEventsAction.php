<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Events;

use Illuminate\Database\Eloquent\Collection;
use Modules\Event\Entities\Organizer;

final class IndexAccessibleEventsAction
{
    public function __invoke(): Collection
    {
        /** @var Organizer */
        $organizer = getCurrentOrganizer();
        $accessible_events = $organizer->eventAccess()->select(['id', 'name_ar', 'name_en'])->where('end_at', '>=', now())->get();

        return  $accessible_events;
    }
}
