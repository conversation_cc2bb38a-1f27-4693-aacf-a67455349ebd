<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Events\Statistics\General;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Modules\Event\Entities\Event;

final class SessionsAction
{
    public function __invoke(Request $request, int $id): LengthAwarePaginator
    {
        // Get sort direction and per page from query parameters
        $sort = $request->query('sort', 'desc'); // 'desc' or 'asc'
        $perPage = $request->query('per_page', 10);

        $user = getCurrentOrganizer();

        $event = Event::query()->where(
            'organization_id',
            $user->organization_id
        )->with([
            'agendas',
        ])->findOrFail($id);

        // Query agendas with counts, sorted and paginated
        return $event->agendas()->withCount([
            'attendees as total_attendees',
            'engagements as total_engagements',
        ])->orderBy(
            'total_attendees',
            $sort,
        )->paginate($perPage);
    }
}
