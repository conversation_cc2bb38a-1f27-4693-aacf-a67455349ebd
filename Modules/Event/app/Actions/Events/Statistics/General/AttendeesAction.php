<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Events\Statistics\General;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Modules\Event\Entities\Event;
use Modules\Event\Enums\InvitationStatus;

final class AttendeesAction
{
    public function __invoke(int $id): Builder|Model|Event
    {
        return Event::query()->select(
            'id'
        )->withCount([
            'invitedAttendees AS total_attendees',
            'invitedAttendees as total_checked_in_attendees' => fn (Builder $query) => $query->where('invitation.status', InvitationStatus::CHECKED_IN->value),
            'invitedAttendees as total_absent_attendees' => fn (Builder $query) => $query->where('invitation.status', InvitationStatus::PENDING->value),
        ])->findOrFail($id);
    }
}
