<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Events\Statistics\General;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Modules\Event\Entities\Event;

final class SpeakersAction
{
    public function __invoke(Request $request, int $id): LengthAwarePaginator
    {
        $sort = $request->query('sort', 'desc'); // 'desc' or 'asc'
        $perPage = $request->query('per_page', 10);

        $event = Event::query()->findOrFail($id);

        return $event->invitedSpeakers()->with(
            'profile'
        )->withCount([
            'speakerSessions as total_sessions',
        ])->orderBy(
            'total_sessions',
            $sort,
        )->paginate($perPage);
    }
}
