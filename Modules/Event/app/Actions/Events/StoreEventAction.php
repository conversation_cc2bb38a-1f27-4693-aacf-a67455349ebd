<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Events;

use Modules\Event\DataTransferObjects\StoreEventData;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Organizer;
use Modules\Event\Enums\EventType;

final class StoreEventAction
{
    /**
     * Handle the incoming data.
     *
     * @param  StoreEventData  $data
     *
     * @return Event
     */
    public function __invoke(StoreEventData $data): Event
    {
        /** @var Organizer $organizer */
        $organizer = getCurrentOrganizer();

        $event = Event::query()->create(
            attributes: $data->toArray() + [
                'organization_id' => $organizer->organization_id,
                'type' => $data->is_private ? EventType::PRIVATE : EventType::PUBLIC,
            ]
        );

        $organizer->eventAccess()->syncWithoutDetaching([$event->id]);

        $organization = $organizer->organization;

        $owner = $organization->owner;

        if ($owner->id !== $organizer->id) {
            $owner->eventAccess()->syncWithoutDetaching([$event->id]);
        }

        return $event->fresh();
    }
}
