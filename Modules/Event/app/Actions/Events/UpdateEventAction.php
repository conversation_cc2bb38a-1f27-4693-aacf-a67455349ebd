<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Events;

use App\Exceptions\LogicalException;
use Carbon\Carbon;
use Modules\Event\DataTransferObjects\UpdateEventData;
use Modules\Event\Entities\Event;
use Spatie\LaravelData\Optional;

final class UpdateEventAction
{
    /**
     * Handle the incoming data.
     *
     * @param UpdateEventData $data
     * @param int $id
     * @return Event
     * @throws LogicalException
     */
    public function __invoke(UpdateEventData $data, int $id): Event
    {
        $owner = getCurrentOrganizer();

        $event = Event::query()->where(
            'organization_id',
            $owner->organization_id
        )->findOrFail($id);

        if ( ! $data->end_at instanceof Optional) {
            $end_at = Carbon::parse($data->end_at);

            if ($end_at <= $event->start_at) {
                throw new LogicalException(__('event::exceptions.invalid_ending_time'));
            }
        }

        if ( ! $data->start_at instanceof Optional && $event->isCurrent()) {
            throw new LogicalException(__('event::exceptions.start_time_can_not_updated'));
        }

        $event->update(
            $data->toArray()
        );

        return $event->fresh();
    }
}
