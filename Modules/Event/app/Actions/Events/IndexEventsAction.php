<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Events;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\Events\IndexEventsData;
use Modules\Event\Entities\Event;
use Modules\Event\Enums\EventStatus;

final class IndexEventsAction
{
    public function __invoke(IndexEventsData $data): LengthAwarePaginator
    {
        return Event::query()->with(
            relations: 'category'
        )->withExists(
            relation: 'settings as has_settings'
        )->whereHas(
            'organizerAccess',
            fn ($query) => $query->where('organizer_id', getCurrentOrganizer()->id)
        )->when(
            $data->order_by,
            fn ($query) => $query->orderBy($data->order_by, $data->sort)
        )->when(
            $data->search,
            fn ($query) => $query->where(fn ($query) => $query->where(
                'name_en',
                'ilike',
                '%' . $data->search . '%'
            )->orWhere(
                'name_ar',
                'ilike',
                '%' . $data->search . '%'
            ))
        )->when(
            $data->check_in_type,
            fn ($query) => $query->where('check_in_type', $data->check_in_type)
        )->when(
            $data->category_id,
            fn ($query) => $query->where('category_id', $data->category_id)
        )->when(
            $data->status,
            function ($query) use ($data) {
                return match ($data->status) {
                    EventStatus::UPCOMING->value => $query->where('start_at', '>=', now()),
                    EventStatus::CURRENT->value => $query->where('start_at', '<=', now())->where('end_at', '>=', now()),
                    default => $query->where('end_at', '<=', now()),
                };
            }
        )->when(
            $data->date_from || $data->date_to,
            function ($query) use ($data) {
                if ($data->date_from) {
                    $query->where(function ($q) use ($data): void {
                        $q->where('start_at', '>=', $data->date_from->startOfDay())
                            ->orWhere('end_at', '>=', $data->date_from->startOfDay());
                    });
                }

                if ($data->date_to) {
                    $query->where('end_at', '<=', $data->date_to->endOfDay());
                }

                return $query;
            }
        )->paginate($data->records_per_page);
    }
}
