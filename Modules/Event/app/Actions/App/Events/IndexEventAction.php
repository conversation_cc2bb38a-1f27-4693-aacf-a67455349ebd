<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\App\Events\IndexEventData;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\User;

final class IndexEventAction
{
    public function __invoke(IndexEventData $data): LengthAwarePaginator
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        return  Event::query()->notEnded()
            ->with([
                'settings',
                'checkIns' => fn ($query) => $query->where('user_id', $user->id)->latest()->limit(1),
                'invitations' => fn ($query) => $query->where('user_id', $user->id)->latest()->limit(1)
            ])
            ->whereHas('invitedUsers', function (Builder $query) use ($data): void {
                $query->where('id', $data->user_id);
            })
            ->withExists(['materials as has_materials' => fn ($query) => $query->where('is_published', true)])
            ->paginate($data->records_per_page);
    }
}
