<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Attendees\Events;

use App\Exceptions\LogicalException;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\App\Events\Attendees\IndexAttendeesData;
use Modules\Event\Entities\User;
use Modules\Event\Enums\InvitationStatus;

final class IndexAttendeeAction
{
    public function __invoke(IndexAttendeesData $data): LengthAwarePaginator
    {
        $user = User::query()->find(getCurrentUser()->id);

        $event = $user->events()->findOrFail($data->event_id);

        if ( ! $event->can_see_attendee) {
            throw new LogicalException(__('event::exceptions.user.attendee.invalid_attendee_access'), 422);
        }

        if ( ! $event->invitedUsers()->where('invitation.status', InvitationStatus::CHECKED_IN->value)->where('user_id', $user->id)->exists()) {
            throw new LogicalException(__('event::exceptions.user.attendee.attendee_not_checked_in_exception'), 422);
        }

        $attendees = $event
            ->invitedAttendees()
            ->wherePivot('invitation.status', InvitationStatus::CHECKED_IN->value)
            ->where('id', '<>', $user->id)
            ->with('profile')
            ->paginate($data->records_per_page);

        return $attendees;
    }
}
