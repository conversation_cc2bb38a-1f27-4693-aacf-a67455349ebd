<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\User;

final class ShowEventAction
{
    public function __invoke(int $event_id): Model
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        return Event::query()->with([
            'settings',
            'invitedSpeakers.profile',
            'invitedSupervisors.profile',
            'invitedAttendees' => fn ($query) => $query->take(5),
            'invitedAttendees.profile',
            'agendas' => fn ($query) => $query->active()->withExists([
                'attendees as is_joined' =>
                    fn ($query) => $query->where('user_id', $user->id),
            ])->orderBy('start_at', 'asc'),
            'agendas.speakers.profile',
            'agendas.supervisors.profile',
            'invitations' => fn ($query) => $query->where('user_id', $user->id),
        ])->whereHas('invitedUsers', function (Builder $query): void {
            $query->where('id', getCurrentUser()->id);
        })->withCount('invitedAttendees as attendees_count')->findOrFail($event_id);
    }
}
