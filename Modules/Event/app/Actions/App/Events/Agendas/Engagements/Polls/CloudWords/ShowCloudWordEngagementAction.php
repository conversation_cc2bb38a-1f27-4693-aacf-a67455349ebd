<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas\Engagements\Polls\CloudWords;

use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Builder;
use Modules\Event\DataTransferObjects\App\Events\Engagements\ShowEngagementData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Engagement;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Invitation;
use Modules\Event\Entities\User;
use Modules\Event\Enums\EngagementType;
use Modules\Event\Enums\InvitationStatus;
use Modules\Event\Enums\PollType;

final class ShowCloudWordEngagementAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(ShowEngagementData $data): Engagement
    {
        /** @var User $user */
        $user = getCurrentUser();

        /** @var Event $event */
        $event = Event::query()->findOrFail($data->event_id);

        $invitation = Invitation::with('user')
            ->where('user_id', $user->id)
            ->where('event_id', $event->id)
            ->first();

        if ($invitation?->status === InvitationStatus::PENDING->value) {
            throw new LogicalException(__('event::exceptions.engagement.poll.pending_invitation'));
        }

        /** @var Agenda $agenda */
        $agenda = $event->agendas()->findOrFail($data->agenda_id);

        if ( ! $agenda->attendees()->where('user_id', $user->id)->exists()) {
            throw new LogicalException(__('event::exceptions.engagement.poll.pending_invitation'));
        }

        /** @var Engagement $engagement */
        return $agenda->engagements()
            ->where('engagement_type', EngagementType::POLL->value)
            ->where('poll_type', PollType::WORDS->value)
            ->where('is_published', true)
            ->withExists(['responses as is_answered' => function (Builder $query) use ($user): void {
                $query->where('created_by_id', $user->id);
            }])
            ->findOrFail($data->engagement_id);
    }
}
