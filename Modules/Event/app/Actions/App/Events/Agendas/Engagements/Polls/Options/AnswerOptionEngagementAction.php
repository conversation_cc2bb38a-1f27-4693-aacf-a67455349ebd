<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas\Engagements\Polls\Options;

use App\Exceptions\LogicalException;
use Modules\Event\DataTransferObjects\App\Events\Engagements\AnswerOptionEngagementData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Engagement;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Invitation;
use Modules\Event\Entities\Response;
use Modules\Event\Entities\User;
use Modules\Event\Enums\EngagementType;
use Modules\Event\Enums\InvitationStatus;
use Modules\Event\Enums\PollType;

final class AnswerOptionEngagementAction
{
    /**
     * @param AnswerOptionEngagementData $data
     * @throws LogicalException
     */
    public function __invoke(AnswerOptionEngagementData $data): void
    {
        /** @var User $user */
        $user = User::query()->findOrFail(getCurrentUser()->id);

        /** @var Event $event */
        $event = Event::query()->findOrFail($data->event_id);

        $invitation = Invitation::with('user')
            ->where('user_id', $user->id)
            ->where('event_id', $event->id)
            ->first();

        if ($invitation?->status === InvitationStatus::PENDING->value) {
            throw new LogicalException(__('event::exceptions.engagement.poll.pending_invitation'));
        }

        /** @var Agenda $agenda */
        $agenda = $event->agendas()->findOrFail($data->agenda_id);

        if ( ! $agenda->attendees()->where('user_id', $user->id)->exists()) {
            throw new LogicalException(__('event::exceptions.engagement.poll.pending_invitation'));
        }

        /** @var Engagement $engagement */
        $engagement = $agenda->engagements()
            ->where('engagement_type', EngagementType::POLL->value)
            ->where('poll_type', PollType::OPTIONS->value)
            ->where('is_published', true)
            ->findOrFail($data->engagement_id);

        if ($engagement->isEnded()) {
            throw new LogicalException(__('event::exceptions.engagement.engagement_time_is_finish'));
        }

        /** @var ?Response $option */
        $option = $engagement->responses()->find($data->option_id);

        if ( ! $option) {
            throw new LogicalException(__('event::exceptions.engagement.no_option'));
        }

        if ($user->responses()->whereIn('id', $engagement->responses()->pluck('id'))->exists()) {
            throw new LogicalException(__('event::exceptions.engagement.already_answered'));
        }

        $option->update(['count' => ++$option->count]);
        $option->users()->attach($user->id);
    }
}
