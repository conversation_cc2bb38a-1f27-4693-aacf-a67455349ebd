<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas\Engagements\Polls\CloudWords;

use App\Exceptions\LogicalException;
use Carbon\Carbon;
use Modules\Event\DataTransferObjects\App\Events\Engagements\Polls\SendCloudWordAnswerData;
use Modules\Event\Entities\Invitation;
use Modules\Event\Entities\User;
use Modules\Event\Enums\InvitationStatus;
use Modules\Event\Enums\PollType;
use Modules\Event\Enums\ResponseType;

final class SendCloudWordAnswerAction
{
    public function __invoke(SendCloudWordAnswerData $data): void
    {
        /** @var User $user */
        $user = User::query()->findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($data->event_id);

        $invitation = Invitation::with('user')
            ->where('user_id', $user->id)
            ->where('event_id', $event->id)
            ->first();

        if ($invitation?->status === InvitationStatus::PENDING->value) {
            throw new LogicalException(__('event::exceptions.engagement.poll.pending_invitation'));
        }

        $session = $user->attendeesAgendas()->findOrFail($data->session_id);

        $engagement = $session->engagements()->where('poll_type', PollType::WORDS->value)->findOrFail($data->engagement_id);

        if ( ! $engagement->is_published) {
            throw new LogicalException(__('event::exceptions.engagement.engagement_is_not_published'));
        }

        if (Carbon::parse($engagement->end_at)->isPast()) {
            throw new LogicalException(__('event::exceptions.engagement.engagement_time_is_finish'));
        }

        $is_answered = $user->responses()->where('engagement_id', $data->engagement_id)->exists();

        if ($is_answered) {
            throw new LogicalException(__('event::exceptions.engagement.already_answered'));
        }

        $cloud_word = $engagement->responses()->firstOrCreate(
            [
                'title' => $data->answer,
            ],
            [
                'count' => 1,
                'type' => ResponseType::WORDS->value,
            ]
        );
        $user->responses()->syncWithoutDetaching([$cloud_word->id]);

        if ( ! $cloud_word->wasRecentlyCreated) {
            $cloud_word->increment('count');
        }
    }
}
