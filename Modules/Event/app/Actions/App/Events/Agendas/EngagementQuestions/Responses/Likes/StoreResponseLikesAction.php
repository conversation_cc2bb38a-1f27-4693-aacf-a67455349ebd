<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas\EngagementQuestions\Responses\Likes;

use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Modules\Event\Entities\User;

final class StoreResponseLikesAction
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param int $eventId
     * @param int $sessionId
     * @param int $engagementId
     * @param int $responseId
     *
     * @return Model
     *
     * @throws LogicalException
     */
    public function __invoke(Request $request, int $eventId, int $sessionId, int $engagementId, int $responseId): Model
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($eventId);

        $session = $event->agendas()->findOrFail($sessionId);

        $questions = $session->engagements()->where('is_published', true)->findOrFail($engagementId);

        $response = $questions->responses()->findOrFail($responseId);

        if ($response->likes()->where('user_id', $user->id)->exists()) {
            throw new LogicalException(__('event::exceptions.engagement.like_already_exists'));
        }

        $response->increment('count');

        $response->likes()->create([
            'user_id' => $user->id,
        ]);

        return $response;
    }
}
