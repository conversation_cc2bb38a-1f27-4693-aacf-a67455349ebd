<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas\EngagementQuestions\Responses;

use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Modules\Event\DataTransferObjects\App\Events\EngagementQuestions\EngagementResponseData;
use Modules\Event\Entities\User;
use Modules\Event\Enums\AttendeeQuestionStatus;
use Modules\Event\Enums\ResponseType;

final class StoreEngagementResponseAction
{
    /**
     * Handle the incoming request.
     *
     * @param EngagementResponseData $data
     * @param int $eventId
     * @param int $sessionId
     * @param int $engagementId
     *
     * @return Model
     */
    public function __invoke(EngagementResponseData $data, int $eventId, int $sessionId, int $engagementId): Model
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($eventId);

        $session = $event->agendas()->findOrFail($sessionId);

        $questions = $session->engagements()->where('is_published', true)->findOrFail($engagementId);

        if (Carbon::parse($questions->end_at)->isPast()) {
            throw new LogicalException(__('event::exceptions.engagement.engagement_time_is_finish'));
        }

        $attributes = $data->toArray() + [
            'created_by_id' => $data->is_anonymous ? null : $user->id,
            'type' => ResponseType::QUESTIONS->value,
            'status' => AttendeeQuestionStatus::REJECTED->value,
        ];

        $response = $questions->responses()->create($attributes);

        return $response;
    }
}
