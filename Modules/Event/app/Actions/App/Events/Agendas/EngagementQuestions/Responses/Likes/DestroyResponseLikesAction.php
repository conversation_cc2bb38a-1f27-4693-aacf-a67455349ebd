<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas\EngagementQuestions\Responses\Likes;

use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Modules\Event\Entities\User;

final class DestroyResponseLikesAction
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param int $eventId
     * @param int $sessionId
     * @param int $engagementId
     * @param int $responseId
     *
     * @return Model
     *
     * @throws LogicalException
     */
    public function __invoke(Request $request, int $eventId, int $sessionId, int $engagementId, int $responseId): Model
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($eventId);

        $session = $event->agendas()->findOrFail($sessionId);

        $questions = $session->engagements()->where('is_published', true)->findOrFail($engagementId);

        $response = $questions->responses()->findOrFail($responseId);

        $like = $response->likes()->where('user_id', $user->id)->first();

        if ( ! $like) {
            throw new LogicalException(__('event::exceptions.engagement.like_not_exists'), 422);
        }

        $like->delete();

        $response->decrement('count');

        return $response;
    }
}
