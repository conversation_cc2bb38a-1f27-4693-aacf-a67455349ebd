<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas\EngagementQuestions\Responses;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\App\Events\EngagementQuestions\IndexEngagementResponseData;
use Modules\Event\Entities\User;

final class IndexEngagementResponseAction
{
    /**
     * Handle the incoming request.
     *
     * @param IndexEngagementResponseData $data
     * @param int $eventId
     * @param int $sessionId
     * @param int $engagementId
     *
     * @return LengthAwarePaginator
     */
    public function __invoke(IndexEngagementResponseData $data, int $eventId, int $sessionId, int $engagementId): LengthAwarePaginator
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($eventId);

        $session = $event->agendas()->findOrFail($sessionId);

        $questions = $session->engagements()->where('is_published', true)->findOrFail($engagementId);

        $responses = $questions->responses()->accepted()->with('createdBy.profile')
            ->withExists(['likes as is_liked' => function ($query) use ($user): void {
                $query->where('user_id', $user->id);
            }])->orderBy($data->order_by, $data->sort)->paginate($data->per_page);

        return $responses;
    }
}
