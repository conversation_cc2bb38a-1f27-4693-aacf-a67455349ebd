<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas\EngagementQuestions;

use App\Exceptions\LogicalException;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Modules\Event\Entities\User;

final class ShowEngagementQuestionAction
{
    /**
     * Handle the incoming request.
     *
     * @param int $eventId
     * @param int $sessionId
     * @param int $engagementId
     *
     * @return Model
     *
     * @throws Exception
     */
    public function __invoke(int $eventId, int $sessionId, int $engagementId): Model
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($eventId);

        $session = $event->agendas()->findOrFail($sessionId);

        $questions = $session->engagements()->where('is_published', true)->findOrFail($engagementId);

        $is_answered = $questions->responses()->where('created_by_id', $user->id)->exists();

        if ($is_answered) {
            throw new LogicalException(__('event::exceptions.engagement.already_answered'));
        }

        if (Carbon::parse($questions->end_at)->isPast()) {
            throw new LogicalException(__('event::exceptions.engagement.question_has_ended'));
        }

        return $questions;
    }
}
