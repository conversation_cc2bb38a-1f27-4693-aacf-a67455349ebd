<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas;

use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Invitation;
use Modules\Event\Enums\InvitationStatus;

final class ShowCurrentSessionAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(int $id): ?Model
    {
        $event = Event::query()->findOrFail($id);

        $invitation = Invitation::with('user')->where([
            ['user_id', getCurrentUser()->id],
            ['event_id', $event->id],
        ])->first();

        $user = $invitation->user;

        if ($invitation->status === InvitationStatus::PENDING->value) {
            throw new LogicalException(__('event::exceptions.sessions.pending_invitation'));
        }

        return $user->attendeesAgendas()->where(
            'event_id',
            $event->id
        )->where([
            ['start_at', '<=', now()->addMinutes(30)],
            ['end_at', '>=', now()]
        ])->orderByPivot(
            'created_at',
            'desc'
        )->with([
            'speakers.profile',
            'supervisors.profile',
        ])->withExists([
            'attendees as is_joined' => fn ($query) => $query->where('user_id', getCurrentUser()->id)
        ])->first();
    }
}
