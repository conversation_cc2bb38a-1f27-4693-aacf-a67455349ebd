<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas\Speakers;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Modules\Event\Entities\User;

final class ListAgendaSpeakerAction
{
    public function __invoke(Request $request, int $event_id, int $agenda_id): LengthAwarePaginator
    {
        $event = User::query()
            ->findOrFail(getCurrentUser()->id)
            ->events()
            ->findOrFail($event_id);

        $agenda = $event->agendas()
            ->findOrFail($agenda_id);

        $speakers = $agenda
            ->speakers()
            ->with('profile')
            ->paginate($request->per_page);

        return $speakers;
    }
}
