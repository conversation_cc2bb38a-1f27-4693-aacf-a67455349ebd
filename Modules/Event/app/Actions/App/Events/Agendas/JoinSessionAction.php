<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas;

use App\Exceptions\LogicalException;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Invitation;
use Modules\Event\Enums\AgendaStatus;
use Modules\Event\Enums\InvitationStatus;

final class JoinSessionAction
{
    /**
     * Join session
     *
     *
     *
     * @throws LogicalException
     */
    public function __invoke(int $id, int $agenda_id): void
    {
        $event = Event::query()->findOrFail($id);
        $invitation = Invitation::with('user')->where([['user_id', getCurrentUser()->id], ['event_id', $event->id]])->first();
        $user = $invitation->user;
        $agenda = $event->agendas()->findOrFail($agenda_id);

        if ($invitation->status === InvitationStatus::PENDING->value) {
            throw new LogicalException(__('event::exceptions.sessions.pending_invitation'));
        }

        if (AgendaStatus::Ended === $agenda->status) {
            throw new LogicalException(__('event::exceptions.sessions.ended'));
        }


        if ( ! $agenda->is_soon && AgendaStatus::Upcoming === $agenda->status) {
            throw new LogicalException(__('event::exceptions.sessions.not_started'));
        }

        if ($agenda->attendees()->where('user_id', $user->id)->exists()) {
            throw new LogicalException(__('event::exceptions.sessions.already_in_session'));
        }

        if ($agenda->is_seats_limited) {
            if ($agenda->seats_available <= 0) {
                throw new LogicalException(__('event::exceptions.sessions.full_seats'));
            }
            $agenda->decrement('seats_available');
        }

        $previous_agenda = $user->attendeesAgendas()
            ->where('event_id', $event->id)
            ->where([['start_at', '<=', now()->addMinutes(30)], ['end_at', '>=', now()]])
            ->orderByPivot('created_at', 'desc')
            ->first();

        if (AgendaStatus::Current === $previous_agenda?->status || $previous_agenda?->is_soon) {
            $previous_agenda->attendees()->detach($user->id);

            if ($previous_agenda->is_seats_limited) {
                $previous_agenda->increment('seats_available');
            }
        }

        $agenda->attendees()->syncWithoutDetaching([$user->id]);
    }
}
