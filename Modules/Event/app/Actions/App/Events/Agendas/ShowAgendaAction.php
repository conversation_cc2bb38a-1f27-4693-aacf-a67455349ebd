<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas;

use Illuminate\Database\Eloquent\Model;
use Modules\Event\Entities\User;

final class ShowAgendaAction
{
    public function __invoke(int $id, int $agenda_id): Model
    {
        $user = User::findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($id);

        $agenda = $event->agendas()->with(
            [
                'speakers.profile',
                'supervisors.profile',
                'materials' => fn ($query) => $query->where('is_published', true)->take(3),
            ]
        )->withExists([
            'attendees as is_joined' => fn ($query) => $query->where('user_id', getCurrentUser()->id)
        ])->findOrFail($agenda_id);

        return $agenda;
    }
}
