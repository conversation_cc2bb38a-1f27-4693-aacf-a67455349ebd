<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas\Materials;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\App\Events\Materials\IndexSessionMaterialData;
use Modules\Event\Entities\User;

final class IndexMaterialAction
{
    public function __invoke(IndexSessionMaterialData $data): LengthAwarePaginator
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($data->event_id);

        $session = $event->agendas()->findOrFail($data->session_id);

        $materials = $session->materials()->where('is_published', true)->paginate($data->records_per_page);

        return $materials;
    }
}
