<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Agendas;

use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\Agendas\App\IndexAgendaData;

final class IndexEventAgendaAction
{
    public function __invoke(IndexAgendaData $data, int $id): LengthAwarePaginator
    {
        $user = getCurrentUser();

        $event = $user->events()->findOrFail($id);

        $agendas = $event->agendas()->with([
            'speakers' => fn ($query) => $query->take(3),
            'speakers.profile',
            'supervisors' => fn ($query) => $query->take(3),
            'supervisors.profile',
        ])->withExists([
            'attendees as is_joined' => fn ($query) => $query->where('user_id', getCurrentUser()->id),
        ])->when(
            $data->is_joined,
            fn ($query) => $query->whereHas('attendees', fn ($query) => $query->where('user_id', getCurrentUser()->id))
        )->when(
            $data->date,
            fn ($query) => $query->whereDate('start_at', Carbon::parse($data->date))
        )->orderBy($data->order_by, $data->sort)->paginate($data->per_page);

        return $agendas;
    }
}
