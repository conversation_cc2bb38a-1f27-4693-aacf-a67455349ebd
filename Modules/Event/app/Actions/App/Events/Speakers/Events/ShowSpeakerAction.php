<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Speakers\Events;

use Illuminate\Database\Eloquent\Model;
use Modules\Event\Entities\User;

final class ShowSpeakerAction
{
    public function __invoke(int $event_id, int $speaker_id): Model
    {
        $user = User::query()
            ->findOrFail(getCurrentUser()->id);

        $event = $user
            ->events()
            ->findOrFail($event_id);

        $speaker = $event
            ->invitedSpeakers()
            ->with(['profile', 'agendas' => function ($query) use ($event): void {
                $query->where('event_id', $event->id)
                    ->withExists(['attendees as is_joined' => fn ($query) => $query->where('user_id', getCurrentUser()->id)]);
            }])
            ->findOrFail($speaker_id);

        return $speaker;
    }
}
