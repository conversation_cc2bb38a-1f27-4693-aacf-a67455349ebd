<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Speakers;

use Illuminate\Database\Eloquent\Model;
use Modules\Event\Entities\User;

final class ShowSpeakerAction
{
    public function __invoke(int $event_id, int $agenda_id, int $speaker_id): Model
    {

        $user = User::query()
            ->findOrFail(getCurrentUser()->id);

        $event = $user
            ->events()
            ->findOrFail($event_id);

        $agenda = $event
            ->agendas()
            ->findOrFail($agenda_id);

        $speaker = $agenda
            ->speakers()
            ->with(['profile', 'agendas' => function ($query) use ($event): void {
                $query->where('event_id', $event->id);
            }])
            ->findOrFail($speaker_id);

        return $speaker;
    }
}
