<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Speakers;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\App\Events\Speakers\IndexSpeakerData;
use Modules\Event\Entities\User;

final class IndexSpeakersAction
{
    public function __invoke(IndexSpeakerData $data): LengthAwarePaginator
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($data->event_id);

        $speakers = $event->invitedSpeakers()->with('profile')->paginate($data->records_per_page);

        return $speakers;
    }
}
