<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Memories;

use App\Exceptions\LogicalException;
use Modules\Event\Entities\Memory;
use Modules\Event\Entities\User;
use Modules\Event\Enums\InvitationStatus;

final class DeleteMemoryAction
{
    public function __invoke(array $data): void
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($data['event_id']);

        /** @var Memory $memorey */
        $memorey = $event->memories()->findOrFail($data['memory_id']);

        $isCheckedIn = $event->invitedUsers()
            ->where('invitation.user_id', $user->id)
            ->where('invitation.status', InvitationStatus::CHECKED_IN)
            ->exists();


        if ( ! $isCheckedIn) {
            throw new LogicalException(__('event::exceptions.memories.not_checked_in'));
        }

        if ($user->id !== $memorey->user_id) {
            throw new LogicalException(__('event::exceptions.memory.not_allowed_to_delete'), 403);
        }

        $memorey->interactions()->delete();

        $memorey->delete();

    }
}
