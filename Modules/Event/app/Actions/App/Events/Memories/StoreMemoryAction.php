<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Memories;

use App\Exceptions\LogicalException;
use Modules\Event\DataTransferObjects\App\Events\Memories\StoreMemoryData;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Memory;
use Modules\Event\Entities\User;
use Modules\Event\Enums\InvitationStatus;

final class StoreMemoryAction
{
    public function __invoke(StoreMemoryData $data): Memory
    {
        $user = User::findOrFail(getCurrentUser()->id);

        $event = Event::findOrFail($data->event_id);

        $isCheckedIn = $event->invitedUsers()
            ->where('invitation.user_id', $user->id)
            ->where('invitation.status', InvitationStatus::CHECKED_IN)
            ->exists();

        if ( ! $isCheckedIn) {
            throw new LogicalException(__('event::exceptions.memories.not_checked_in'));
        }


        $memory = $user->memories()->create([
            'event_id' => $data->event_id,
        ]);

        $memory->addMedia($data->image)->toMediaCollection('image');

        return $memory;
    }
}
