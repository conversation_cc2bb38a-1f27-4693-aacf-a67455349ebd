<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Memories;

use App\Exceptions\LogicalException;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\App\Events\Memories\IndexMemoryData;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\User;
use Modules\Event\Enums\InvitationStatus;

final class IndexMemoryAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(IndexMemoryData $data): LengthAwarePaginator
    {
        /** @var User $user */
        $user = User::query()->findOrFail(getCurrentUser()->id);

        /** @var Event $event */
        $event = Event::query()->findOrFail($data->event_id);

        $isCheckedIn = $event->invitedUsers()->where(
            'invitation.user_id',
            $user->id
        )->where(
            'invitation.status',
            InvitationStatus::CHECKED_IN
        )->exists();

        if ( ! $isCheckedIn) {
            throw new LogicalException(__('event::exceptions.memories.not_checked_in'));
        }

        return $event->memories()->when(
            $data->my_memories,
            fn ($q) => $q->where('user_id', $user->id)
        )->with([
            'user.profile',
            'media',
            'reaction',
        ])->withCount(
            'interactions as interactions_count'
        )->orderBy(
            $data->order_by,
            $data->sort
        )->paginate($data->records_per_page);
    }
}
