<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Memories\Interactions;

use App\Exceptions\LogicalException;
use Modules\Event\DataTransferObjects\App\Events\Interactions\StoreInteractionData;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Interaction;
use Modules\Event\Entities\User;
use Modules\Event\Enums\InvitationStatus;

final class StoreInteractionAction
{
    public function __invoke(StoreInteractionData $data): void
    {
        $user = User::findOrFail(getCurrentUser()->id);

        $event = Event::findOrFail($data->event_id);

        if ($event->isEnded()) {
            throw new LogicalException(__('exceptions.event_ended'));
        }

        $isCheckedIn = $event->invitedUsers()
            ->where('invitation.user_id', $user->id)
            ->where('invitation.status', InvitationStatus::CHECKED_IN)
            ->exists();

        if ( ! $isCheckedIn) {
            throw new LogicalException(__('event::exceptions.memories.not_checked_in'));
        }

        $memory = $event->memories()->findOrFail($data->memory_id);

        $interaction = $memory->interactions()->where('user_id', $user->id)->first();

        if ($interaction) {
            if ($interaction->emoji === $data->emoji) {

                $interaction->delete();
            } else {
                $interaction->update(['emoji' => $data->emoji]);
            }
        } else {
            $user->interactions()->create($data->except('event_id')->toArray());
        }

        $emojis = Interaction::query()->where('memory_id', $memory->id)->distinct()->pluck('emoji')->toArray();

        $memory->update([
            'emojis' =>  $emojis
        ]);
    }
}
