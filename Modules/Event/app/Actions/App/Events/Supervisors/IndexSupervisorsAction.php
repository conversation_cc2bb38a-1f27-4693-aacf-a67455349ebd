<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Supervisors;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\App\Events\Supervisors\IndexSupervisorData;
use Modules\Event\Entities\User;

final class IndexSupervisorsAction
{
    public function __invoke(IndexSupervisorData $data): LengthAwarePaginator
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($data->event_id);

        $supervisors = $event->invitedSupervisors()->with('profile')->paginate($data->records_per_page);

        return $supervisors;
    }
}
