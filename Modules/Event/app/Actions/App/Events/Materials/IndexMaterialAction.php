<?php

declare(strict_types=1);

namespace Modules\Event\Actions\App\Events\Materials;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\App\Events\Materials\IndexMaterialData;
use Modules\Event\Entities\User;

final class IndexMaterialAction
{
    public function __invoke(IndexMaterialData $data): LengthAwarePaginator
    {
        $user = User::query()->findOrFail(getCurrentUser()->id);

        $event = $user->events()->findOrFail($data->event_id);

        $materials = $event->materials()->where('is_published', true)->paginate($data->records_per_page);

        return $materials;
    }
}
