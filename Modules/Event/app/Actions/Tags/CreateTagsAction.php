<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Tags;

use Modules\Event\DataTransferObjects\Tags\TagData;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Tag;

final class CreateTagsAction
{
    public function __invoke(int $id, TagData $data): Tag
    {
        $event = Event::query()->where(
            'organization_id',
            getCurrentOrganizer()->organization_id
        )->findOrFail($id);

        $attributes = $data->toArray() + ['is_default' => false];

        return $event->tags()->create(
            $attributes
        )->fresh();
    }
}
