<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Tags;

use App\Enums\ExceptionType;
use App\Enums\Http;
use App\Exceptions\LogicalException;
use Modules\Event\Entities\Event;

final class DeleteTagsAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(int $id, int $tag_id): bool
    {
        $event = Event::query()->where(
            'organization_id',
            getCurrentOrganizer()->organization_id
        )->findOrFail($id);

        $tag = $event->tags()->findOrFail($tag_id);

        if ($tag->is_default) {
            throw new LogicalException(
                __('exceptions.tags.delete_not_allowed'),
                statusCode: Http::UNPROCESSABLE_ENTITY->value,
                error: [
                    'type' => ExceptionType::DELETE_TAG_NOT_ALLOWED->value,
                    'message' => __('exceptions.tags.delete_not_allowed'),
                    'code' => Http::UNPROCESSABLE_ENTITY->value,
                ],
            );
        }

        if ($tag->invitations()->exists()) {
            throw new LogicalException(
                __('exceptions.tags.has_users_linked'),
                statusCode: Http::UNPROCESSABLE_ENTITY->value,
                error: [
                    'type' => ExceptionType::TAG_HAS_USERS_LINKED->value,
                    'message' => __('exceptions.tags.has_users_linked'),
                    'code' => Http::UNPROCESSABLE_ENTITY->value,
                ],
            );
        }

        return $tag->delete();
    }
}
