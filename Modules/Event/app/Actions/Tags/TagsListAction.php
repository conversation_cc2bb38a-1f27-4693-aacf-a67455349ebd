<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Tags;

use Illuminate\Database\Eloquent\Collection;
use Modules\Event\Entities\Event;
use Modules\Event\Enums\DefaultTags;

final class TagsListAction
{
    public function __invoke(int $id): Collection
    {
        $event = Event::query()->where(
            'organization_id',
            getCurrentOrganizer()->organization_id
        )->findOrFail($id);

        return $event->tags()->when( ! $event->can_have_speaker && ! $event->can_have_supervisor, function ($query): void {
            // If both are disabled, exclude both SPEAKER and SUPERVISOR tags
            $query->whereNotIn('name', [
                DefaultTags::SPEAKER->value,
                DefaultTags::SUPERVISOR->value,
            ]);
        })
            ->when( ! $event->can_have_speaker && $event->can_have_supervisor, function ($query): void {
                // If only a speaker is disabled, exclude SPEAKER tags
                $query->where('name', '!=', DefaultTags::SPEAKER->value);
            })
            ->when($event->can_have_speaker && ! $event->can_have_supervisor, function ($query): void {
                // If only supervisor is disabled, exclude SUPERVISOR tags
                $query->where('name', '!=', DefaultTags::SUPERVISOR->value);
            })->oldest()->get();
    }
}
