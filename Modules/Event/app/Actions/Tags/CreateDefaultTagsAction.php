<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Tags;

use Modules\Event\Concerns\HasDefaultTags;
use Modules\Event\Entities\Event;

final class CreateDefaultTagsAction
{
    use HasDefaultTags;

    public function __invoke(Event $event): void
    {
        if ($event->tags()->where('is_default', true)->doesntExist()) {
            $event->tags()->createMany(
                $this->tagsBasedOnSettings(
                    $event->can_have_speaker,
                    $event->can_have_supervisor,
                    $event->settings?->attendee_badge_color ?? $event->settings->primary_color,
                    $event->settings?->speaker_badge_color ?? $event->settings->primary_color,
                    $event->settings?->supervisor_badge_color ?? $event->settings->primary_color,
                )
            );
        }
    }
}
