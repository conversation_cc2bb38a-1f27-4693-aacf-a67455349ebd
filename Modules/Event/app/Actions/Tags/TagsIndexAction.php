<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Tags;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\Tags\IndexTagData;
use Modules\Event\Entities\Event;

final class TagsIndexAction
{
    public function __invoke(int $id, IndexTagData $data): LengthAwarePaginator
    {
        $event = Event::query()->where(
            'organization_id',
            getCurrentOrganizer()->organization_id
        )->findOrFail($id);

        return $event->tags()->when(
            $data->search,
            function ($query, $search) {
                return $query->where(function ($query) use ($search): void {
                    $query->where(
                        'name',
                        'ilike',
                        "%{$search}%"
                    );
                });
            }
        )->withCount([
            'invitations as invitations_count',
        ])->orderBy(
            $data->sort_by,
            $data->sort_direction
        )->paginate($data->per_page);
    }
}
