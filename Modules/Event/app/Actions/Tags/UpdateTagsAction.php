<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Tags;

use App\Enums\ExceptionType;
use App\Enums\Http;
use App\Exceptions\LogicalException;
use Modules\Event\DataTransferObjects\Tags\TagData;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Tag;

final class UpdateTagsAction
{
    /**
     * @throws LogicalException
     */
    public function __invoke(int $id, int $tag_id, TagData $data): Tag
    {
        $event = Event::query()->where(
            'organization_id',
            getCurrentOrganizer()->organization_id
        )->findOrFail($id);

        $tag = $event->tags()->findOrFail($tag_id);

        if ($tag->is_default) {
            throw new LogicalException(
                __('exceptions.tags.update_not_allowed'),
                statusCode: Http::UNPROCESSABLE_ENTITY->value,
                error: [
                    'type' => ExceptionType::UPDATE_TAG_NOT_ALLOWED->value,
                    'message' => __('exceptions.tags.update_not_allowed'),
                    'code' => Http::UNPROCESSABLE_ENTITY->value,
                ],
            );
        }

        $tag->update(
            $data->toArray()
        );

        return $tag->fresh();
    }
}
