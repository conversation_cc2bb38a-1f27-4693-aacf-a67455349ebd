<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Materials\Sessions;

use App\Exceptions\LogicalException;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Organizer;

final class DeleteSessionMaterialAction
{
    public function __invoke(int $event_id, int $seesion_id, int $material_id): void
    {
        /** @var Organizer $user */
        $user = getCurrentOrganizer();

        $event = Event::query()
            ->where(
                'organization_id',
                $user->organization_id
            )
            ->findOrFail($event_id);

        $session = $event->agendas()
            ->findOrFail($seesion_id);

        $material = $session->materials()
            ->findOrFail($material_id);

        if ($material->is_published) {
            throw new LogicalException(__('event::exceptions.material.cannot_delete_published_material'));
        }

        if ($material->created_by_id !== $user->id && $user->isSpeaker()) {
            throw new LogicalException(__('exceptions.speaker_not_allowed'));
        }

        $material->delete();
    }
}
