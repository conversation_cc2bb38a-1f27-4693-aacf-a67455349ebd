<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Materials\Sessions;

use Modules\Event\DataTransferObjects\Materials\Sessions\StoreMaterialData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Material;
use Modules\Event\Entities\Organizer;

final class StoreMaterialAction
{
    public function __invoke(StoreMaterialData $data): void
    {
        /** @var Organizer $user */
        $user = getCurrentOrganizer();

        /** @var Event $event */
        $event = Event::query()
            ->where('organization_id', $user->organization_id)
            ->findOrFail($data->event_id);

        /** @var Agenda $agenda */
        $agenda = $event->agendas()->findOrFail($data->agenda_id);

        $materials = $agenda->materials()->createMany(
            $data->materials->map(fn ($material): array => array_merge(
                $material->toArray(),
                ['created_by_id' => $user->id]
            ))->toArray()
        );

        $materials->each(function (Material $material, int $index) use ($data): void {
            $material->addMedia($data->materials[$index]->file)->toMediaCollection('material');
        });
    }
}
