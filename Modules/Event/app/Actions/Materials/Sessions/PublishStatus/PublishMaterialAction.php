<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Materials\Sessions\PublishStatus;

use App\Exceptions\LogicalException;
use Modules\Event\Entities\Event;
use Modules\Organizer\Entities\Organizer;

final class PublishMaterialAction
{
    public function __invoke(int $event_id, int $session_id, int $material_id): void
    {
        /** @var Organizer $user */
        $user = getCurrentOrganizer();

        $event = Event::query()->where(
            'organization_id',
            $user->organization_id
        )->findOrFail($event_id);

        $session = $event->agendas()->findOrFail($session_id);

        $material = $session->materials()->findOrFail($material_id);

        if ($material->created_by_id !== $user->id && $user->isSpeaker()) {
            throw new LogicalException(__('exceptions.speaker_not_allowed'));
        }

        $material->update([
            'is_published' => true
        ]);
    }
}
