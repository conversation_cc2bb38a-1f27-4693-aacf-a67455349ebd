<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Materials\Sessions;

use App\Enums\MediaCollection;
use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Modules\Event\DataTransferObjects\Materials\Sessions\UpdateMaterialData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Material;

final class UpdateMaterialAction
{
    public function __invoke(UpdateMaterialData $data): Model
    {
        /** @var \Modules\Event\Entities\Organizer $user */
        $user = getCurrentOrganizer();

        /** @var Event $event */
        $event = Event::query()
            ->where('organization_id', $user->organization_id)
            ->findOrFail($data->event_id);

        /** @var Agenda $agenda */
        $agenda = $event->agendas()->findOrFail($data->agenda_id);

        /** @var Material $material */
        $material = $agenda->materials()->findOrFail($data->material_id);

        if ($material->created_by_id !== $user->id && $user->isSpeaker()) {
            throw new LogicalException(__('exceptions.speaker_not_allowed'));
        }

        $material->update([
            'title' => $data->title,
        ]);

        if ($data->file) {
            $material->clearMediaCollection(MediaCollection::MATERIAL_COLLECTION->value);
            $material->addMedia($data->file)->toMediaCollection(MediaCollection::MATERIAL_COLLECTION->value);
        }

        return $material;

    }
}
