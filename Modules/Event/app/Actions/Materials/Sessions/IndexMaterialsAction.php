<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Materials\Sessions;

use Illuminate\Support\Collection;
use Modules\Event\DataTransferObjects\Materials\Sessions\IndexMaterialData;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Event;

final class IndexMaterialsAction
{
    public function __invoke(IndexMaterialData $data): Collection
    {
        /** @var \Modules\Event\Entities\Organizer $user */
        $user = getCurrentOrganizer();

        /** @var Event $event */
        $event = Event::query()
            ->where('organization_id', $user->organization_id)
            ->findOrFail($data->event_id);

        /** @var Agenda $agenda */
        $agenda = $event->agendas()->findOrFail($data->agenda_id);

        return $agenda->materials()->with('media')->when(
            $user->isSpeaker(),
            fn ($query) => $query->where('created_by_id', $user->id)
        )->orderBy($data->order_by, $data->sort)->get();
    }
}
