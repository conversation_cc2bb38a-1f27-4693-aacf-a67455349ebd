<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Materials\Events;

use App\Enums\MediaCollection;
use App\Exceptions\LogicalException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Modules\Event\DataTransferObjects\Materials\Events\UpdateMaterialData;
use Modules\Event\Entities\Event;
use Modules\Organizer\Entities\Organizer;

final class UpdateMaterialAction
{
    public function __invoke(UpdateMaterialData $data): Model
    {
        /** @var Organizer $user */
        $user = getCurrentOrganizer();

        $event = Event::query()
            ->where('organization_id', $user->organization_id)
            ->findOrFail($data->event_id);

        $material = $event->materials()->findOrFail($data->material_id);

        if ($material->created_by_id !== $user->id && $user->isSpeaker()) {
            throw new LogicalException(__('exceptions.speaker_not_allowed'));
        }

        $material->update([
            'title' => $data->title,
        ]);

        if ($data->file instanceof UploadedFile) {
            $material->clearMediaCollection(MediaCollection::MATERIAL_COLLECTION->value);
            $material->addMedia($data->file)->toMediaCollection(MediaCollection::MATERIAL_COLLECTION->value);
        }

        return $material;
    }
}
