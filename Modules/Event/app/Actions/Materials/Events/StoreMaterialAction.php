<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Materials\Events;

use Illuminate\Support\Collection;
use Modules\Event\DataTransferObjects\Materials\Events\MaterialData;
use Modules\Event\DataTransferObjects\Materials\Events\StoreMaterialData;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Material;

final class StoreMaterialAction
{
    public function __invoke(StoreMaterialData $data): void
    {
        $event = Event::query()
            ->where(
                'organization_id',
                getCurrentOrganizer()->organization_id
            )->findOrFail($data->event_id);

        /** @var Collection<int, MaterialData> $materialDataCollection */
        $materialDataCollection = $data->materials;

        $materialPayloads = [];

        foreach ($materialDataCollection as $material) {
            $materialPayloads[] = array_merge(
                $material->toArray(),
                ['created_by_id' => getCurrentOrganizer()->id]
            );
        }

        $materials = $event->materials()->createMany($materialPayloads);

        $materials->each(function (Material $material, int $index) use ($data): void {
            $material->addMedia($data->materials[$index]->file)->toMediaCollection('material');
        });
    }
}
