<?php

declare(strict_types=1);

namespace Modules\Event\Actions\Materials\Events;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\Event\DataTransferObjects\Materials\Events\IndexMaterialData;
use Modules\Event\Entities\Event;
use Modules\Organizer\Entities\Organizer;

final class IndexMaterialAction
{
    public function __invoke(IndexMaterialData $data): LengthAwarePaginator
    {
        /** @var Organizer $user */
        $user = getCurrentOrganizer();

        $event = Event::query()
            ->where('organization_id', $user->organization_id)
            ->findOrFail($data->event_id);

        return $event->materials()->when(
            $user->isSpeaker(),
            fn ($query) => $query->where('created_by_id', $user->id)
        )->when(
            $data->search,
            fn ($query) =>
            $query->where('title', 'ilike', '%' . $data->search . '%')
        )
            ->when(
                $data->sort,
                fn ($query) =>
                $query->orderBy($data->order_by, $data->sort)
            )
            ->with('media')
            ->paginate($data->records_per_page);
    }
}
