<?php

declare(strict_types=1);

namespace Modules\Event\Concerns;

use Modules\Event\Enums\DefaultTags;

trait HasDefaultTags
{
    protected function tags(): array
    {
        return [
            $this->make(
                DefaultTags::SUPERVISOR->value
            ),
            $this->make(
                DefaultTags::SPEAKER->value
            ),
            $this->make(
                DefaultTags::ATTENDEE->value
            ),
        ];
    }

    protected function tagsBasedOnSettings(
        bool $can_have_speaker,
        bool $can_have_supervisor,
        ?string $attendee_badge_color,
        ?string $speaker_badge_color,
        ?string $supervisor_badge_color,
    ): array {
        $tags = [
            $this->make(
                DefaultTags::ATTENDEE->value,
                $attendee_badge_color
            ),
        ];

        if ($can_have_supervisor) {
            $tags[] = $this->make(
                DefaultTags::SUPERVISOR->value,
                $supervisor_badge_color
            );
        }

        if ($can_have_speaker) {
            $tags[] = $this->make(
                DefaultTags::SPEAKER->value,
                $speaker_badge_color
            );
        }

        return $tags;
    }

    protected function make(string $name, ?string $color = null): array
    {
        return [
            'name' => $name,
            'color' => $color,
            'is_default' => true,
        ];
    }
}
