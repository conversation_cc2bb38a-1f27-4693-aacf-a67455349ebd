<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Engagements\Questions;

use Modules\Event\Enums\EngagementType;
use Spatie\LaravelData\Data;

final class StoreQuestionsData extends Data
{
    public function __construct(
        public string $title,
        public int $duration,
        public int $agenda_id,
        public int $event_id,
        public string $engagement_type = EngagementType::QUESTIONS->value,
    ) {}
}
