<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Engagements\Questions;

use Modules\Event\Enums\EngagementType;
use Spatie\LaravelData\Data;
use <PERSON>tie\LaravelData\Optional;

final class UpdateQuestionsData extends Data
{
    public function __construct(
        public Optional|string $title,
        public Optional|int $duration,
        public int $agenda_id,
        public int $question_id,
        public int $event_id,
        public string $engagement_type = EngagementType::QUESTIONS->value,
    ) {}
}
