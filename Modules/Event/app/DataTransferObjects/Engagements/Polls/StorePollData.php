<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Engagements\Polls;

use Modules\Event\Enums\EngagementType;
use Spatie\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Optional;

final class StorePollData extends Data
{
    public function __construct(
        public string $title,
        public int $duration,
        public int $agenda_id,
        public int $event_id,
        public string $poll_type,
        public Optional|array $options,
        public string $engagement_type = EngagementType::POLL->value,
    ) {}
}
