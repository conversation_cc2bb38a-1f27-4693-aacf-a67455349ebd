<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Events;

use Carbon\Carbon;
use Spatie\LaravelData\Data;

final class IndexEventsData extends Data
{
    public function __construct(
        public ?string $search = null,
        public ?string $check_in_type = null,
        public ?string $status = null,
        public ?int $category_id = null,
        public ?Carbon $date_from = null,
        public ?Carbon $date_to = null,
        public ?string $order_by = 'created_at',
        public ?string $sort = 'desc',
        public ?int $records_per_page = 15,
        public ?int $page = 1,
    ) {}
}
