<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Agendas;

use Spatie\LaravelData\Data;
use <PERSON>tie\LaravelData\Optional;

final class StoreAgendaData extends Data
{
    public function __construct(
        public string $title,
        public string $color,
        public string $description,
        public string $location,
        public string $start_at,
        public string $end_at,
        public Optional|bool $is_notification_enabled,
        public ?int $seats_number,
        public Optional|array $speaker_ids,
        public Optional|array $supervisor_ids,
        public int $event_id,
    ) {}
}
