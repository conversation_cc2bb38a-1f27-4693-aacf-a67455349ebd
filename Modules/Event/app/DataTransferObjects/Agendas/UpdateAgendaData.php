<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Agendas;

use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

final class UpdateAgendaData extends Data
{
    public function __construct(
        public Optional|string $title,
        public Optional|string $color,
        public Optional|string $description,
        public Optional|string $location,
        public Optional|string $start_at,
        public Optional|string $end_at,
        public Optional|bool $is_notification_enabled,
        public ?int $seats_number,
        public Optional|array|null $speaker_ids,
        public Optional|array|null $supervisors_ids,
        public int $event_id,
        public int $agenda_id,
    ) {}
}
