<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Agendas\App;

use Spatie\LaravelData\Data;

final class IndexAgendaData extends Data
{
    public function __construct(
        public string|null $date,
        public ?string $order_by = 'start_at',
        public ?string $sort = 'asc',
        public ?int $per_page = 100,
        public ?bool $is_joined = null,
    ) {}
}
