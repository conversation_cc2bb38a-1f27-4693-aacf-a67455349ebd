<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Speakers;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

final class StoreSpeakerData extends Data
{
    public function __construct(
        public string $name,
        public UploadedFile|Optional $picture,
        public string $email,
        public string $company,
        public string $job,
        public string $bio,
        public array|Optional $social_accounts,
        public int $event_id,
        public ?string $mobile = null,
        public ?string $country_code = null,
    ) {}
}
