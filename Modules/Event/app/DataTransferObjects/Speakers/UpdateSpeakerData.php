<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Speakers;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

final class UpdateSpeakerData extends Data
{
    public function __construct(
        public string|Optional $name,
        public UploadedFile|Optional $picture,
        public string|Optional $company,
        public string|Optional $job,
        public string|Optional $bio,
        public array|Optional $social_accounts,
        public int $event_id,
        public int $speaker_id,
        public string|Optional $mobile,
        public string|Optional $country_code,
    ) {}
}
