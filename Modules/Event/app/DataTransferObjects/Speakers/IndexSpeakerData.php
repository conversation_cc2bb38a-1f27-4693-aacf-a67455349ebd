<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Speakers;

use <PERSON><PERSON>\LaravelData\Data;

final class IndexSpeakerData extends Data
{
    public function __construct(
        public int $event_id,
        public ?string $search = null,
        public ?string $order_by = 'name',
        public ?string $sort = 'desc',
        public ?int $records_per_page = 10,
    ) {}
}
