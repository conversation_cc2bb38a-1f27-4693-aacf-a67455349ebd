<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\App\Events\Memories;

use Spatie\LaravelData\Data;

final class IndexMemoryData extends Data
{
    public function __construct(
        public int $event_id,
        public bool $my_memories = false,
        public string $order_by = 'created_at',
        public string $sort = 'desc',
        public int $records_per_page = 15,
        public int $page = 1,
    ) {}
}
