<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Optional;

final class StoreEventData extends Data
{
    public function __construct(
        public string $name_ar,
        public string|Optional $name_en,
        public string $check_in_type,
        public string $start_at,
        public string $end_at,
        public bool $can_see_attendee,
        public bool $can_have_speaker,
        public bool $can_have_supervisor,
        public int $category_id,
        public bool $allow_chatting,
        public ?bool $is_private = null,
    ) {}
}
