<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Platform\Events\Wizard;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

final class StoreWizardData extends Data
{
    public function __construct(
        public string $description_ar,
        public string|Optional $description_en,
        public string $latitude,
        public string $longitude,
        public string $location,
        public UploadedFile $logo,
        public ?UploadedFile $banner,
        public string $primary_color,
        public string|Optional $secondary_color,
        public float|Optional $ticket_price,
        public int|Optional $template_id,
        public int|Optional $badge_id,
        public string|Optional|null $attendee_badge_color,
        public string|Optional|null $speaker_badge_color,
        public string|Optional|null $supervisor_badge_color,
        public string $invitation_method,
        public string|Optional|null $out_app_communication_method,
    ) {}
}
