<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Platform\Events\Settings;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

final class UpdateSettingsData extends Data
{
    public function __construct(
        public string|Optional $description_ar,
        public string|Optional|null $description_en,
        public string|Optional $latitude,
        public string|Optional $longitude,
        public string|Optional $location,
        public UploadedFile|Optional $logo,
        public UploadedFile|Optional $banner,
        public string|Optional $primary_color,
        public string|Optional|null $secondary_color,
        public int|Optional|null $reminder_period,
        public float|Optional|null $ticket_price,
        public int|Optional|null $template_id,
        public int|Optional|null $badge_id,
    ) {}
}
