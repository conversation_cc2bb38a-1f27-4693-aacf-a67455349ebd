<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Attendees;

use <PERSON><PERSON>\LaravelData\Data;

final class IndexAttendeeData extends Data
{
    public function __construct(
        public int $event_id,
        public ?string $search,
        public ?bool $status,
        public ?string $invitation_status,
        public ?int $records_per_page = 10,
        public ?string $sort = 'desc',
        public ?string $order_by = 'created_at',
    ) {}
}
