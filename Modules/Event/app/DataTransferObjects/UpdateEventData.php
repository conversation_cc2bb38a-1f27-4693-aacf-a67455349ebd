<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects;

use Spa<PERSON>\LaravelData\Data;
use <PERSON>tie\LaravelData\Optional;

final class UpdateEventData extends Data
{
    public function __construct(
        public string|Optional $name_ar,
        public string|Optional $name_en,
        public string|Optional $domain,
        public string|Optional $check_in_type,
        public string|Optional $start_at,
        public string|Optional $end_at,
        public int|Optional $category_id,
    ) {}
}
