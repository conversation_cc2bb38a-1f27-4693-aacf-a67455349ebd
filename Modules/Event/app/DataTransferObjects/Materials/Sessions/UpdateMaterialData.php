<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Materials\Sessions;

use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Data;

final class UpdateMaterialData extends Data
{
    public function __construct(
        public int $event_id,
        public int $agenda_id,
        public int $material_id,
        public string $title,
        public ?UploadedFile $file
    ) {}
}
