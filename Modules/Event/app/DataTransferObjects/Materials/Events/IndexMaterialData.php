<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Materials\Events;

use Spatie\LaravelData\Data;

final class IndexMaterialData extends Data
{
    public function __construct(
        public int $event_id,
        public ?string $search,
        public string $order_by = 'created_at',
        public string $sort = 'desc',
        public int $records_per_page = 15,
    ) {}
}
