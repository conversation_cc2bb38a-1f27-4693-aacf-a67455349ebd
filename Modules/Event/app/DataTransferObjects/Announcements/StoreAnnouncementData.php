<?php

declare(strict_types=1);

namespace Modules\Event\DataTransferObjects\Announcements;

use Spatie\LaravelData\Data;
use <PERSON>tie\LaravelData\Optional;

final class StoreAnnouncementData extends Data
{
    public function __construct(
        public Optional|string $title_ar,
        public Optional|string $title_en,
        public Optional|string $content_ar,
        public Optional|string $content_en,
        public Optional|string $send_method,
        public int $event_id,
        /** @var array<int> $tag_ids */
        public array $tag_ids,
    ) {}
}
