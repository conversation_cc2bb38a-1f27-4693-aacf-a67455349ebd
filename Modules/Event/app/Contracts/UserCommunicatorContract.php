<?php

declare(strict_types=1);

namespace Modules\Event\Contracts;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Modules\Event\DataTransferObjects\Attendees\ImportAttendeeData;
use Modules\Event\DataTransferObjects\Attendees\IndexAttendeeData;
use Modules\Event\DataTransferObjects\Attendees\StoreAttendeeData;

interface UserCommunicatorContract
{
    public static function storeAttendee(StoreAttendeeData $data): Model;

    public static function indexAttendee(IndexAttendeeData $data): LengthAwarePaginator;

    public static function importAttendee(ImportAttendeeData $data): void;

    public static function getAgendaSpeakers(array $speakersIds): Collection;

    public static function deleteAttendee(int $id, int $user_id): void;
}
