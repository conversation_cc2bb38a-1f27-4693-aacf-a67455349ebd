<?php

declare(strict_types=1);

namespace Modules\Event\Presenters;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Modules\Event\Enums\AgendaStatus;

trait AgendaPresenter
{
    protected function status(): Attribute
    {
        return Attribute::make(
            get: function () {
                $start_at = Carbon::parse($this->start_at);
                $end_at = Carbon::parse($this->end_at);

                if ($start_at->isPast() && $end_at->isFuture()) {
                    return AgendaStatus::Current;
                }

                if ($start_at->isFuture()) {
                    return AgendaStatus::Upcoming;
                }

                if ($end_at->isPast()) {
                    return AgendaStatus::Ended;
                }
            },
        );
    }

    protected function translatedStatus(): Attribute
    {
        return Attribute::make(
            get: fn () => __('keywords.' . $this->status->value),
        );
    }


    protected function isSeatsLimited(): Attribute
    {
        return Attribute::make(
            get: fn () => null !== $this->seats_number,
        );
    }

    protected function appStatus(): Attribute
    {
        return Attribute::make(
            get: function () {
                $start_at = Carbon::parse($this->start_at);
                $end_at = Carbon::parse($this->end_at);

                if ($start_at->isPast() && $end_at->isFuture()) {
                    return __('event::messages.agenda.status.current');
                }

                if ($start_at->isFuture() && $start_at->diffInMinutes(absolute: true) < 30) {
                    return __('event::messages.agenda.status.soon');
                }

                if ($start_at->isFuture()) {
                    return __('event::messages.agenda.status.upcoming');
                }

                if ($end_at->isPast()) {
                    return __('event::messages.agenda.status.ended');
                }
            },
        );
    }

    protected function appIsSeatsLimited(): Attribute
    {
        return Attribute::make(
            get: fn () => null !== $this->seats_number,
        );
    }

    protected function isSoon(): Attribute
    {
        $start_at = Carbon::parse($this->start_at);

        return Attribute::make(
            get: fn () => $start_at->isFuture() && $start_at->diffInMinutes(absolute: true) < 30,
        );
    }
}
