<?php

declare(strict_types=1);

namespace Modules\Event\Presenters;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Modules\Event\Enums\EventStatus;

trait EventPresenter
{
    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->{'name_' . app()->getLocale()} ?? $this->name_ar,
        );
    }

    protected function startAt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => Carbon::parse($value),
        );
    }

    protected function endAt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => Carbon::parse($value),
        );
    }

    protected function status(): Attribute
    {
        return Attribute::make(
            get: function () {
                $start_at = Carbon::parse($this->start_at);
                $end_at = Carbon::parse($this->end_at);

                if ($start_at->isPast() && $end_at->isFuture()) {
                    return EventStatus::CURRENT;
                }

                if ($start_at->isFuture()) {
                    return EventStatus::UPCOMING;
                }

                if ($end_at->isPast()) {
                    return EventStatus::PAST;
                }
            },
        );
    }

    protected function translatedStatus(): Attribute
    {
        return Attribute::make(
            get: fn () => __('event::keywords.' . $this->status->value),
        );
    }

}
