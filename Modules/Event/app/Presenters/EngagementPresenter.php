<?php

declare(strict_types=1);

namespace Modules\Event\Presenters;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Modules\Event\Enums\EngagementStatus;
use Modules\Event\Enums\EngagementType;
use Modules\Event\Enums\PollType;

trait EngagementPresenter
{
    protected function status(): Attribute
    {
        return Attribute::make(
            get: function () {
                if ( ! $this->is_published) {
                    return EngagementStatus::DRAFT->value;
                }

                if (Carbon::parse($this->end_at)->isFuture()) {
                    return EngagementStatus::PUBLISHED->value;
                }

                return EngagementStatus::ENDED->value;
            },
        );
    }

    protected function translateStatus(): Attribute
    {
        return Attribute::make(
            get: function () {
                if ( ! $this->is_published) {
                    return __('event::keywords.engagement.status.draft');
                }

                if (Carbon::parse($this->end_at)->isFuture()) {
                    return __('event::keywords.engagement.status.published');
                }

                return __('event::keywords.engagement.status.ended');
            },
        );
    }

    protected function type(): Attribute
    {
        return Attribute::make(
            get: function () {
                if ($this->engagement_type === EngagementType::QUESTIONS->value) {
                    return __('event::keywords.engagement.type.questions');
                }

                if ($this->engagement_type === EngagementType::POLL->value && $this->poll_type === PollType::WORDS->value) {
                    return __('event::keywords.engagement.type.words');
                }

                if ($this->engagement_type === EngagementType::POLL->value && $this->poll_type === PollType::OPTIONS->value) {
                    return __('event::keywords.engagement.type.options');
                }
            },
        );
    }
}
