<?php

declare(strict_types=1);

namespace Modules\Event\Presenters;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Spatie\MediaLibrary\Support\File;
use Symfony\Component\Mime\MimeTypes;

trait MaterialPresenter
{
    protected function fileExtension(): Attribute
    {
        return Attribute::make(
            get: fn () => MimeTypes::getDefault()
                ->getExtensions($this->getFirstMedia('material')->mime_type)[0] ?? null
        );
    }

    protected function fileUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMediaUrl('material')
        );
    }

    protected function fileName(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('material')->file_name
        );
    }

    protected function fileSize(): Attribute
    {
        return Attribute::make(
            get: fn () =>  File::getHumanReadableSize($this->getFirstMedia('material')->size)
        );
    }
}
