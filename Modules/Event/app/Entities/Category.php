<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Event\Database\Factories\CategoryFactory;
use Modules\Event\Presenters\CategoryPresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Category extends Model
{
    use CategoryPresenter;
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name_ar',
        'name_en',
    ];

    public function events(): HasMany
    {
        return $this->hasMany(Event::class);
    }

    protected static function newFactory(): CategoryFactory
    {
        return CategoryFactory::new();
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
        ];
    }
}
