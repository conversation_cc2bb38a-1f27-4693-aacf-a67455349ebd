<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\Sanctum\HasApiTokens;
use Modules\Support\Entities\Country;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

/**
 * @property string $name
 * @property string $locale
 * @property string $password
 */
final class User extends Authenticatable
{
    use HasApiTokens;
    use Snowflakes;

    public function events(): BelongsToMany
    {
        return $this->belongsToMany(Event::class, 'invitation')
            ->withPivot(['status', 'checked_in_at', 'tag_id'])
            ->withTimestamps()
            ->using(Invitation::class);
    }

    public function profile(): HasOne
    {
        return $this->hasOne(
            Profile::class
        );
    }

    public function agendas(): BelongsToMany
    {
        return $this->belongsToMany(
            Agenda::class,
            'agenda_speaker',
        )->withTimestamps();
    }

    public function speakerSessions(): BelongsToMany
    {
        return $this->belongsToMany(
            Agenda::class,
            'agenda_speaker',
        )->withTimestamps();
    }

    public function attendeesAgendas(): BelongsToMany
    {
        return $this->belongsToMany(
            Agenda::class,
            'participants',
        )->withTimestamps();
    }

    public function supervisorSessions(): BelongsToMany
    {
        return $this->belongsToMany(
            Agenda::class,
            'agenda_supervisor',
        )->withTimestamps();
    }

    public function responses(): BelongsToMany
    {
        return $this->belongsToMany(
            Response::class,
            'response_user',
        )->withTimestamps();
    }

    public function memories(): HasMany
    {
        return $this->hasMany(Memory::class);
    }

    public function interactions(): HasMany
    {
        return $this->hasMany(Interaction::class);
    }

    public function checkIns(): HasMany
    {
        return $this->hasMany(CheckIn::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'country_id' => SnowflakeCast::class,
        ];
    }
}
