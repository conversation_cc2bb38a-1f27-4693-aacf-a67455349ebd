<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Carbon;
use Modules\Event\Database\Factories\AgendaFactory;
use Modules\Event\Presenters\AgendaPresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Agenda extends Model
{
    use AgendaPresenter;
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'color',
        'location',
        'description',
        'start_at',
        'end_at',
        'seats_number',
        'seats_available',
        'event_id',
        'reminder_scheduled_at',
        'is_notification_enabled',
    ];

    public function speakers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'agenda_speaker');
    }

    public function supervisors(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'agenda_supervisor');
    }

    public function attendees(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'participants');
    }

    public function engagements(): HasMany
    {
        return $this->hasMany(Engagement::class);
    }

    public function materials(): MorphMany
    {
        return $this->morphMany(Material::class, 'materialable');
    }

    public function isUpComing(): bool
    {
        return Carbon::parse($this->start_at)->isFuture();
    }

    public function isEnded(): bool
    {
        return Carbon::parse($this->end_at)->isPast();
    }

    /**
     * Scope a query to only include current or upcoming agendas.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        $now = now();

        return $query->where('end_at', '>', $now);
    }

    protected static function newFactory(): AgendaFactory
    {
        return AgendaFactory::new();
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'event_id' => SnowflakeCast::class,
            'start_at' => 'datetime',
            'end_at' => 'datetime',
        ];
    }
}
