<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Modules\Event\Presenters\EngagementPresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

/**
 *
 * @property Collection $responses
 */
final class Engagement extends Model
{
    use EngagementPresenter;
    use HasFactory;
    use Snowflakes;
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'duration',
        'is_published',
        'engagement_type',
        'poll_type',
        'start_at',
        'end_at',
        'agenda_id',
        'created_by_id',
        'is_stopped',
    ];

    public function agenda(): BelongsTo
    {
        return $this->belongsTo(Agenda::class);
    }

    public function organizer(): BelongsTo
    {
        return $this->belongsTo(Organizer::class, 'created_by_id');
    }

    public function responses(): HasMany
    {
        return $this->hasMany(Response::class);
    }

    public function isEnded(): bool
    {
        return Carbon::parse($this->end_at)->isPast();
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'agenda_id' => SnowflakeCast::class,
            'created_by_id' => SnowflakeCast::class,
            'start_at' => 'datetime',
            'end_at' => 'datetime',
        ];
    }
}
