<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\Event\Enums\OrganizerType;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Organizer extends Model
{
    use Snowflakes;

    public function organization(): BelongsTo
    {
        return $this->belongsTo(
            Organization::class
        );
    }

    public function events(): HasManyThrough
    {
        return $this->hasManyThrough(
            Event::class,
            Organization::class,
            'id',
            'organization_id',
            'organization_id'
        );
    }

    public function speaker(): HasOne
    {
        return $this->hasOne(User::class, 'email', 'email');
    }

    public function eventAccess(): BelongsToMany
    {
        return $this->BelongsToMany(
            Event::class,
            'event_access',
        )->withTimestamps();
    }

    public function isSpeaker(): bool
    {
        return OrganizerType::SPEAKER === $this->type;
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
        ];
    }
}
