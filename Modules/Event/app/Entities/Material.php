<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use App\Enums\MediaCollection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Modules\Event\Database\Factories\MaterialFactory;
use Modules\Event\Presenters\MaterialPresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

final class Material extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use MaterialPresenter;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'is_published',
        'created_by_id',
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(MediaCollection::MATERIAL_COLLECTION->value)->singleFile();
    }

    public function materialable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeIsPublished(Builder $query): void
    {
        $query->where('is_published', true);
    }

    public function mediaCollection(): ?Media
    {
        return $this->getFirstMedia(MediaCollection::MATERIAL_COLLECTION->value);
    }

    protected static function newFactory(): MaterialFactory
    {
        return new MaterialFactory;
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'materialable_id' => SnowflakeCast::class,
            'created_by_id' => SnowflakeCast::class,
        ];
    }

}
