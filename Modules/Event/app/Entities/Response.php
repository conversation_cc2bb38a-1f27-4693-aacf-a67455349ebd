<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use DivisionByZeroError;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Event\Enums\AttendeeQuestionStatus;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Response extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'count',
        'status',
        'engagement_id',
        'created_by_id',
        'type',
    ];

    public function engagement(): BelongsTo
    {
        return $this->belongsTo(Engagement::class);
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(
            User::class,
            'response_user',
        )->withTimestamps();
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }

    public function likes(): HasMany
    {
        return $this->hasMany(ResponseLike::class);
    }

    public function scopeAccepted(Builder $query): Builder
    {
        return $query->where('status', AttendeeQuestionStatus::ACCEPTED->value);
    }

    public function getPercentage(int $total): float
    {
        try {
            return round((100 / $this->total) * $this->count, 0);
        } catch (DivisionByZeroError $e) {
            return 0;
        }
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'engagement_id' => SnowflakeCast::class,
            'created_by_id' => SnowflakeCast::class,
        ];
    }
}
