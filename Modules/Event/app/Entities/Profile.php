<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Model;
use Modules\Event\Presenters\ProfilePresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Profile extends Model
{
    use ProfilePresenter;
    use Snowflakes;


    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'social_accounts' => 'json',
        ];
    }

}
