<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Snowflake\Snowflakes;

final class Organization extends Model
{
    use Snowflakes;

    public function events(): Has<PERSON>any
    {
        return $this->hasMany(Event::class);
    }

    public function organizers(): HasMany
    {
        return $this->hasMany(Organizer::class);
    }
}
