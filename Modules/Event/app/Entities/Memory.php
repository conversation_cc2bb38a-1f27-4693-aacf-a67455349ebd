<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\Event\Presenters\MemoryPresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

final class Memory extends Model implements HasMedia
{
    use InteractsWithMedia;
    use MemoryPresenter;
    use Snowflakes;
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'event_id',
        'emojis',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    public function interactions(): HasMany
    {
        return $this->hasMany(Interaction::class);
    }


    public function reaction(): HasOne
    {
        return $this->interactions()->where('user_id', auth()->user()->id)->one();
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')->singleFile();
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'user_id' => SnowflakeCast::class,
            'event_id' => SnowflakeCast::class,
            'emojis' => 'json',
        ];
    }
}
