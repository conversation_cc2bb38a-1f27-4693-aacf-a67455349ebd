<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Modules\Event\Database\Factories\EventFactory;
use Modules\Event\Enums\UserType;
use Modules\Event\Presenters\EventPresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

/**
 * @property string $name_ar
 * @property string $name_en
 * @property string $start_at
 */
final class Event extends Model
{
    use EventPresenter;
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'type',
        'qr_code',
        'name_ar',
        'name_en',
        'domain',
        'status',
        'published',
        'check_in_type',
        'start_at',
        'end_at',
        'organization_id',
        'category_id',
        'can_see_attendee',
        'can_have_speaker',
        'can_have_supervisor',
        'allow_chatting',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function settings(): HasOne
    {
        return $this->hasOne(EventSetting::class);
    }

    public function materials(): MorphMany
    {
        return $this->morphMany(Material::class, 'materialable');
    }

    public function invitedUsers(): BelongsToMany
    {
        return $this->belongsToMany(
            User::class,
            'invitation'
        )->withPivot([
            'status',
            'is_active',
            'attachment',
            'extra',
            'checked_in_at',
            'type',
            'tag_id',
            'attendance_code',
        ])->withTimestamps()->using(
            Invitation::class
        );
    }

    public function invitations(): HasMany
    {
        return $this->hasMany(Invitation::class);
    }

    public function invitedSpeakers(): BelongsToMany
    {
        return $this->invitedUsers()->wherePivot(
            'type',
            UserType::SPEAKER->value
        );
    }

    public function invitedAttendees(): BelongsToMany
    {
        return $this->invitedUsers()->wherePivot(
            'type',
            UserType::ATTENDEE->value
        );
    }

    public function invitedSupervisors(): BelongsToMany
    {
        return $this->invitedUsers()->wherePivot(
            'type',
            UserType::SUPERVISOR->value
        );
    }

    public function agendas(): HasMany
    {
        return $this->hasMany(Agenda::class);
    }

    public function announcements(): HasMany
    {
        return $this->hasMany(Announcement::class);
    }

    public function memories(): HasMany
    {
        return $this->hasMany(Memory::class);
    }

    public function checkIns(): HasMany
    {
        return $this->hasMany(CheckIn::class);
    }

    public function organizerAccess(): BelongsToMany
    {
        return $this->BelongsToMany(
            Organizer::class,
            'event_access',
        )->withTimestamps();
    }

    public function tags(): HasMany
    {
        return $this->hasMany(
            Tag::class,
        );
    }

    public function scopeCurrentStatus(Builder $query): Builder
    {
        return $query->where([['start_at', '<=', now()], ['end_at', '>=', now()]]);
    }

    public function scopeUpcoming(Builder $query): Builder
    {
        return $query->where('start_at', '>', now());
    }

    public function scopeNotEnded(Builder $query): Builder
    {
        return $query->where('end_at', '>', now());
    }

    public function isEnded(): bool
    {
        return Carbon::parse($this->end_at)->isPast();
    }

    public function isUpComing(): bool
    {
        return Carbon::parse($this->start_at)->isFuture();
    }

    public function isCurrent(): bool
    {
        return ! $this->isUpComing() && ! $this->isEnded();
    }

    protected static function newFactory(): EventFactory
    {
        return EventFactory::new();
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'organization_id' => SnowflakeCast::class,
            'category_id' => SnowflakeCast::class,
        ];
    }
}
