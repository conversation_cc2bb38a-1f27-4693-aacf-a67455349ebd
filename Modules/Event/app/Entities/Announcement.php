<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Modules\Event\Database\Factories\AnnouncementFactory;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Announcement extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title_ar',
        'title_en',
        'content_ar',
        'content_en',
        'send_method',
        'event_id',
    ];

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(
            Tag::class,
            'announcement_tag'
        );
    }

    protected static function newFactory(): AnnouncementFactory
    {
        return AnnouncementFactory::new();
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'event_id' => SnowflakeCast::class,
        ];
    }
}
