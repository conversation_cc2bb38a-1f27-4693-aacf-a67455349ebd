<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Event\Database\Factories\TagFactory;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Tag extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'color',
        'is_default',
        'event_id',
    ];

    public function events(): HasMany
    {
        return $this->hasMany(
            Event::class
        );
    }

    public function invitations(): HasMany
    {
        return $this->hasMany(
            Invitation::class
        );
    }

    public function announcements(): BelongsToMany
    {
        return $this->belongsToMany(
            Announcement::class,
            'announcement_tag'
        );
    }

    protected static function newFactory(): TagFactory
    {
        return TagFactory::new();
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'event_id' => SnowflakeCast::class,
            'is_default' => 'boolean',
        ];
    }
}
