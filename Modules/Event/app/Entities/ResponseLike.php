<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class ResponseLike extends Model
{
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'response_id',
        'user_id',
    ];

    public function response(): BelongsTo
    {
        return $this->belongsTo(Response::class);
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'response_id' => SnowflakeCast::class,
        ];
    }
}
