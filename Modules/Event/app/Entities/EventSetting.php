<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\Event\Database\Factories\EventSettingFactory;
use Modules\Event\Presenters\EventSettingPresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class EventSetting extends Model
{
    use EventSettingPresenter;
    use HasFactory;
    use Snowflakes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'logo',
        'banner',
        'description_ar',
        'description_en',
        'latitude',
        'longitude',
        'location',
        'primary_color',
        'secondary_color',
        'event_id',
        'reminder_period',
        'ticket_price',
        'template_id',
        'badge_id',
        'attendee_badge_color',
        'speaker_badge_color',
        'supervisor_badge_color',
        'invitation_method',
        'out_app_communication_method',
    ];

    protected static function newFactory(): EventSettingFactory
    {
        return EventSettingFactory::new();
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'event_id' => SnowflakeCast::class,
            'category_id' => SnowflakeCast::class,
            'ticket_price' => 'float',
        ];
    }
}
