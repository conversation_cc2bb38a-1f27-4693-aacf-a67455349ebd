<?php

declare(strict_types=1);

namespace Modules\Event\Entities;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Snowflake\SnowflakeCast;

final class Invitation extends Pivot
{
    protected $with = ['tag'];

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'status',
        'user_id',
        'event_id',
        'checked_in_at',
        'is_active',
        'attachment',
        'extra',
        'tag_id',
        'registration_source',
    ];

    final public function user(): BelongsTo
    {
        return $this->belongsTo(
            User::class
        );
    }

    final public function event(): BelongsTo
    {
        return $this->belongsTo(
            Event::class
        );
    }

    public function tag(): BelongsTo
    {
        return $this->belongsTo(
            Tag::class,
            'tag_id',
            'id'
        );
    }


    protected function casts(): array
    {
        return [
            'user_id' => SnowflakeCast::class,
            'event_id' => SnowflakeCast::class,
            'tag_id' => SnowflakeCast::class,
            'is_active' => 'boolean',
            'extra' => 'json',
        ];
    }
}
