<?php

declare(strict_types=1);

use App\Http\Middleware\CheckAttendeeAccess;
use Illuminate\Support\Facades\Route;
use Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Polls\CloudWords\SendCloudWordAnswerController;
use Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Polls\CloudWords\ShowCloudWordEngagementController;
use Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Polls\Options\AnswerOptionEngagementController;
use Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Polls\Options\ShowOptionEngagementController;
use Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Responses\IndexEngagementResponsesController;
use Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Responses\ResponseLikes\DestroyResponseLikesController;
use Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Responses\ResponseLikes\StoreResponseLikesController;
use Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\Responses\StoreEngagementResponsesController;
use Modules\Event\Http\Controllers\App\Events\Agendas\Engagements\ShowEngagementQuestionController;
use Modules\Event\Http\Controllers\App\Events\Agendas\IndexEventSessionController;
use Modules\Event\Http\Controllers\App\Events\Agendas\JoinSessionController;
use Modules\Event\Http\Controllers\App\Events\Agendas\Materials\IndexSessionMaterialController;
use Modules\Event\Http\Controllers\App\Events\Agendas\ShowCurrentSessionController;
use Modules\Event\Http\Controllers\App\Events\Agendas\ShowSessionController;
use Modules\Event\Http\Controllers\App\Events\Agendas\Speakers\ListAgendaSpeakerController;
use Modules\Event\Http\Controllers\App\Events\Attendees\Events\IndexAttendeeController;
use Modules\Event\Http\Controllers\App\Events\IndexEventController;
use Modules\Event\Http\Controllers\App\Events\Materials\IndexMaterialController;
use Modules\Event\Http\Controllers\App\Events\Memories\DeleteMemoryController;
use Modules\Event\Http\Controllers\App\Events\Memories\IndexMemoryController;
use Modules\Event\Http\Controllers\App\Events\Memories\Interactions\StoreInteractionController;
use Modules\Event\Http\Controllers\App\Events\Memories\StoreMemoryController;
use Modules\Event\Http\Controllers\App\Events\ShowEventController;
use Modules\Event\Http\Controllers\App\Events\Speakers\Events\ShowSpeakerController as EventsShowSpeakerController;
use Modules\Event\Http\Controllers\App\Events\Speakers\IndexSpeakerController;
use Modules\Event\Http\Controllers\App\Events\Speakers\ShowSpeakerController;
use Modules\Event\Http\Controllers\App\Events\Supervisors\IndexSupervisorController;
use Modules\Event\Http\Controllers\Users\EventUserAttendingController;

/*
 *--------------------------------------------------------------------------
 * App Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register App routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "app" middleware group
 *
*/

Route::name('app.')->prefix('app')->middleware([
    'auth:sanctum',
    CheckAttendeeAccess::class
])->group(function (): void {
    Route::name('events.')->prefix('events')->whereNumber(['id', 'event_id', 'speaker_id', 'session_id', 'memory_id'])->group(function (): void {

        Route::get(
            '/',
            IndexEventController::class
        )->name('index')->withoutMiddleware(CheckAttendeeAccess::class);

        Route::get(
            '/{id}',
            ShowEventController::class
        )->name('show');

        Route::name('speakers.')->prefix('{id}/speakers')->group(function (): void {
            Route::get(
                '/',
                IndexSpeakerController::class
            )->name('index');

            Route::get(
                '/{speaker_id}',
                EventsShowSpeakerController::class
            )->name('show');
        });

        Route::name('supervisors.')->prefix('{id}/supervisors')->group(function (): void {
            Route::get(
                '/',
                IndexSupervisorController::class
            )->name('index');
        });

        Route::name('attendees.')->prefix('{id}/attendees')->group(function (): void {
            Route::get(
                '/',
                IndexAttendeeController::class
            )->name('index');
        });

        Route::name('memories.')->prefix('{id}/memories')->group(function (): void {
            Route::delete(
                '/{memory_id}',
                DeleteMemoryController::class
            )->name('delete');
        });

        Route::name('sessions.')->prefix('{id}/sessions')->group(function (): void {
            Route::get(
                '/',
                IndexEventSessionController::class
            )->name('index');

            Route::get(
                '/{session_id}',
                ShowSessionController::class
            )->name('show');

            Route::name('speakers.')->prefix('{session_id}/speakers')->group(function (): void {
                Route::get(
                    '/',
                    ListAgendaSpeakerController::class
                )->name('index');
            });

            Route::name('speakers.')->prefix('{session_id}/speakers')->group(function (): void {
                Route::get(
                    '/{speaker_id}',
                    ShowSpeakerController::class
                )->name('show');
            });

            Route::post(
                '/{session_id}/join',
                JoinSessionController::class
            )->name('join');

            Route::get(
                '/current',
                ShowCurrentSessionController::class
            )->name('current');

            Route::name('materials.')->prefix('{session_id}/materials')->group(function (): void {
                Route::get(
                    '/',
                    IndexSessionMaterialController::class
                )->name('index');
            });

            Route::name('engagements.')->prefix('{session_id}/engagements')->group(function (): void {
                Route::name('questions.')->prefix('questions/{engagement_id}')->group(function (): void {
                    Route::get(
                        '/',
                        ShowEngagementQuestionController::class
                    )->name('show');

                    Route::name('responses.')->prefix('responses')->group(function (): void {
                        Route::post(
                            '/',
                            StoreEngagementResponsesController::class
                        )->name('store');

                        Route::get(
                            '/',
                            IndexEngagementResponsesController::class
                        )->name('index');

                        Route::name('likes.')->prefix('{response_id}/likes')->group(function (): void {
                            Route::post(
                                '/',
                                StoreResponseLikesController::class
                            )->name('store');

                            Route::delete(
                                '/',
                                DestroyResponseLikesController::class
                            )->name('destroy');
                        });
                    });
                });
                Route::name('polls.')->prefix('polls')->group(function (): void {
                    Route::name('options.')->prefix('options')->group(function (): void {
                        Route::get(
                            '{engagement_id}',
                            ShowOptionEngagementController::class
                        )->name('show');

                        Route::post(
                            '{engagement_id}/answer',
                            AnswerOptionEngagementController::class
                        )->name('answer');
                    });

                    Route::name('cloud-words.')->prefix('cloud-words/{engagement_id}')->group(function (): void {
                        Route::post(
                            '/',
                            SendCloudWordAnswerController::class
                        )->name('store');

                        Route::get(
                            '/',
                            ShowCloudWordEngagementController::class
                        )->name('show');
                    });
                });
            });
        });

        Route::name('check-in.')->prefix('{id}/check-in')->group(function (): void {
            Route::post(
                '/self',
                EventUserAttendingController::class
            )->name('self');
        });

        Route::name('materials.')->prefix('{id}/materials')->group(function (): void {
            Route::get(
                '/',
                IndexMaterialController::class
            )->name('index');
        });

        Route::name('memories.')->prefix('{id}/memories')->group(function (): void {
            Route::get(
                '/',
                IndexMemoryController::class
            )->name('index');

            Route::post(
                '/',
                StoreMemoryController::class
            )->name('store');

            Route::name('interaction.')->prefix('{memory_id}/interactions')->group(function (): void {
                Route::post(
                    '/',
                    StoreInteractionController::class
                )->name('store');
            });
        });
    });
});
