<?php

declare(strict_types=1);

use App\Http\Middleware\CheckEventAccess;
use App\Http\Middleware\CheckEventEnded;
use App\Http\Middleware\CheckOrganizerType;
use Illuminate\Support\Facades\Route;
use Modules\Event\Http\Controllers\Agendas\DeleteAgendaController;
use Modules\Event\Http\Controllers\Agendas\IndexAgendaController;
use Modules\Event\Http\Controllers\Agendas\ShowAgendaController;
use Modules\Event\Http\Controllers\Agendas\StoreAgendaController;
use Modules\Event\Http\Controllers\Agendas\UpdateAgendaController;
use Modules\Event\Http\Controllers\Announcements\IndexAnnouncementController;
use Modules\Event\Http\Controllers\Announcements\StoreAnnouncementController;
use Modules\Event\Http\Controllers\Attendees\DeleteAttendeeController;
use Modules\Event\Http\Controllers\Attendees\ImportAttendeeController;
use Modules\Event\Http\Controllers\Attendees\IndexAgendaAttendeesController;
use Modules\Event\Http\Controllers\Attendees\IndexAttendeeController;
use Modules\Event\Http\Controllers\Attendees\StoreAttendeeController;
use Modules\Event\Http\Controllers\Categories\CategoryListController;
use Modules\Event\Http\Controllers\Engagements\DeleteEngagementController;
use Modules\Event\Http\Controllers\Engagements\IndexEngagementsController;
use Modules\Event\Http\Controllers\Engagements\Polls\GetCloudWordsPollResultController;
use Modules\Event\Http\Controllers\Engagements\Polls\GetOptionPollResultController;
use Modules\Event\Http\Controllers\Engagements\Polls\StorePollController;
use Modules\Event\Http\Controllers\Engagements\Polls\UpdatePollController;
use Modules\Event\Http\Controllers\Engagements\PublishEngagementController;
use Modules\Event\Http\Controllers\Engagements\Questions\Responses\ApproveQuestionsController;
use Modules\Event\Http\Controllers\Engagements\Questions\Responses\IndexQuestionsController;
use Modules\Event\Http\Controllers\Engagements\Questions\Responses\RejectQuestionsController;
use Modules\Event\Http\Controllers\Engagements\Questions\StoreQuestionsController;
use Modules\Event\Http\Controllers\Engagements\Questions\UpdateQuestionsController;
use Modules\Event\Http\Controllers\Engagements\TerminateEngagementController;
use Modules\Event\Http\Controllers\Events\EarningStatisticsController;
use Modules\Event\Http\Controllers\Events\IndexAccessibleEventController;
use Modules\Event\Http\Controllers\Events\IndexEventController;
use Modules\Event\Http\Controllers\Events\ShowEventController;
use Modules\Event\Http\Controllers\Events\Statistics\General\AttendeeStatisticsController;
use Modules\Event\Http\Controllers\Events\Statistics\General\SessionStatisticsController;
use Modules\Event\Http\Controllers\Events\Statistics\General\SpeakerStatisticsController;
use Modules\Event\Http\Controllers\Events\Statistics\General\SupervisorStatisticsController;
use Modules\Event\Http\Controllers\Materials\Events\DeleteMaterialController;
use Modules\Event\Http\Controllers\Materials\Events\IndexMaterialController;
use Modules\Event\Http\Controllers\Materials\Events\StoreMaterialController as StoreEventMaterialController;
use Modules\Event\Http\Controllers\Materials\Events\UpdateMaterialController as UpdateEventMaterialController;
use Modules\Event\Http\Controllers\Materials\Sessions\DeleteSessionMaterialController;
use Modules\Event\Http\Controllers\Materials\Sessions\IndexMaterialsController;
use Modules\Event\Http\Controllers\Materials\Sessions\Status\PublishMaterialController;
use Modules\Event\Http\Controllers\Materials\Sessions\Status\UnpublishMaterialController;
use Modules\Event\Http\Controllers\Materials\Sessions\StoreMaterialController;
use Modules\Event\Http\Controllers\Materials\Sessions\UpdateMaterialController;
use Modules\Event\Http\Controllers\Organizations\Events\StoreEventController;
use Modules\Event\Http\Controllers\Organizations\Events\UpdateEventController;
use Modules\Event\Http\Controllers\Platform\Events\Settings\ShowSettingsController;
use Modules\Event\Http\Controllers\Platform\Events\Settings\UpdateSettingsController;
use Modules\Event\Http\Controllers\Platform\Events\Wizard\StoreWizardController;
use Modules\Event\Http\Controllers\Tags\CreateTagController;
use Modules\Event\Http\Controllers\Tags\DeleteTagController;
use Modules\Event\Http\Controllers\Tags\IndexTagController;
use Modules\Event\Http\Controllers\Tags\TagListController;
use Modules\Event\Http\Controllers\Tags\UpdateTagController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::name('events.')->prefix('events')->group(function (): void {
    Route::name('accounts.')->prefix('accounts')->group(function (): void {
        Route::middleware(['auth:sanctum'])->group(function (): void {});
    });
});

Route::middleware(['auth:organizer-api'])->whereNumber(['id', 'event_id', 'agenda_id', 'material_id', 'user_id', 'question_id', 'engagement_id'])->group(function (): void {
    Route::name('platform.')->prefix('platform')->group(function (): void {
        Route::name('categories.')->prefix('categories')->group(static function (): void {
            Route::get(
                '/',
                CategoryListController::class
            )->name('index');
        });

        Route::name('events.')->prefix('events')->group(function (): void {
            Route::get(
                '/',
                IndexEventController::class
            )->name('index');
            Route::get(
                '/accessible-events',
                IndexAccessibleEventController::class
            )->name('accessible-events');

            Route::middleware(CheckEventAccess::class)->get(
                '/{id}',
                ShowEventController::class
            )->name('show');

            Route::post(
                '/',
                StoreEventController::class,
            )->name('store');

            Route::middleware([CheckEventEnded::class, CheckEventAccess::class])->patch(
                '/{id}',
                UpdateEventController::class,
            )->name('update');

            Route::name('tags.')->prefix('{id}/tags')->group(static function (): void {
                Route::get(
                    '/list',
                    TagListController::class
                )->name('list');

                Route::get(
                    '/',
                    IndexTagController::class
                )->name('index');

                Route::post(
                    '/',
                    CreateTagController::class
                )->name('create');

                Route::patch(
                    '/{tag_id}',
                    UpdateTagController::class
                )->name('update');

                Route::delete(
                    '/{tag_id}',
                    DeleteTagController::class
                )->name('delete');
            });

            Route::name('attendees.')->prefix('{id}/attendees')->middleware(CheckEventAccess::class)->group(function (): void {
                Route::middleware(CheckEventEnded::class)->group(function (): void {
                    Route::post(
                        '/',
                        StoreAttendeeController::class
                    )->name('store');

                    Route::post(
                        '/import',
                        ImportAttendeeController::class
                    )->name('import');

                    Route::delete(
                        '/{user_id}',
                        DeleteAttendeeController::class
                    )->name('delete');
                });
                Route::get(
                    '/',
                    IndexAttendeeController::class
                );
            });

            Route::name('agendas.')->prefix('{id}/agendas')->middleware(CheckEventAccess::class)->group(function (): void {
                Route::middleware([CheckEventEnded::class, CheckOrganizerType::class])->group(function (): void {
                    Route::post(
                        '/',
                        StoreAgendaController::class
                    )->name('store');

                    Route::patch(
                        '/{agenda_id}',
                        UpdateAgendaController::class
                    )->name('update');

                    Route::delete(
                        '{agenda_id}',
                        DeleteAgendaController::class
                    )->name('delete');
                });

                Route::get(
                    '/',
                    IndexAgendaController::class
                )->name('index');

                Route::get(
                    '{agenda_id}',
                    ShowAgendaController::class
                )->name('show');

                Route::name('attendees.')->prefix('{agenda_id}/attendees')->group(function (): void {
                    Route::get(
                        '/',
                        IndexAgendaAttendeesController::class
                    )->name('index');
                });

                Route::name('materials.')->prefix('{agenda_id}/materials')->group(function (): void {

                    Route::middleware(CheckEventEnded::class)->group(function (): void {
                        Route::post(
                            '/',
                            StoreMaterialController::class
                        )->name('store');

                        Route::patch(
                            '/{material_id}',
                            UpdateMaterialController::class
                        )->name('update');

                        Route::delete(
                            '/{material_id}',
                            DeleteSessionMaterialController::class
                        )->name('delete');
                    });

                    Route::get(
                        '/',
                        IndexMaterialsController::class
                    )->name('index');

                    Route::name('status.')->prefix('{material_id}/status')->middleware(CheckEventEnded::class)->group(function (): void {
                        Route::patch(
                            '/publish',
                            PublishMaterialController::class
                        )->name('publish');

                        Route::patch(
                            '/unpublish',
                            UnpublishMaterialController::class
                        )->name('unpublish');
                    });
                });

                Route::name('engagements.')->prefix('{agenda_id}/engagements')->group(function (): void {
                    Route::patch(
                        '/{engagement_id}/publish',
                        PublishEngagementController::class
                    )->name('publish');

                    Route::patch(
                        '/{engagement_id}/terminate',
                        TerminateEngagementController::class
                    )->name('terminate');

                    Route::get(
                        '/',
                        IndexEngagementsController::class
                    )->name('index');
                    Route::delete(
                        '/{engagement_id}',
                        DeleteEngagementController::class,
                    )->name('delete');

                    Route::name('questions.')->prefix('questions')->group(function (): void {
                        Route::post(
                            '/',
                            StoreQuestionsController::class
                        )->name('store');

                        Route::patch(
                            '/{question_id}',
                            UpdateQuestionsController::class
                        )->name('update');
                        Route::name('responses.')->prefix('{question_id}/responses')->group(function (): void {
                            Route::get(
                                '/',
                                IndexQuestionsController::class
                            )->name('index');
                            Route::post(
                                '/approve',
                                ApproveQuestionsController::class
                            )->name('approve');
                            Route::post(
                                '/reject',
                                RejectQuestionsController::class
                            )->name('reject');
                        });
                    });

                    Route::name('polls.')->prefix('polls')->group(function (): void {
                        Route::post(
                            '/',
                            StorePollController::class
                        )->name('store');
                        Route::patch(
                            '/{poll_id}',
                            UpdatePollController::class
                        )->name('update');
                        Route::get(
                            'cloud-words/{poll_id}/result',
                            GetCloudWordsPollResultController::class
                        )->name('index');

                        Route::name('option')->prefix('option')->group(function (): void {
                            Route::get(
                                '/{poll_id}/result',
                                GetOptionPollResultController::class
                            )->name('result');
                        });
                    });
                });
            });

            Route::name('materials.')->prefix('{id}/materials')->middleware(CheckEventAccess::class)->group(function (): void {

                Route::middleware(CheckEventEnded::class)->group(function (): void {
                    Route::post(
                        '/',
                        StoreEventMaterialController::class
                    )->name('store');

                    Route::patch(
                        '/{material_id}',
                        UpdateEventMaterialController::class,
                    )->name('update');

                    Route::delete(
                        '/{material_id}',
                        DeleteMaterialController::class
                    )->name('delete');
                });

                Route::get(
                    '/',
                    IndexMaterialController::class
                )->name('index');
            });

            Route::name('announcements.')->prefix('{id}/announcements')->middleware(CheckEventAccess::class)->group(function (): void {
                Route::middleware(CheckEventEnded::class)->post(
                    '/',
                    StoreAnnouncementController::class
                )->name('store');
                Route::get(
                    '/',
                    IndexAnnouncementController::class
                )->name('index');
            });
        });

        Route::name('portal.')->prefix('portal')->group(function (): void {
            Route::name('events.')->prefix('events/{id}')->middleware(CheckEventAccess::class)->group(function (): void {
                Route::name('wizard.')->prefix('wizard')->group(function (): void {
                    Route::post(
                        '/',
                        StoreWizardController::class,
                    )->name('store');
                });

                Route::name('settings.')->prefix('settings')->group(function (): void {
                    Route::get(
                        '/',
                        ShowSettingsController::class,
                    )->name('show');

                    Route::patch(
                        '/',
                        UpdateSettingsController::class,
                    )->name('update');
                });

                Route::name('statistics.')->prefix('statistics')->group(static function (): void {
                    Route::name('general.')->prefix('general')->group(static function (): void {
                        Route::get(
                            '/attendees',
                            AttendeeStatisticsController::class
                        )->name('attendees');

                        Route::get(
                            '/sessions',
                            SessionStatisticsController::class
                        )->name('sessions');

                        Route::get(
                            '/speakers',
                            SpeakerStatisticsController::class
                        )->name('speakers');

                        Route::get(
                            '/supervisors',
                            SupervisorStatisticsController::class
                        )->name('supervisors');
                    });

                    Route::name('earning.')->prefix('earning')->group(static function (): void {
                        Route::get(
                            '',
                            EarningStatisticsController::class
                        )->name('show');
                    });
                });
            });
        });
    });
});
