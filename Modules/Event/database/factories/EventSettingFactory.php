<?php

declare(strict_types=1);

namespace Modules\Event\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

final class EventSettingFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = \Modules\Event\Entities\EventSetting::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'id' => snowflake(),
            'description_ar' => $this->faker->sentence(3),
            'description_en' => $this->faker->sentence(3),
            'latitude' => $this->faker->latitude(),
            'longitude' => $this->faker->longitude(),
            'location' => $this->faker->address(),
            'primary_color' => $this->faker->hexColor(),
            'secondary_color' => $this->faker->hexColor(),
        ];
    }
}
