<?php

declare(strict_types=1);

namespace Modules\Event\Database\Factories;

use Carbon\Carbon;
use Faker\Factory as Faker;
use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Event\Enums\EventCheckInType;
use Modules\Event\Enums\EventType;

final class EventFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = \Modules\Event\Entities\Event::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $date = Carbon::parse($this->faker->dateTimeBetween('-1 week', '+1 week'));

        $startAtCarbon = Carbon::parse($date);

        $endAtCarbon = (clone $startAtCarbon)->addDays($this->faker->numberBetween(1, 10));

        return [
            'id' => snowflake(),
            'name_en' => $this->faker->sentence(3),
            'name_ar' => Faker::create('ar_AS')->name,
            'published' => $this->faker->numberBetween(0, 1),
            'check_in_type' => $this->faker->randomElement(EventCheckInType::cases()),
            'start_at' => $startAtCarbon,
            'end_at' => $endAtCarbon,
            'type' => $this->faker->randomElement(EventType::cases()),
        ];
    }
}
