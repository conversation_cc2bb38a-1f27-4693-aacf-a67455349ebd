<?php

declare(strict_types=1);

namespace Modules\Event\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

final class AnnouncementFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = \Modules\Event\Entities\Announcement::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'id' => snowflake(),
            'title_ar' => $this->faker->title(),
            'content_ar' => $this->faker->text(),
            'event_id' => null,
        ];
    }
}
