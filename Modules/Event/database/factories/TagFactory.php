<?php

declare(strict_types=1);

namespace Modules\Event\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

final class TagFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = \Modules\Event\Entities\Tag::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [];
    }
}
