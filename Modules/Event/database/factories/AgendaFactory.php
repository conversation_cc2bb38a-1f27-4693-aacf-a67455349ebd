<?php

declare(strict_types=1);

namespace Modules\Event\Database\Factories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

final class AgendaFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = \Modules\Event\Entities\Agenda::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {

        $startAtCarbon = Carbon::parse($this->faker->dateTimeBetween('-5 day', '+1 week'));

        $endAtCarbon = (clone $startAtCarbon)->addMinutes($this->faker->numberBetween(1, 180));

        return [
            'id' => snowflake(),
            'title' => $this->faker->title(),
            'color' => $this->faker->hexColor(),
            'location' => $this->faker->address(),
            'description' => $this->faker->paragraph(),
            'start_at' => $startAtCarbon,
            'end_at' => $endAtCarbon,
            'seats_number' => $this->faker->numberBetween(10, 500),
            'event_id' => null,
        ];
    }
}
