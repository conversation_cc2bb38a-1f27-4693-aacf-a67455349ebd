<?php

declare(strict_types=1);

namespace Modules\Event\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Event\Entities\Announcement;
use Modules\Event\Entities\Event;

final class AnnouncementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $events = Event::query()->select(['id'])->get();

        foreach ($events as $event) {
            $event->announcements()->createMany(Announcement::factory(5)->make()->toArray());
        }
    }
}
