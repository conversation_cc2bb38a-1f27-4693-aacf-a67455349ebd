<?php

declare(strict_types=1);

namespace Modules\Event\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Event\Entities\Category;

final class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name_ar' => 'رياضة',
                'name_en' => 'Sports',
            ],
            [
                'name_ar' => 'تكنولوجيا',
                'name_en' => 'Tech',
            ],
            [
                'name_ar' => 'تنمية بشرية',
                'name_en' => 'Self Development',
            ],
            [
                'name_ar' => 'ترفية',
                'name_en' => 'Entertainment',
            ],
            [
                'name_ar' => 'أكاديمية',
                'name_en' => 'Academic',
            ],
            [
                'name_ar' => 'أعمال',
                'name_en' => 'Business',
            ],
            [
                'name_ar' => 'أداء مباشر',
                'name_en' => 'Live Performance',
            ],
            [
                'name_ar' => 'فنون',
                'name_en' => 'Art',
            ],
            [
                'name_ar' => 'ذكاء اصطناعي',
                'name_en' => 'AI',
            ],
            [
                'name_ar' => 'أخرى',
                'name_en' => 'Other',
            ],
        ];

        foreach ($categories as $category) {
            Category::query()->create([
                'name_ar' => $category['name_ar'],
                'name_en' => $category['name_en'],
            ]);
        }

    }
}
