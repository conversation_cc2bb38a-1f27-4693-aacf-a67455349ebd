<?php

declare(strict_types=1);

namespace Modules\Event\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;
use Modules\Event\Entities\Category;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\EventSetting;
use Modules\Event\Entities\Organization;

final class EventSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::query()->select('id')->get();
        $organizations = Organization::query()->select('id')->get();
        $events = [];

        foreach ($organizations as $organization) {
            foreach (range(0, 3) as $range) {
                $events[] = Event::factory()->make([
                    'organization_id' => $organization->id,
                    'category_id' => $categories->random()->id,
                ]);
            }
        }

        $events = collect($events)->map(
            fn ($event) => array_merge($event->toArray(), [
                'created_at' => now(),
                'updated_at' => now(),
            ])
        )->toArray();

        $eventsSettings = [];

        foreach ($events as $event) {
            $eventsSettings[] = EventSetting::factory()->make([
                'event_id' => $event['id'],
            ])->toArray();
        }

        Event::query()->insert($events);

        $logo = Storage::putFile(public_path('images/default/logo.png'));
        $banner = Storage::putFile(public_path('images/default/banner.jpg'));

        for ($i = 0; $i < count($eventsSettings); $i++) {
            $eventsSettings[$i] = array_merge(
                $eventsSettings[$i],
                [
                    'logo' => $logo,
                    'banner' => $banner
                ]
            );
        }

        EventSetting::query()->insert($eventsSettings);
    }
}
