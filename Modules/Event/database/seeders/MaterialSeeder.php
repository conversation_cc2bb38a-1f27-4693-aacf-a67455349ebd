<?php

declare(strict_types=1);

namespace Modules\Event\Database\Seeders;

use App\Enums\MediaCollection;
use Illuminate\Database\Seeder;
use Modules\Event\Entities\Agenda;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\Material;

final class MaterialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $events = Event::query()->select('id')->get();
        $agendas = Agenda::query()->select('id')->get();

        foreach ($events as $value) {
            $material = Material::factory()->create([
                'materialable_id' => $value->id,
                'materialable_type' => Event::class,
            ]);
            $material->addMedia(storage_path('materials/test.docx'))
                ->preservingOriginal()
                ->toMediaCollection(MediaCollection::MATERIAL_COLLECTION->value);
        }

        foreach ($agendas as $value) {
            $material = Material::factory()->create([
                'materialable_id' => $value->id,
                'materialable_type' => Agenda::class,
            ]);
            $material->addMedia(storage_path('materials/test.docx'))
                ->preservingOriginal()
                ->toMediaCollection(MediaCollection::MATERIAL_COLLECTION->value);
        }


    }
}
