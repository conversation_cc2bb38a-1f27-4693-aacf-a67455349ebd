<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Event\Entities\Engagement;
use Modules\Event\Entities\Response;
use Modules\Event\Enums\EngagementType;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('responses', function (Blueprint $table): void {
            $table->string('type')->nullable();

        });


        Engagement::query()->get()->each(function (Engagement $engagement): void {
            $type = $engagement->engagement_type === EngagementType::QUESTIONS->value ? $engagement->engagement_type : $engagement->poll_type;
            $engagement->responses->each(function (Response $response) use ($type): void {
                $response->type = $type;
                $response->save();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('responses', function (Blueprint $table): void {
            $table->dropColumn('type');
        });
    }
};
