<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Modules\Event\Entities\Event::query()->with([
            'settings',
            'tags',
        ])->each(function (Modules\Event\Entities\Event $event, int $index): void {

            if (null !== $event->settings) {
                $event->settings->update([
                    'attendee_badge_color' => $event->settings->primary_color,
                    'speaker_badge_color' => $event->settings->primary_color,
                    'supervisor_badge_color' => $event->settings->primary_color,
                ]);
            }

            if ($event->tags->count() > 0) {
                $event->tags()->each(function (Modules\Event\Entities\Tag $tag, int $index) use ($event): void {
                    if (null !== $event->settings && null !== $event->settings->primary_color) {
                        $tag->update([
                            'color' => $event->settings->primary_color,
                        ]);
                    }
                });
            }
        });
    }
};
