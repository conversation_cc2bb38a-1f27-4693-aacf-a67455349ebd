<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_access', function (Blueprint $table): void {

            $table->foreignSnowflake('event_id')->index()->constrained();
            $table->foreignSnowflake('organizer_id')->index()->constrained();
            $table->unique(['event_id', 'organizer_id']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_access');
    }
};
