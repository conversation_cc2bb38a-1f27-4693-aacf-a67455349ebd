<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_settings', function (Blueprint $table): void {
            $table->snowflake()->primary();

            $table->string('logo');
            $table->string('banner')->nullable();

            $table->string('description_ar', 1025);
            $table->string('description_en', 1025)->nullable();

            $table->string('latitude');
            $table->string('longitude');
            $table->string('location');

            $table->string('primary_color');
            $table->string('secondary_color')->nullable();

            $table->foreignSnowflake('event_id')->index()->constrained()->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_settings');
    }
};
