<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('response_likes', function (Blueprint $table): void {
            $table->snowflake()->primary();

            $table->foreignSnowflake('response_id')->constrained('responses');
            $table->foreignSnowflake('user_id')->constrained('users');

            $table->unique(['response_id', 'user_id']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('response_likes');
    }
};
