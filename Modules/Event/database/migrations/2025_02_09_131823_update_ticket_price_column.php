<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Event\Entities\EventSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        EventSetting::whereNull('ticket_price')->update(['ticket_price' => 0]);
        EventSetting::whereNull('template_id')->update(['template_id' => 1]);

        Schema::table('event_settings', function (Blueprint $table): void {
            $table->decimal('ticket_price')->default(0)->change();
            $table->unsignedTinyInteger('template_id')->default(1)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('event_settings', function (Blueprint $table): void {
            $table->decimal('ticket_price')->nullable()->change();
            $table->unsignedTinyInteger('template_id')->nullable()->change();
        });
    }
};
