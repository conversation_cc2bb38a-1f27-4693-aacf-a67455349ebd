<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Modules\Event\Concerns\HasDefaultTags;

return new class extends Migration
{
    use HasDefaultTags;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Modules\Event\Entities\Event::query()->whereDoesntHave('tags')->each(
            fn (Modules\Event\Entities\Event $event, int $index): mixed => $event->tags()->createMany($this->tags())
        );
    }
};
