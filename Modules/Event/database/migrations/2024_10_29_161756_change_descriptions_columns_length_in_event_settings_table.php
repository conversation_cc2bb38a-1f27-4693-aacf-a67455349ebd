<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('event_settings', function (Blueprint $table): void {
            $table->string('description_ar', 1023)->change();
            $table->string('description_en', 1023)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('event_settings', function (Blueprint $table): void {
            $table->string('description_ar', 255)->change();
            $table->string('description_en', 255)->nullable()->change();
        });
    }
};
