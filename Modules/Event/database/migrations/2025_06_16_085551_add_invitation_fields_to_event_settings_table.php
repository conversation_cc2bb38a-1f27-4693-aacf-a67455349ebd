<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Event\Enums\InvitationMethod;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('event_settings', function (Blueprint $table): void {
            $table->string('invitation_method')->default(InvitationMethod::IN_APP->value);
            $table->string('out_app_communication_method')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('event_settings', function (Blueprint $table): void {
            $table->dropColumn(['invitation_method', 'out_app_communication_method']);
        });
    }
};
