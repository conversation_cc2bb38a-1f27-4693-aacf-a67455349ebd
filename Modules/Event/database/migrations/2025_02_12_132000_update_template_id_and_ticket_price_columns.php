<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\EventSetting;
use Modules\Event\Enums\EventType;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('event_settings', function (Blueprint $table): void {
            $table->decimal('ticket_price')->nullable()->default(null)->change();
            $table->unsignedTinyInteger('template_id')->nullable()->default(null)->change();
        });

        $privateEventIds = Event::query()
            ->where('type', EventType::PRIVATE->value)
            ->pluck('id');

        EventSetting::query()
            ->whereIn('event_id', $privateEventIds)
            ->where('ticket_price', 0)
            ->update(['ticket_price' => null]);

        EventSetting::query()
            ->whereIn('event_id', $privateEventIds)
            ->where('template_id', 1)
            ->update(['template_id' => null]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $privateEventIds = Event::query()
            ->where('type', 'private')
            ->pluck('id');

        EventSetting::query()
            ->whereIn('event_id', $privateEventIds)
            ->whereNull('ticket_price')
            ->update(['ticket_price' => 0]);

        EventSetting::query()
            ->whereIn('event_id', $privateEventIds)
            ->whereNull('template_id')
            ->update(['template_id' => 1]);

        Schema::table('event_settings', function (Blueprint $table): void {
            $table->decimal('ticket_price')->default(0)->change();
            $table->unsignedTinyInteger('template_id')->default(1)->change();
        });
    }
};
