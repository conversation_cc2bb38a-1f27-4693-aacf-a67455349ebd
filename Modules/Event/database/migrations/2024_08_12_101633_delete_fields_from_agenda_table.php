<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Event\Entities\Agenda;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $agendas = Agenda::query()->get();

        foreach ($agendas as $agenda) {
            $agenda->starts_at = Carbon\Carbon::parse($agenda->date)->setTimeFromTimeString($agenda->start_at);
            $agenda->ends_at = Carbon\Carbon::parse($agenda->date)->setTimeFromTimeString($agenda->end_at);
            $agenda->save();
        }

        Schema::table('agendas', function (Blueprint $table): void {
            $table->dropColumn(['date', 'start_at', 'end_at']);
            $table->renameColumn('starts_at', 'start_at');
            $table->renameColumn('ends_at', 'end_at');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agendas', function (Blueprint $table): void {
            $table->dropColumn(['date', 'start_at', 'end_at']);
        });
    }
};
