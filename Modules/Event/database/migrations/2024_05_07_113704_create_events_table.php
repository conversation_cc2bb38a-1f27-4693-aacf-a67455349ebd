<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Event\Enums\EventStatus;
use Modules\Event\Enums\EventType;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', static function (Blueprint $table): void {
            $table->snowflake()->primary();

            $table->string('type')->default(EventType::PUBLIC->value);

            $table->string('name_ar');
            $table->string('name_en')->nullable();
            $table->string('domain')->nullable()->unique();

            $table->string('status')->default(EventStatus::UPCOMING);

            $table->boolean('published')->default(true);

            $table->timestamp('start_at');
            $table->timestamp('end_at');

            $table->string('qr_code')->nullable();
            $table->string('check_in_type');

            $table->foreignSnowflake('organization_id')->constrained()->nullOnDelete();
            $table->foreignSnowflake('category_id')->constrained()->nullOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
