<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Event\Entities\Agenda;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('agendas', function (Blueprint $table): void {
            $table->integer('seats_available')->nullable();
        });

        $agendas = Agenda::query()->get();

        foreach ($agendas as $agenda) {
            if ($agenda->seats_number > 0) {
                $participantsCount = $agenda->attendees()->count();
                $agenda->update([
                    'seats_available' => $agenda->seats_number - $participantsCount
                ]);
            }
        }
    }

    public function down(): void
    {
        Schema::table('agendas', function (Blueprint $table): void {
            $table->dropColumn('seats_available');
        });
    }
};
