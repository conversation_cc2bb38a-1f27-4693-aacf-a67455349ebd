<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Modules\Event\Entities\Event;
use Modules\Event\Entities\User;
use Modules\Event\Enums\DefaultTags;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Event::query()->with([
            'invitedAttendees',
            'invitedSpeakers',
            'invitedSupervisors',
        ])->each(function (Event $event): void {
            $attendeeTag = $event->tags()->where(
                'name',
                DefaultTags::ATTENDEE
            )->first();

            $speakerTag = $event->tags()->where(
                'name',
                DefaultTags::SPEAKER
            )->first();

            $supervisorTag = $event->tags()->where(
                'name',
                DefaultTags::SUPERVISOR
            )->first();

            $event->invitedAttendees()->each(function (User $user) use ($event, $attendeeTag): void {
                $user->events()->syncWithoutDetaching([
                    $event->id => [
                        'tag_id' => $attendeeTag->id ?? null,
                    ],
                ]);
            });

            $event->invitedSpeakers()->each(function (User $user) use ($event, $speakerTag): void {
                $user->events()->syncWithoutDetaching([
                    $event->id => [
                        'tag_id' => $speakerTag->id ?? null,
                    ],
                ]);
            });

            $event->invitedSupervisors()->each(function (User $user) use ($event, $supervisorTag): void {
                $user->events()->syncWithoutDetaching([
                    $event->id => [
                        'tag_id' => $supervisorTag->id ?? null,
                    ],
                ]);
            });
        });
    }
};
