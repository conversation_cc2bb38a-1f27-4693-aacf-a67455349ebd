<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agendas', function (Blueprint $table): void {
            $table->snowflake()->primary();
            $table->string('title');
            $table->string('color');
            $table->string('location');
            $table->string('description', 1025);
            $table->date('date');
            $table->time('start_at');
            $table->time('end_at');
            $table->integer('seats_number')->nullable();
            $table->foreignSnowflake('event_id')->index()->constrained();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agendas');
    }
};
