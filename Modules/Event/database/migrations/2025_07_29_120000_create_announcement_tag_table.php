<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('announcement_tag', function (Blueprint $table): void {
            $table->foreignSnowflake('announcement_id')->index();
            $table->foreignSnowflake('tag_id')->index();

            $table->timestamps();

            $table->unique(['announcement_id', 'tag_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('announcement_tag');
    }
};
