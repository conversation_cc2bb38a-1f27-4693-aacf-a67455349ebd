<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('event_settings', function (Blueprint $table): void {
            $table->string('attendee_badge_color')->nullable()->after('badge_id');
            $table->string('speaker_badge_color')->nullable()->after('attendee_badge_color');
            $table->string('supervisor_badge_color')->nullable()->after('speaker_badge_color');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('event_settings', function (Blueprint $table): void {
            $table->dropColumn(['attendee_badge_color', 'speaker_badge_color', 'supervisor_badge_color']);
        });
    }
};
