<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('responses', function (Blueprint $table): void {
            $table->dropForeign(['created_by_id']);
            $table->foreignSnowflake('created_by_id')->nullable()->index()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('responses', function (Blueprint $table): void {
            $table->foreignSnowflake('created_by_id')->nullable(false)->dropIndex()->change();
            $table->foreign('created_by_id')->references('id')->on('users');
        });
    }
};
