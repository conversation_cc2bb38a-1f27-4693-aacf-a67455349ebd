<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('responses', function (Blueprint $table): void {
            $table->snowflake()->primary();
            $table->string('title');
            $table->integer('count')->default(0); //for likes , Number of selected option , Number of similar words.
            $table->foreignSnowflake('engagement_id')->constrained()->nullable();

            $table->foreignSnowflake('created_by_id')->constrained('users')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('responses');
    }
};
