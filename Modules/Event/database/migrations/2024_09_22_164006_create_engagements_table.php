<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('engagements', function (Blueprint $table): void {
            $table->snowflake()->primary();
            $table->string('title');
            $table->integer('duration');
            $table->boolean('is_published')->default(false);
            $table->string('engagement_type');
            $table->string('poll_type')->nullable();
            $table->timestamp('start_at')->nullable();
            $table->timestamp('end_at')->nullable();
            $table->foreignSnowflake('agenda_id')->constrained();

            $table->foreignSnowflake('created_by_id')->index()->constrained('organizers');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('engagements');
    }
};
