<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
</head>

<body
    style="margin: 0; padding: 0; direction: {{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}; background-color: #f9f9fb">
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin: 0; padding: 0; width: 100%">
        <tr>
            <td align="center" style="padding: 20px">
                <table width="600" cellpadding="0" cellspacing="0" border="0"
                    style="
              max-width: 700px;
              width: 100%;

              text-align: start;
            ">
                    <tr>
                        <td align="center" style="padding: 40px">
                            <img src="{{ asset('images/default/event_app.png') }}" alt="Logo"
                                style="width: 120px" />
                        </td>
                    </tr>
                    <tr>
                        <td align="start" style="padding: 40px; background-color: #ffffff">
                            <h2
                                style="
                    color: #000000;
                    margin: 0;
                    font-weight: 900;
                    text-align: center;
                  ">
                                {{ __('notification::mails.contact_us.title') }}
                            </h2>
                            <div>
                                <h4>{{ __('notification::mails.contact_us.sender_details') }}</h4>
                                <table
                                    style="width: 100%; border-spacing: 0; border-collapse: collapse; padding: 20px;">
                                    <tr>
                                        <td style="padding: 5px 0; vertical-align: middle; width: 1%;">
                                            <div style="display: flex; align-items: center; height: 100%;">•</div>
                                        </td>
                                        <td style="padding: 5px 3px;">
                                            <strong>{{ __('notification::mails.contact_us.name') }}</strong>
                                            {{ $name }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 5px 0; vertical-align: middle;">
                                            <div style="display: flex; align-items: center; height: 100%;">•</div>
                                        </td>
                                        <td style="padding: 5px 3px;">
                                            <strong>{{ __('notification::mails.contact_us.email') }}</strong>
                                            {{ $email }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 5px 0; vertical-align: middle;">
                                            <div style="display: flex; align-items: center; height: 100%;">•</div>
                                        </td>
                                        <td style="padding: 5px 3px;">
                                            <strong>{{ __('notification::mails.contact_us.mobile') }}</strong>
                                            {{ $mobile }}
                                        </td>
                                    </tr>
                                </table>



                            </div>

                            <p style="color: #000000; margin: 20px 0">
                                <span style="font-weight: 600">{{ __('notification::mails.contact_us.topic') }}</span>
                                {{ $topic }}
                            </p>
                            <h4 style="margin: 0">{{ __('notification::mails.contact_us.message') }}</h4>
                            <p style="color: #62636c; margin: 0">
                                {{ $userMessage }}
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>
