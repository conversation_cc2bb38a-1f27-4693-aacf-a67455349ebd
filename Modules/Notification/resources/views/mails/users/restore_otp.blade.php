@extends('notification::mails.layouts.content')

@section('content')
    @parent

    <!-- Email Main content -->
    <table class="row" style="margin: auto;">
        <tbody>
        <tr>
            <th class="small-12 columns first last">
                <table style="margin: auto;">
                    <tr>
                        <th>
                            <p dir="{{ app()->getLocale() === 'ar' ? "rtl" : "ltr"}}"
                               style="font-family: {{ getFontName() }}, sans-serif; color:black; font-size: 16px; margin-bottom: 16px; line-height: 45px;
                                    text-align: {{app()->getLocale() === 'ar' ? "right" : "left"}}  !important;">
                                {!! __('notification::mails.restore_account_otp.welcome_name', [
                                    'name' => '<strong>'.$name.'</strong>',
                                ]) !!}
                            </p>
                            <p dir="{{ app()->getLocale() === 'ar' ? "rtl" : "ltr"}}"
                               style="font-family: {{ getFontName() }}, sans-serif; color:black; font-size: 16px; margin-bottom: 16px;
                                    text-align: {{app()->getLocale() === 'ar' ? "right" : "left"}}  !important;">
                                {{ __('notification::mails.restore_account_otp.welcome_message') }}
                            </p>
                        </th>
                    </tr>
                </table>
            </th>
        </tr>
        </tbody>
    </table>
    <table class="row" style="margin: auto;">
        <tbody>
        <tr>
            <th class="small-12 columns first last">
                <table style="margin:16px auto">
                    <tr>
                        <th>
                            <center>
                                <table class="button success float-center">
                                    <tr>
                                        <td>
                                            @include('notification::mails.layouts.verification_code', [
                                                'code' => $otp,
                                            ])
                                        </td>
                                    </tr>
                                </table>
                            </center>
                        </th>
                        <th class="expander"></th>
                    </tr>
                </table>
            </th>
        </tr>
        </tbody>
        <tbody>
        <tr>
            <th class="small-12 columns first last">
                <table style="margin: auto;">
                    <tr>
                        <th>
                            <p dir="{{ app()->getLocale() === 'ar' ? "rtl" : "ltr"}}"
                               style="font-family: {{ getFontName() }}, sans-serif ;color:black;font-size: 16px !important; text-align: {{app()->getLocale() === 'ar' ? "right" : "left"}}  !important;">
                                {!! __('notification::mails.restore_account_otp.code_end_in', ['bold_text' => app()->getLocale() === 'ar' ? '<strong>لمدة 10 دقائق</strong>' : '<strong>10 minutes</strong>']) !!}
                            </p>
                        </th>
                        <th class="expander"></th>
                    </tr>
                </table>
            </th>
        </tr>
        </tbody>
        <tbody>
        <tr>
            <th class="small-12 columns first last">
                <table style="margin: auto;">
                    <tr>
                        <th>
                            <p dir="{{ app()->getLocale() === 'ar' ? "rtl" : "ltr"}}"
                               style="font-family: {{ getFontName() }}, sans-serif ;color:black;font-size: 16px !important;
                                     text-align: {{app()->getLocale() === 'ar' ? "right" : "left"}}  !important;">
                                {{ __('notification::mails.restore_account_otp.ignore_this_mail') }}
                            </p>
                        </th>
                        <th class="expander"></th>
                    </tr>
                </table>
            </th>
        </tr>
        </tbody>
    </table>
@endsection
