@extends('notification::mails.users.content')

@section('content')
    @parent

    <!-- Email Main content -->
    <table class="row" style="margin: auto;">
        <tbody>
        <tr>
            <th class="small-12 columns first last">
                <table style="margin: auto;">
                    <tr>
                        <th>
                            <p dir="{{ app()->getLocale() === 'ar' ? "rtl" : "ltr"}}"
                               style="font-family: {{ getFontName() }}, sans-serif; color:black; font-size: 16px; margin-bottom: 16px; line-height: 45px;
                                    text-align: {{app()->getLocale() === 'ar' ? "right" : "left"}}  !important;">
                                {{ __('notification::mails.invitation_speaker.welcome_name') }}
                                <span style="font-weight: 500"> {{ $speakerName }} </span>
                            </p>
                            <p dir="{{ app()->getLocale() === 'ar' ? "rtl" : "ltr"}}"
                               style="font-family: {{ getFontName() }}, sans-serif; color:black; font-size: 16px; margin-bottom: 16px;
                                    text-align: {{app()->getLocale() === 'ar' ? "right" : "left"}}  !important;">
                                {!! __('notification::mails.invitation_speaker.intro_message',
                                [
                                    'eventName' => $eventName,
                                    'senderName' => $senderName,
                                ])
                            !!}
                            </p>
                        </th>
                    </tr>
                </table>
            </th>
        </tr>
        </tbody>
    </table>
    <table class="row" style="margin: auto;">
        <tbody>
        <tr>
            <th class="small-12 columns first last">
                <table style="margin: auto;">
                    <tr>
                        <th>
                            <p dir="{{ app()->getLocale() === 'ar' ? "rtl" : "ltr"}}"
                               style="font-family: {{ getFontName() }}, sans-serif ;color:black;font-size: 16px !important;
                                     text-align: {{app()->getLocale() === 'ar' ? "right" : "left"}}  !important;">
                                {{ __('notification::mails.invitation_speaker.support') }}
                               <a> <EMAIL> </a>
                            </p>
                        </th>
                        <th class="expander"></th>
                    </tr>
                </table>
            </th>
        </tr>
        </tbody>
    </table>
@endsection
