@extends('notification::mails.users.content')

@section('content')
    @parent

    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="margin-bottom: 30px;">
        <tr>
            <td style="padding: 0 20px;">
                <!-- Welcome Message -->
                <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                    style="font-family: {{ getFontName() }}, sans-serif;
                          color: #000000;
                          font-size: 16px;
                          line-height: 1.6;
                          margin: 0 0 30px 0;
                          text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                    {!! __('notification::mails.invite_attendee.welcome_message', [
                        'name' => $name,
                        'date' => $date,
                        'location' => $location,
                    ]) !!}
                </p>

                <!-- Event Details Section -->
                <div style="margin-bottom: 30px;">
                    <h3 dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                        style="font-family: {{ getFontName() }}, sans-serif;
                               color: #000000;
                               font-size: 16px;
                               font-weight: bold;
                               margin: 0 0 15px 0;
                               text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                        {{ __('notification::mails.invite_attendee.event_details') }}
                    </h3>

                    <table dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" cellpadding="0" cellspacing="0"
                        border="0" width="100%"
                        style="text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }}">
                        <tr>
                            <td style="padding-bottom: 10px;">
                                <span style="color: #000000;">• {{ __('notification::mails.invite_attendee.title') }}
                                </span>
                                <span style="color: #000000;">{{ $name }}</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding-bottom: 10px;">
                                <span style="color: #000000;">• {{ __('notification::mails.invite_attendee.date') }}
                                </span>
                                <span style="color: #000000;">{{ $date }}</span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span style="color: #000000;">• {{ __('notification::mails.invite_attendee.location') }}
                                </span>
                                <span style="color: #000000;">{{ $location }}</span>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Instructions -->
                <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                    style="font-family: {{ getFontName() }}, sans-serif;
                          color: #000000;
                          font-size: 16px;
                          line-height: 1.6;
                          margin: 0 0 20px 0;
                          text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                    {{ __('notification::mails.invite_attendee.instruction') }}
                </p>

                <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                    style="font-family: {{ getFontName() }}, sans-serif;
                          color: #000000;
                          font-size: 16px;
                          line-height: 1.6;
                          margin: 0 0 20px 0;
                          text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                    {{ __('notification::mails.invite_attendee.see_you') }}
                </p>

                <!-- App Store Links -->
                <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                    style="font-family: {{ getFontName() }}, sans-serif;
                          color: #000000;
                          font-size: 16px;
                          line-height: 1.6;
                          margin: 0 0 20px 0;
                          text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                    {{ __('notification::mails.invite_attendee.get_app') }}
                </p>

                <!-- Store Buttons -->
                <table cellpadding="0" cellspacing="0" border="0" width="100%" style="margin-top: 20px;">
                    <tr>
                        <td align="{{ app()->getLocale() === 'ar' ? 'right' : 'left' }}">
                            <table cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <td style="padding-right: 10px;">
                                        <a href="https://apps.apple.com/ye/app/eventapp-for-attendees/id6505038453"
                                            target="_blank">
                                            <img src="{{ asset('images/default/app_store.png') }}"
                                                alt="Download on App Store" width="180" height="60"
                                                style="display: block;">
                                        </a>
                                    </td>
                                    <td style="padding-left: 10px;">
                                        <a href="https://play.google.com/store/apps/details?id=com.bootfi.eventsapp"
                                            target="_blank">
                                            <img src="{{ asset('images/default/google_play.png') }}"
                                                alt="Get it on Google Play" width="180" height="60"
                                                style="display: block;">
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
@endsection
