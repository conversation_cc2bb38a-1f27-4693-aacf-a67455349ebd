@extends('notification::mails.users.content')

@section('content')
    @parent

    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="margin-bottom: 30px;">
        <tr>
            <td style="padding: 0 20px;">
                <!-- Welcome Message -->
                <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                    style="font-family: {{ getFontName() }}, sans-serif;
                          color: #000000;
                          font-size: 16px;
                          line-height: 1.6;
                          margin: 0 0 10px 0;
                          text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                    {!! __('notification::mails.out_app_invitation.greeting', ['name' => $name]) !!}
                </p>

                <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                    style="font-family: {{ getFontName() }}, sans-serif;
                          color: #000000;
                          font-size: 16px;
                          line-height: 1.6;
                          margin: 0 0 30px 0;
                          text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                    {!! __('notification::mails.out_app_invitation.intro', [
                        'organizer_name' => $organizer_name,
                        'event_name' => $event_name,
                        'invitee_class' => $invitee_class,
                    ]) !!}
                </p>

                <!-- Event Details Section -->
                <table dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" style="margin-bottom: 30px; width: 100%;">
                    <tr>
                        <td style="text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                            <h3 dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                style="font-family: {{ getFontName() }}, sans-serif;
                                       color: #000000;
                                       font-size: 16px;
                                       font-weight: bold;
                                       margin: 0 0 15px 0;
                                       text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                                {{ __('notification::mails.out_app_invitation.event_details') }}
                            </h3>
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                            <table dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" cellpadding="0" cellspacing="0"
                                border="0" width="100%"
                                style="text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                                <tr>
                                    <td dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                        style="padding-bottom: 10px; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                                        <span style="color: #000000;">•
                                            {{ __('notification::mails.out_app_invitation.event_name') }}</span>
                                        <span style="color: #000000;">{{ $event_name }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                        style="padding-bottom: 10px; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                                        <span style="color: #000000;">•
                                            {{ __('notification::mails.out_app_invitation.date') }}</span>
                                        <span style="color: #000000;">{{ $date }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                        style="padding-bottom: 10px; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                                        <span style="color: #000000;">•
                                            {{ __('notification::mails.out_app_invitation.time') }}</span>
                                        <span style="color: #000000;">{{ $time }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                        style="padding-bottom: 10px; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                                        <span style="color: #000000;">•
                                            {{ __('notification::mails.out_app_invitation.location') }}</span>
                                        <span style="color: #000000;">{{ $location }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                        style="text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                                        <span style="color: #000000;">•
                                            {{ __('notification::mails.out_app_invitation.map_link') }}</span>
                                        <a href="{{ $map_link }}"
                                            style="color: #0000EE;">{{ __('notification::mails.out_app_invitation.map_link_text') }}</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>

                <!-- Instructions -->
                <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                    style="font-family: {{ getFontName() }}, sans-serif;
                          color: #000000;
                          font-size: 16px;
                          line-height: 1.6;
                          margin: 0 0 20px 0;
                          text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                    {{ __('notification::mails.out_app_invitation.download_instruction') }}
                </p>

                <!-- Download Button -->
                <table dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" cellpadding="0" cellspacing="0"
                    border="0" width="100%" style="margin-top: 20px; margin-bottom: 20px;">
                    <tr>
                        <td dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                            style="text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                            <a dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" href="{{ $ticket_link }}"
                                target="_blank"
                                style="font-family: {{ getFontName() }}, sans-serif;
                                        background-color: #1a73e8;
                                        color: #ffffff;
                                        display: inline-block;
                                        padding: 15px 30px;
                                        text-decoration: none;
                                        border-radius: 8px;
                                        font-size: 18px;
                                        font-weight: bold;
                                        float: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                                {{ __('notification::mails.out_app_invitation.download_button') }}
                            </a>
                        </td>
                    </tr>
                </table>

                <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                    style="font-family: {{ getFontName() }}, sans-serif;
                          color: #000000;
                          font-size: 16px;
                          line-height: 1.6;
                          margin: 0 0 30px 0;
                          text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                    {{ __('notification::mails.out_app_invitation.bring_badge') }}
                </p>

                <!-- Closing -->
                <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                    style="font-family: {{ getFontName() }}, sans-serif;
                          color: #000000;
                          font-size: 16px;
                          line-height: 1.6;
                          margin: 0;
                          text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};">
                    {{ __('notification::mails.out_app_invitation.regards') }}<br>
                    {{ $organizer_name }}
                </p>
            </td>
        </tr>
    </table>
@endsection
