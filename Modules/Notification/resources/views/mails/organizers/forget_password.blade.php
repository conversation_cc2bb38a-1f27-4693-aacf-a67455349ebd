@extends('notification::mails.layouts.content')

@section('content')
    @parent

    <!-- Email Main content -->
    <table class="row" style="margin: auto;">
        <tbody>
            <tr>
                <th class="small-12 large-12 columns first last">
                    <table style="margin: auto;">
                        <tr>
                            <th>
                                <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                    style="font-family: {{ getFontName() }}, sans-serif; color:black; font-size: 16px; margin-bottom: 16px; line-height: 45px; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }} !important;">
                                    {!! __('notification::mails.forget_password.welcome_name', [
                                        'name' => $name,
                                    ]) !!}
                                </p>
                                <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                    style="font-family: {{ getFontName() }}, sans-serif; color:black; font-size: 16px; margin-bottom: 16px; line-height: 45px; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }} !important;">
                                    {!! __('notification::mails.forget_password.welcome_message') !!}
                                </p>
                                <p dir="{{ app()->getLocale() === 'ar' ? "rtl" : "ltr"}}"
                                    style="font-family: {{ getFontName() }}, sans-serif ;color:black; font-size: 16px !important; text-align: {{app()->getLocale() === 'ar' ? "right" : "left"}}  !important;">
                                    {{ __('notification::mails.forget_password.code_is') }}
                                </p>
                            </th>
                        </tr>
                    </table>
                </th>
            </tr>
        </tbody>
    </table>
    <table class="row" style="margin: auto;">
        <tbody>
            <tr>
                <th class="small-12 large-12 columns first last">
                    <table style="margin:16px auto">
                        <tr>
                            <th>
                                <center>
                                    <table class="button success float-center">
                                        <tr>
                                            <td>
                                                @include('notification::mails.layouts.verification_code', [
                                                    'code' => $otp,
                                                ])
                                            </td>
                                        </tr>
                                    </table>
                                </center>
                            </th>
                            <th class="expander"></th>
                        </tr>
                    </table>
                </th>
            </tr>
        </tbody>
        <tbody>
            <tr>
                <th class="small-12 large-12 columns first last">
                    <table style="margin: auto;">
                        <tr>
                            <th>
                                <p dir="{{ app()->getLocale() === 'ar' ? "rtl" : "ltr"}}"
                                    style="font-family: {{ getFontName() }}, sans-serif ;color:black;font-size: 16px !important; text-align: {{app()->getLocale() === 'ar' ? "right" : "left"}}  !important;">
                                    {{ __('notification::mails.forget_password.code_end_in') }}
                                </p>
                            </th>
                            <th class="expander"></th>
                        </tr>
                    </table>
                </th>
            </tr>
        </tbody>
        <tbody>
            <tr>
                <th class="small-12 large-12 columns first last">
                    <table style="margin: auto;">
                        <tr>
                            <th>
                                <p dir="{{ app()->getLocale() === 'ar' ? "rtl" : "ltr"}}"
                                    style="font-family: {{ getFontName() }}, sans-serif ;color:black;font-size: 16px !important;
                                     text-align: {{app()->getLocale() === 'ar' ? "right" : "left"}}  !important;">
                                    {{ __('notification::mails.forget_password.ignore_this_mail') }}
                                </p>
                            </th>
                            <th class="expander"></th>
                        </tr>
                    </table>
                </th>
            </tr>
        </tbody>
    </table>
@endsection
