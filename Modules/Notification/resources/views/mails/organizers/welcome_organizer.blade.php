 @extends('notification::mails.layouts.content')

 @section('content')
     @parent

     <!-- Email Main content -->
     <table class="row" style="margin: auto;">
         <tbody>
             <tr>
                 <th class="small-12 large-12 columns first last">
                     <table style="margin: auto;">
                         <tr>
                             <th>
                                 <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                     style="font-family: {{ getFontName() }}, sans-serif; color:black; font-size: 16px; margin-bottom: 16px; line-height: 45px; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }} !important;">
                                     {!! __('notification::mails.welcome.welcome_name', [
                                         'name' => $name,
                                     ]) !!}
                                 </p>
                                 <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                     style="font-family: {{ getFontName() }}, sans-serif; color:black; font-size: 16px; margin-bottom: 16px; line-height: 45px; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }} !important;">
                                     {!! __('notification::mails.welcome.welcome_message', [
                                         'inviter_name' => $inviter_name,
                                     ]) !!}
                                 </p>
                                 <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                     style="font-family: {{ getFontName() }}, sans-serif ;color:black; font-size: 16px !important; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }} !important;">
                                     {{ __('notification::mails.welcome.accept') }}
                                 </p>
                             </th>
                             <th class="expander"></th>
                         </tr>
                     </table>
                 </th>
             </tr>
         </tbody>
     </table>
     <table class="row" style="margin: auto;">
         <tbody>
             <tr>
                 <th class="small-12 large-12 columns first last">
                     <table style="margin:16px auto">
                         <tr>
                             <th>
                                 <center>
                                     <table class="button success float-center">
                                         <tr>
                                             <td>
                                                 @include('notification::mails.layouts.button', [
                                                     'content' => __('notification::mails.welcome.join'),
                                                     'url' => $url,
                                                 ])

                                             </td>
                                         </tr>
                                     </table>
                                 </center>
                             </th>
                             <th class="expander"></th>
                         </tr>
                     </table>
                 </th>
             </tr>
         </tbody>
         <tbody>
             <tr>
                 <th class="small-12 large-12 columns first last">
                     <table style="margin: auto;">
                         <tr>
                             <th>
                                 <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                     style="font-family: {{ getFontName() }}, sans-serif ;color:black;font-size: 16px !important; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }} !important;">
                                     {{ __('notification::mails.welcome.button_not_working') }}
                                 </p>
                                 <br />
                                 <p style="font-size: 16px !important; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }} !important;"
                                     dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
                                     <a href="{{ $url }}" target="_blank">{{ $url }}</a>
                                 </p>
                             </th>
                             <th class="expander"></th>
                         </tr>
                     </table>
                 </th>
             </tr>
         </tbody>
         <tbody>
             <tr>
                 <th class="small-12 large-12 columns first last">
                     <table style="margin: auto;">
                         <tr>
                             <th>
                                 <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                     style="font-family: {{ getFontName() }}, sans-serif ;font-weight: 500;
                                    color:black; font-size: 16px !important; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }} !important;">
                                     {{ __('notification::mails.welcome.link_end') }}
                                 </p>
                             </th>
                             <th class="expander"></th>
                         </tr>
                     </table>
                 </th>
             </tr>
         </tbody>
         <tbody>
             <tr>
                 <th class="small-12 large-12 columns first last">
                     <table style="margin:16px auto">
                         <tr>
                             <td>
                             <th>
                                 <p dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"
                                     style="color:black; font-size: 16px !important; text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }} !important;"
                                     dir='{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}'>
                                     {{ __('notification::mails.welcome.support') }} <a href="mailto:<EMAIL>"
                                         style="color: #0363a9;"><EMAIL></a>
                             </th>
                             </td>
                         </tr>
                     </table>
                     </center>
                 <th class="expander"></th>
         </tbody>
     </table>
 @endsection
