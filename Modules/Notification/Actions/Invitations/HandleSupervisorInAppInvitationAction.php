<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Invitations;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Notification;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\User;
use Modules\Notification\Notifications\SendSupervisorInvitationEmail;
use Modules\User\Events\SupervisorInvitationEvent;

final class HandleSupervisorInAppInvitationAction
{
    public function __invoke(User $user, Model|Event $event, SupervisorInvitationEvent $invitationEvent): void
    {
        $event_name = 'ar' === $user->locale ? $event->name_ar : $event->name_en;

        Notification::send(
            $user,
            (new SendSupervisorInvitationEmail(
                $invitationEvent->senderName,
                $event_name ?: $event->name_ar,
                $event->settings?->logo,
                $event->settings?->primary_color
            ))->locale($user->locale)
        );
    }
}
