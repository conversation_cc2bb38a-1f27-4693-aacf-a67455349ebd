<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Invitations;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Notification;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\User;
use Modules\Notification\Notifications\SendSpeakerInvitationEmail;
use Modules\User\Events\SpeakerInvitationEvent;

final class HandleSpeakerInAppInvitationAction
{
    public function __invoke(User $user, Model|Event $event, SpeakerInvitationEvent $invitationEvent): void
    {
        $event_name = 'ar' === $user->locale ? $event->name_ar : $event->name_en;

        Notification::send(
            $user,
            (new SendSpeakerInvitationEmail(
                $invitationEvent->senderName,
                $event_name ?: $event->name_ar,
                $event->settings?->logo,
                $event->settings?->primary_color,
                $invitationEvent->page_url
            ))->locale($user->locale)
        );
    }
}
