<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Modules\Notification\Entities\Notification;
use Modules\Notification\Enums\AppNotifications;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Notification::where('notification_type', AppNotifications::ENGAGEMENTS->value)->delete();
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
