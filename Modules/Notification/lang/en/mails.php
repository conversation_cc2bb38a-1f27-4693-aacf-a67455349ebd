<?php

declare(strict_types=1);

return [
    'welcome' => [
        'subject' => 'Invitation',
        'welcome_name' => 'Hello <span style="font-weight: 500">:name</span>,',
        'welcome_message' => 'You’ve been invited by <span style="font-weight: 500">:inviter_name</span> to EventApp Platform to manage your organization’s events',
        'accept' => 'To accept the invitation and join, click on the following button:',
        'join' => 'Join',
        'button_not_working' => 'If the button above is not working, you can use the following link.',
        'link_end' => 'This invitation will expire within 5 days if it is not accepted.',
        'support' => 'If you need any help, or have other questions, don’t hesitate to contact us on our email:',
    ],
    'forget_password' => [
        'subject' => 'Password Reset',
        'welcome_name' => 'Dear <span style="font-weight: 500">:name</span>،',
        'welcome_message' => 'We received a request to reset the password of your account on EventApp Platform. For your safety, we created an OTP code which you’ll need to provide for your password to be reset.',
        'code_is' => 'Your OTP code is:',
        'code_end_in' => 'Please enter this code in the account verification page to reset your password. Note that this code will expire within 24 hours.',
        'ignore_this_mail' => 'If you’re not sure why you received this email, It’s possible that another party has requested to reset the account’s password. If so, You can safely ignore this email, and your account will still be secured.',
    ],
    'login_otp' => [
        'subject' => 'Login Otp',
        'welcome_name' => 'Dear :name،',
        'welcome_message' => 'We received a request to log into your account on EventApp Platform. For your safety, we created an OTP code which you’ll need to provide to be able to log into you account.',
        'code_is' => 'Your OTP code is:',
        'code_end_in' => 'Please enter this code in the account verification page to login. Note that this code will expire within :bold_text.',
        'ignore_this_mail' => 'If you’re not sure why you received this email, It’s possible that another party has requested to login. If so, you can safely ignore this email, and your account will still be secured.',
    ],
    'invitation_speaker' => [
        'subject' => 'Invitation to join',
        'welcome_name' => 'Hello،',
        'intro_message' => 'You have been invited by <span style="font-weight: 500">:senderName</span> as a speaker in event_Name event',
        'instruction' => 'To accept the invitation and join, click on the following button:',
        'button_not_working' => 'If the button above is not working, you can use the following link instead.',
        'join' => 'Join',
        'note' => 'This invitation will expire within 5 days if it is not accepted',
        'support' => 'If you need any help, or have other questions, don’t hesitate to contact us on our email:',
    ],

    'invite_attendee' => [
        'subject' => 'Invitation Email',
        'welcome_message' => 'We are happy to invite you to attend <span style="font-weight: 500;">:name</span> on <span style="font-weight: 500;">:date</span> in <span style="font-weight: 500;">:location</span>. In this email, you will find important information about the event, along with a link to download the event’s mobile application, witch will provide you with a personal entry card.',
        'description' => 'To gain the most out of this experience, please download the event’s application by clicking on the following link:',
        'instruction' => 'After downloading the application, log into the your account using the email address you used to register in the event. By doing that, you will be able to access your electronic entry card, event’s schedule, event’s location maps, among other useful information.',
        'see_you' => 'Looking forward to see you there!',
        'get_app' => 'Get the application through:',
        'event_details' => 'Event details: ',
        'title' => 'Title: ',
        'date' => 'Date: ',
        'location' => 'Location: ',

    ],
    'invitation_supervisor' => [
        'subject' => 'Invitation to join',
        'welcome_name' => 'Hello،',
        'intro_message' => 'You have been invited by <span style="font-weight: 500">:senderName</span> as a supervisor in event_Name event',
        'instruction' => 'To accept the invitation and join, click on the following button:',
        'button_not_working' => 'If the button above is not working, you can use the following link instead.',
        'join' => 'Join',
        'note' => 'This invitation will expire within 5 days if it is not accepted',
        'support' => 'If you need any help, or have other questions, don’t hesitate to contact us on our email:',
    ],
    'restore_account_otp' => [
        'subject' => 'Account Recovery',
        'welcome_name' => 'Dear :name،',
        'welcome_message' => 'To recover your account, please use the following one time password (OTP):',
        'code_end_in' => 'Don’t share you OTP with anyone. Please note that the OTP will expire within :bold_text',
        'ignore_this_mail' => 'If you are not sure why you are receiving this email, you can safely ignore it, and your account will still be secured.',
    ],

    'contact_us' => [
        'subject' => 'Contact Us Message',
        'title' => 'Inquiry Message',
        'sender_details' => 'Sender Details:',
        'name' => 'Name:',
        'email' => 'Email:',
        'mobile' => 'Phone Number:',
        'message' => 'Message:',
        'topic' => 'Subject:',
    ],

    'out_app_invitation' => [
        'greeting' => 'Dear :name,<br>Kind regards,',
        'intro' => 'We are honored and pleased at :organizer_name to invite you to our special event titled ":event_name". We believe that your presence as :invitee_class will have a great positive impact and will contribute to the success of this event and enrich it with your valuable experiences and opinions.',
        'event_details' => 'Event Details:',
        'event_name' => 'Event Name: ',
        'date' => 'Date: ',
        'time' => 'Time: ',
        'location' => 'Location: ',
        'map_link' => 'Location on Map: ',
        'map_link_text' => 'Click here to view location',
        'download_instruction' => 'Please kindly download your identification badge from the following link:',
        'download_button' => 'Click here to download the badge',
        'bring_badge' => 'Please bring the badge with you on the event day, as it will be used to facilitate the check-in process.',
        'regards' => 'Best regards,',
    ],
    'general_notification' => [
        'announcement' => 'Announcement',
        'subject' => 'Subject',
    ],
];
