<?php

declare(strict_types=1);

namespace Modules\Notification\Communicators;

use Facades\Modules\Event\Facades\EventModule;
use Illuminate\Database\Eloquent\Model;
use Modules\Notification\Contracts\EventCommunicatorContract;

final class EventCommunicator implements EventCommunicatorContract
{
    public static function getEvent(int $id): Model
    {
        return EventModule::getEvent($id);
    }
}
