<?php

declare(strict_types=1);

namespace Modules\Notification\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Notification\Listeners\EventSubscriber;
use Modules\Notification\Listeners\OrganizerEventSubscriber;
use Modules\Notification\Listeners\RestoreUserSubscriber;
use Modules\Notification\Listeners\SupportEventSubscriber;
use Modules\Notification\Listeners\UserEventSubscriber;

final class EventServiceProvider extends ServiceProvider
{
    protected $listen = [];

    /**
     * @var array
     */
    protected $subscribe = [
        SupportEventSubscriber::class,
        OrganizerEventSubscriber::class,
        UserEventSubscriber::class,
        EventSubscriber::class,
        RestoreUserSubscriber::class,
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function register(): void
    {
        parent::register();
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [];
    }
}
