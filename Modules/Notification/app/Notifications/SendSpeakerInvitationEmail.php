<?php

declare(strict_types=1);

namespace Modules\Notification\Notifications;

use App\Enums\Channels;
use App\Enums\Queues;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\Notification\Entities\User;

final class SendSpeakerInvitationEmail extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public string $senderName,
        public string $eventName,
        public string $logo_url,
        public string $color,
        public string $page_url,
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(User $notifiable): array
    {
        return [
            Channels::MAIL->value,
        ];
    }

    /**
     * Get the notification's delivery queues.
     */
    public function viaQueues(): array
    {
        return [
            Channels::MAIL->value => Queues::EMAILS->value,
        ];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(User $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject(__('notification::mails.invitation_speaker.subject'))
            ->view(
                'notification::mails.users.speaker_invitation',
                [
                    'speakerName' => $notifiable->name,
                    'senderName' => $this->senderName,
                    'eventName' => $this->eventName,
                    'logo' => $this->logo_url,
                    'color' => $this->color,
                    'page_url' => $this->page_url
                ]
            );
    }

}
