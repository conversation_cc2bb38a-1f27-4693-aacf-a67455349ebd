<?php

declare(strict_types=1);

namespace Modules\Notification\Notifications;

use App\Enums\Channels;
use App\Enums\Queues;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\Notification\Entities\User;

final class SendAttendeeInvitationEmailNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public string $logo_url,
        public string $name,
        public string $date,
        public string $location,
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(User $notifiable): array
    {
        return [
            Channels::MAIL->value,
        ];
    }

    public function viaQueues(): array
    {
        return [
            Channels::MAIL->value => Queues::EMAILS->value,
        ];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(User $notifiable): MailMessage
    {

        return (new MailMessage)
            ->subject(__('notification::mails.invite_attendee.subject'))
            ->view(
                'notification::mails.users.attendees.invite_attendee',
                [
                    'logo' => $this->logo_url,
                    'name' => $this->name,
                    'date' => $this->date,
                    'location' => $this->location,
                    'app_url' => $this->app_url = 'http://www.reallylong.link/rll/gqXVQXazHEmTzKU8dm8yVlzuKfCYbuFFVpqqSuvtGhlt__365tr8e2ku9FdcVAoaOhuBVbPtas/GhIQHSWLUtS7VL_z6bHNupgJdMy94ipwj2nwAiRWqpEChi6wUIAXChGYqMNWmjDXwPRfQTKNeR5I2245peoGlbYqxqRh',

                ]
            );
    }

    /**
     * Get the array representation of the notification.
     */
}
