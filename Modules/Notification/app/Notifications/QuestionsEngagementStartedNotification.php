<?php

declare(strict_types=1);

namespace Modules\Notification\Notifications;

use App\Channels\DatabaseChannel;
use App\Enums\Queues;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\User;
use Modules\Notification\Enums\AppNotifications;
use Modules\Notification\Enums\NotificationRoutes;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

final class QuestionsEngagementStartedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Event $event,
        public string $agenda_title,
        public int $session_id,
        public int $engagement_id,
        public string $engagement_type,
        public Carbon $end_at,
    ) {}

    public function via(User $notifiable): array
    {
        return [
            DatabaseChannel::class,
            FcmChannel::class,
        ];
    }

    public function viaQueues(): array
    {
        return [
            DatabaseChannel::class => Queues::IN_APP_NOTIFICATION->value,
            FcmChannel::class => Queues::PUSH_NOTIFICATION->value,
        ];
    }

    public function toDatabase(User $notifiable): array
    {
        $data = [
            'event_id' => $this->event->id,
            'session_id' => toString($this->session_id),
            'session_title' => $this->agenda_title,
            'engagement_id' => toString($this->engagement_id),
            'engagement_type' => $this->engagement_type,
            'end_at' => $this->end_at->format('Y-m-d\TH:i:s.u\Z'),
            'notification_type' => AppNotifications::ENGAGEMENTS->value,
            'title_ar' => $this->getTitle('ar'),
            'content_ar' => $this->getContent('ar'),
            'title_en' => $this->getTitle('en'),
            'content_en' => $this->getContent('en'),
        ];

        Log::debug(
            'QuestionsEngagementStartedNotification',
            $data
        );

        return $data;
    }

    public function toFcm(User $notifiable): FcmMessage
    {
        return (new FcmMessage(notification: new FcmNotification(
            title: $this->getTitle(),
            body: $this->getContent(),
        )))->data([
            'event_id' => $this->event->id,
            'event_name' => toString($this->event->name),
            'event_primary_color' => toString(($this->event->settings?->primary_color ?? '')),
            'event_secondary_color' => toString(($this->event->settings?->secondary_color ?? '')),
            'event_logo' => toString(($this->event->settings?->logo ?? '')),
            'event_can_see_attendee' => boolToString($this->event->can_see_attendee),
            'event_can_have_speaker' => boolToString($this->event->can_have_speaker),
            'event_can_have_supervisor' => boolToString($this->event->can_have_supervisor),
            'event_allow_chatting' => boolToString($this->event->allow_chatting),
            'event_has_materials' => boolToString($this->event->has_materials ?? false),
            'event_start_at' => $this->event->start_at,
            'event_end_at' => $this->event->end_at,
            'session_id' => toString($this->session_id),
            'session_title' => toString($this->agenda_title),
            'engagement_id' => toString($this->engagement_id),
            'engagement_type' => toString($this->engagement_type),
            'end_at' => $this->end_at->format('Y-m-d\TH:i:s.u\Z'),
            'notification_type' => AppNotifications::ENGAGEMENTS->value,
            'route_name' => NotificationRoutes::QUESTIONS->value,
        ])->custom([
            'android' => [
                'notification' => [
                    'sound' => 'default',
                    'notification_priority' => 'PRIORITY_MAX',
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics',
                ],
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                    ],
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics_ios',
                ],
            ],
        ]);
    }

    private function getTitle(?string $locale = null): string
    {
        return __('notification::notification.engagement.questions.title', [], $locale ?? app()->getLocale());
    }

    private function getContent(?string $locale = null): string
    {
        return __('notification::notification.engagement.questions.content', ['agenda' => $this->agenda_title], $locale ?? app()->getLocale());
    }
}
