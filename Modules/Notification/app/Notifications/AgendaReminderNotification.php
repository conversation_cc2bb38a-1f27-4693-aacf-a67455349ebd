<?php

declare(strict_types=1);

namespace Modules\Notification\Notifications;

use App\Channels\DatabaseChannel;
use App\Enums\Queues;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\User;
use Modules\Notification\Enums\AppNotifications;
use Modules\Notification\Enums\NotificationRoutes;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

final class AgendaReminderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */

    public function __construct(
        public Event $event,
        public string $title,
        public string $start_at,
        public mixed $agenda_id,
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(User $notifiable): array
    {
        return [
            DatabaseChannel::class,
            FcmChannel::class,
        ];
    }

    public function viaQueues(): array
    {
        return [
            DatabaseChannel::class => Queues::IN_APP_NOTIFICATION->value,
            FcmChannel::class => Queues::PUSH_NOTIFICATION->value,
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  User  $notifiable
     * @return array
     */
    public function toDatabase(User $notifiable): array
    {
        return [
            'event_id' => $this->event->id,
            'session_id' => $this->agenda_id,
            'notification_type' => AppNotifications::GENERAL,
            'title_ar' => $this->getTitle('ar'),
            'content_ar' => $this->getContent('ar'),
            'title_en' => $this->getTitle('en'),
            'content_en' => $this->getContent('en'),
            'route_name' => NotificationRoutes::SESSION_DETAILS->value,
        ];
    }


    public function toFcm(User $notifiable): FcmMessage
    {
        return (new FcmMessage(notification: new FcmNotification(
            title: $this->getTitle(),
            body: $this->getContent(),
        )))->data([
            'event_id' => $this->event->id,
            'event_name' => toString($this->event->name),
            'event_primary_color' => toString(($this->event->settings?->primary_color ?? '')),
            'event_secondary_color' => toString(($this->event->settings?->secondary_color ?? '')),
            'event_logo' => toString(($this->event->settings?->logo ?? '')),
            'event_can_see_attendee' => boolToString($this->event->can_see_attendee),
            'event_can_have_speaker' => boolToString($this->event->can_have_speaker),
            'event_can_have_supervisor' => boolToString($this->event->can_have_supervisor),
            'event_allow_chatting' => boolToString($this->event->allow_chatting),
            'event_has_materials' => boolToString($this->event->has_materials ?? false),
            'event_start_at' => $this->event->start_at,
            'event_end_at' => $this->event->end_at,
            'session_id' => toString($this->agenda_id),
            'title' => toString($this->getTitle()),
            'body' => toString($this->getContent()),
            'route_name' => NotificationRoutes::SESSION_DETAILS->value,
        ])->custom([
            'android' => [
                'notification' => [
                    'sound' => 'default',
                    'notification_priority' => 'PRIORITY_MAX',
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics',
                ],
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                    ],
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics_ios',
                ],
            ],
        ]);
    }

    public function getTitle(?string $locale = null): string
    {
        return __('notification::notification.reminder.title', ['title' => $this->title], $locale ?? app()->getLocale());
    }

    public function getContent(?string $locale = null): string
    {
        return __('notification::notification.reminder.content', ['title' => $this->title, 'time' => $this->getTime($locale)], $locale ?? app()->getLocale());
    }

    public function getTime(?string $locale = null): string
    {
        return Carbon::parse($this->start_at)
            ->addMinute()
            ->locale($locale ?? app()->getLocale())
            ->diffForHumans([
                'syntax' => CarbonInterface::DIFF_ABSOLUTE,
                'parts' => abs(Carbon::parse($this->start_at)->diffInHours()) > 1 ? 2 : 1,
                'join' => __('notification::notification.reminder.join', [], $locale ?? app()->getLocale()),
            ]);
    }
}
