<?php

declare(strict_types=1);

namespace Modules\Notification\Notifications;

use App\Channels\DatabaseChannel;
use App\Enums\Queues;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\User;
use Modules\Notification\Enums\AppNotifications;
use Modules\Notification\Enums\NotificationRoutes;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

final class AnnouncementNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public Event $event,
        public string $title,
        public string $content,
        public string $send_method,
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(User $notifiable): array
    {
        return [
            DatabaseChannel::class,
            FcmChannel::class,
        ];
    }

    public function viaQueues(): array
    {
        return [
            DatabaseChannel::class => Queues::IN_APP_NOTIFICATION->value,
            FcmChannel::class => Queues::PUSH_NOTIFICATION->value,
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  User  $notifiable
     * @return array
     */
    public function toDatabase(User $notifiable): array
    {
        return [
            'event_id' => $this->event->id,
            'notification_type' => AppNotifications::GENERAL,
            'title_ar' => $this->title,
            'content_ar' => $this->content,
            'title_en' => $this->title,
            'content_en' => $this->content,
            'send_method' => $this->send_method,
        ];
    }

    public function toFcm(User $notifiable): FcmMessage
    {
        return (new FcmMessage(notification: new FcmNotification(
            title: $this->title,
            body: $this->content,
        )))->data([
            'event_id' => toString($this->event->id),
            'event_name' => toString($this->event->name),
            'event_primary_color' => toString(($this->event->settings?->primary_color ?? '')),
            'event_secondary_color' => toString(($this->event->settings?->secondary_color ?? '')),
            'event_logo' => toString(($this->event->settings?->logo ?? '')),
            'event_can_see_attendee' => boolToString($this->event->can_see_attendee),
            'event_can_have_speaker' => boolToString($this->event->can_have_speaker),
            'event_can_have_supervisor' => boolToString($this->event->can_have_supervisor),
            'event_allow_chatting' => boolToString($this->event->allow_chatting),
            'event_has_materials' => boolToString($this->event->has_materials ?? false),
            'event_start_at' => $this->event->start_at,
            'event_end_at' => $this->event->end_at,
            'title' => toString($this->title),
            'body' => toString($this->content),
            'route_name' => NotificationRoutes::EVENT_DETAILS->value,
        ])->custom([
            'android' => [
                'notification' => [
                    'sound' => 'default',
                    'notification_priority' => 'PRIORITY_MAX',
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics',
                ],
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                    ],
                ],
                'fcm_options' => [
                    'analytics_label' => 'analytics_ios',
                ],
            ],
        ]);
    }
}
