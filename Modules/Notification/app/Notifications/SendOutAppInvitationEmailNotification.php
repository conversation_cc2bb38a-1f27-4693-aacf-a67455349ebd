<?php

declare(strict_types=1);

namespace Modules\Notification\Notifications;

use App\Enums\Channels;
use App\Enums\Queues;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\Notification\Entities\User;

final class SendOutAppInvitationEmailNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public string $logo_url,
        public string $name,
        public string $organizer_name,
        public string $event_name,
        public string $invitee_class,
        public string $date,
        public string $time,
        public string $location,
        public string $map_link,
        public string $ticket_link,
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(User $notifiable): array
    {
        return [
            Channels::MAIL->value,
        ];
    }

    public function viaQueues(): array
    {
        return [
            Channels::MAIL->value => Queues::EMAILS->value,
        ];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(User $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject(__('notification::mails.out_app_invitation.event_details'))
            ->view(
                'notification::mails.users.attendees.out_app_invitation',
                [
                    'logo' => $this->logo_url,
                    'name' => $this->name,
                    'organizer_name' => $this->organizer_name,
                    'event_name' => $this->event_name,
                    'invitee_class' => $this->invitee_class,
                    'date' => $this->date,
                    'time' => $this->time,
                    'location' => $this->location,
                    'map_link' => $this->map_link,
                    'ticket_link' => $this->ticket_link,
                ]
            );
    }
}
