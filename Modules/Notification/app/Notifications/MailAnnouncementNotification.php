<?php

declare(strict_types=1);

namespace Modules\Notification\Notifications;

use App\Enums\Channels;
use App\Enums\Queues;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\User;

final class MailAnnouncementNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public Event $event,
        public string $title,
        public string $content,
        public string $send_method,
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(User $notifiable): array
    {
        return [
            Channels::MAIL->value,
        ];
    }

    /**
     * Get the notification's delivery queues.
     */
    public function viaQueues(): array
    {
        return [
            Channels::MAIL->value => Queues::EMAILS->value,
        ];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(User $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject(__('notification::mails.announcement_notification.subject'))
            ->view(
                'notification::mails.users.announcement_notification',
                [
                    'title' => $this->title,
                    'content' => $this->content,
                    'organization_name' => $this->event->organization?->name,
                    'logo' => $this->event->settings?->logo,
                ]
            );
    }
}
