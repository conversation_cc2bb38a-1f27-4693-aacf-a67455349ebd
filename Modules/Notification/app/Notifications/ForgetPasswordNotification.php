<?php

declare(strict_types=1);

namespace Modules\Notification\Notifications;

use App\Enums\Channels;
use App\Enums\Queues;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\Notification\Entities\Admin;
use Modules\Notification\Entities\Organizer;
use Modules\Notification\Entities\User;

final class ForgetPasswordNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public string $senderName,
        public int $otp
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(Admin|Organizer|User $notifiable): array
    {
        return [
            Channels::MAIL->value,
        ];
    }

    /**
     * Get the notification's delivery queues.
     */
    public function viaQueues(): array
    {
        return [
            Channels::MAIL->value => Queues::EMAILS->value,
        ];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(Admin|Organizer|User $notifiable): MailMessage
    {
        $timeout = config('notification.otp.timeout');

        return (new MailMessage)
            ->subject(__('notification::mails.forget_password.subject'))
            ->view(
                'notification::mails.organizers.forget_password',
                [
                    'name' => $this->senderName,
                    'otp' => $this->otp,
                ]
            );
    }
}
