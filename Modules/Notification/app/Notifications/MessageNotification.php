<?php

declare(strict_types=1);

namespace Modules\Notification\Notifications;

use App\Channels\DatabaseChannel;
use App\Enums\Queues;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Modules\Notification\DataTransferObjects\Messaging\MessageNotificationHandlerData;
use Modules\Notification\Entities\User;
use Modules\Notification\Enums\AppNotifications;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

final class MessageNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly MessageNotificationHandlerData $notificationData
    ) {}

    public function via(User $notifiable): array
    {
        return [
            DatabaseChannel::class,
            FcmChannel::class,
        ];
    }

    public function viaQueues(): array
    {
        return [
            DatabaseChannel::class => Queues::IN_APP_NOTIFICATION->value,
            FcmChannel::class => Queues::PUSH_NOTIFICATION->value,
        ];
    }

    public function toDatabase(User $notifiable): array
    {
        return [
            'sender_id' => $this->notificationData->sender_id,
            'receiver_id' => $this->notificationData->receiver_id,
            'notification_type' => AppNotifications::GENERAL->value,
            'title_ar' => $this->notificationData->getArabicTitle(),
            'content_ar' => $this->notificationData->getArabicBody(),
            'title_en' => $this->notificationData->getEnglishTitle(),
            'content_en' => $this->notificationData->getEnglishBody(),
        ];
    }

    public function toFcm(User $notifiable): FcmMessage
    {
        return (new FcmMessage(notification: new FcmNotification(
            title: $this->notificationData->getFcmTitle(),
            body: $this->notificationData->getFcmBody(),
        )))->data($this->notificationData->getFcmData())
            ->custom([
                'android' => [
                    'notification' => [
                        'sound' => 'default',
                        'notification_priority' => 'PRIORITY_MAX',
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'analytics',
                    ],
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'sound' => 'default',
                        ],
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'analytics_ios',
                    ],
                ],
            ]);
    }
}
