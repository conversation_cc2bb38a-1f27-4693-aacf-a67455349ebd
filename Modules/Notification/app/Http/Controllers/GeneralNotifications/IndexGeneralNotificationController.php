<?php

declare(strict_types=1);

namespace Modules\Notification\Http\Controllers\GeneralNotifications;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Notification\Actions\GeneralNotifications\IndexGeneralNotificationAction;
use Modules\Notification\DataTransferObjects\GeneralNotifications\IndexGeneralNotificationData;
use Modules\Notification\Http\Requests\GeneralNotification\IndexGeneralNotificationRequest;
use Modules\Notification\Transformers\GeneralNotification\IndexGeneralNotificationResource;

final class IndexGeneralNotificationController extends Controller
{
    public function __invoke(IndexGeneralNotificationRequest $request): JsonResponse
    {
        $data = IndexGeneralNotificationData::from($request->validated());
        $notifications = app(IndexGeneralNotificationAction::class)($data);

        return sendSuccessResponse(
            data: IndexGeneralNotificationResource::collection($notifications)
                ->appends(request()->query())
        );
    }
}
