<?php

declare(strict_types=1);

namespace Modules\Notification\Http\Controllers\EngagementNotifications;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Notification\Actions\EngagementNotifications\IndexEngagementNotificationAction;
use Modules\Notification\DataTransferObjects\GeneralNotifications\IndexGeneralNotificationData;
use Modules\Notification\Http\Requests\GeneralNotification\IndexGeneralNotificationRequest;
use Modules\Notification\Transformers\EngagementNotifications\IndexEngagementNotificationResource;

final class IndexEngagementNotificationController extends Controller
{
    public function __invoke(IndexGeneralNotificationRequest $request): JsonResponse
    {
        $data = IndexGeneralNotificationData::from($request->validated());
        $notifications = app(IndexEngagementNotificationAction::class)($data);

        return sendSuccessResponse(
            data: IndexEngagementNotificationResource::collection($notifications)
                ->appends(request()->query())
        );
    }
}
