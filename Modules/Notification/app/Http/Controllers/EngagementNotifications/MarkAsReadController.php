<?php

declare(strict_types=1);

namespace Modules\Notification\Http\Controllers\EngagementNotifications;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Notification\Actions\EngagementNotifications\MarkAsReadAction;

final class MarkAsReadController extends Controller
{
    public function __invoke(int $id): JsonResponse
    {
        app(MarkAsReadAction::class)($id);

        return sendSuccessResponse();
    }
}
