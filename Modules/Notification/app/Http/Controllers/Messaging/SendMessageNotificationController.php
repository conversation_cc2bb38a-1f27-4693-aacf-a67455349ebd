<?php

declare(strict_types=1);

namespace Modules\Notification\Http\Controllers\Messaging;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Notification\Actions\Messaging\SendMessagingAction;
use Modules\Notification\DataTransferObjects\Messaging\SendMessageData;
use Modules\Notification\Http\Requests\Messaging\SendMessageRequest;

final class SendMessageNotificationController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param SendMessageRequest $request
     * @return JsonResponse
     */
    public function __invoke(SendMessageRequest $request): JsonResponse
    {
        (new SendMessagingAction)(
            SendMessageData::from(
                $request->validated()
            )
        );

        return sendSuccessResponse(
            __('notification::messages.sent_notification')
        );
    }
}
