<?php

declare(strict_types=1);

namespace Modules\Notification\Http\Controllers\Notifications;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Notification\Actions\Notifications\UnreadNotificationsIndicatorAction;

final class UnreadNotificationsIndicatorController extends Controller
{
    public function __invoke(int $id): JsonResponse
    {
        $exist = app(UnreadNotificationsIndicatorAction::class)($id);

        return sendSuccessResponse(
            data: ['has_unread_notifications' => $exist]
        );
    }
}
