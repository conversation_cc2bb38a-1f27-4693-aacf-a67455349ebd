<?php

declare(strict_types=1);

namespace Modules\Notification\Http\Requests\Messaging;

use App\Http\Requests\BaseFormRequest;

final class SendMessageRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'receiver_id' => [
                'required',
                'integer',
                'exists:users,id',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge(
            [
                'event_id' => $this->route('id'),
            ]
        );
    }
}
