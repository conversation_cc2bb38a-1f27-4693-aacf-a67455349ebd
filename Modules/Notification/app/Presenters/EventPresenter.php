<?php

declare(strict_types=1);

namespace Modules\Notification\Presenters;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Carbon;

trait EventPresenter
{
    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->{'name_' . app()->getLocale()} ?? $this->name_ar,
        );
    }

    protected function startAt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => Carbon::parse($value)->format('Y-m-d\TH:i:s.u\Z'),
        );
    }

    protected function endAt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => Carbon::parse($value)->format('Y-m-d\TH:i:s.u\Z'),
        );
    }
}
