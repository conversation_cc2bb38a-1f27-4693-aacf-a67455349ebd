<?php

declare(strict_types=1);

namespace Modules\Notification\Presenters;

use Illuminate\Database\Eloquent\Casts\Attribute;

trait NotificationPresenter
{
    protected function title(): Attribute
    {
        return new Attribute(
            get: fn ($value, $attributes) => $attributes['title_' . app()->getLocale()],
        );
    }

    protected function content(): Attribute
    {
        return new Attribute(
            get: fn ($value, $attributes) => $attributes['content_' . app()->getLocale()],
        );
    }
}
