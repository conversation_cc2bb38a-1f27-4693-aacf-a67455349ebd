<?php

declare(strict_types=1);

namespace Modules\Notification\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Notification;
use Modules\Notification\Entities\Event;
use Modules\Notification\Notifications\AgendaReminderNotification;

final class AgendaReminderJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $event_id,
        public int $agenda_id,
        public string $start_at,
        public int $reminder_period
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $event = Event::query()->with(
            'settings'
        )->withExists([
            'materials as has_materials' => fn ($query) => $query->where('is_published', true)
        ])->findOrFail($this->event_id);

        $settings =  $event->settings;
        $agenda = $event->agendas()->findOrFail($this->agenda_id);

        if ($settings->is_notification_enabled && $agenda->is_notification_enabled
            && $agenda->start_at === $this->start_at && $settings->reminder_period === $this->reminder_period) {
            $users = $event->invitedUsers;
            Notification::send(
                $users,
                (new AgendaReminderNotification(
                    $event,
                    $agenda->title,
                    $agenda->start_at,
                    $agenda->id,
                ))
            );
        }
    }
}
