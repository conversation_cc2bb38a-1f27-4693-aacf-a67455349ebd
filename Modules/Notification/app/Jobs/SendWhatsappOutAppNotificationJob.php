<?php

declare(strict_types=1);

namespace Modules\Notification\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\Notification\DataTransferObjects\Invitations\WhatsappOutAppNotificationData;

final class SendWhatsappOutAppNotificationJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public WhatsappOutAppNotificationData $data,
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $res = Http::withHeader(
            'Content-Type',
            'application/json',
        )->withToken(
            $this->data->accessToken
        )->post($this->data->url, $this->data->toArray());

        Log::error('WhatsappOutAppNotificationPayload', [
            'url' => $this->data->url,
            'payload' => $this->data->toArray(),
            'response_status' => $res->successful(),
            'response_status_code' => $res->status(),
            'response_json' => $res->json(),
            'response_body' => $res->body(),
        ]);
    }
}
