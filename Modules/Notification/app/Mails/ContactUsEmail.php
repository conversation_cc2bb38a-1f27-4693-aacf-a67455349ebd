<?php

declare(strict_types=1);

namespace Modules\Notification\Mails;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Queue\SerializesModels;

final class ContactUsEmail extends Mailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        public string $name,
        public string $email,
        public string $topic,
        public string $userMessage,
        public string $mobile,
    ) {
        $this->subject = __("notification::mails.contact_us.subject");
    }

    /**
     * Get the message content definition.
     *
     * @return Content
     */
    public function content(): Content
    {
        return new Content(
            view: 'notification::mails.contact_us.contact_us',
            with: [
                'name' => $this->name,
                'email' => $this->email,
                'mobile' => $this->mobile,
                'subject' => $this->topic,
                'userMessage' => $this->userMessage,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments(): array
    {
        return [];
    }
}
