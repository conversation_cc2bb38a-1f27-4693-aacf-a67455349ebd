<?php

declare(strict_types=1);

namespace Modules\Notification\Concerns;

use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Notifications\Notifiable;
use Modules\Notification\Entities\Notification;

trait HasNotifications
{
    use Notifiable;

    /**
     * Get the entity's notifications.
     *
     * @return MorphMany
     */
    public function notifications(): MorphMany
    {
        return $this->morphMany(
            Notification::class,
            'notifiable'
        )->latest();
    }

    /**
     * Get the general unread notifications.
     *
     * @return MorphMany
     */
    public function unreadGeneralNotifications()
    {
        return $this->notifications()->shouldBeGeneral()->unread();
    }

    /**
     * Get the jobs vacancies unread notifications.
     *
     * @return MorphMany
     */
    public function unreadEngagementNotifications()
    {
        return $this->notifications()->shouldBeEngagement()->unread();
    }
}
