<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\EngagementNotifications;

use Modules\Event\Entities\Event;
use Modules\Notification\Entities\User;

final class MarkAsReadAction
{
    public function __invoke(int $event_id): void
    {
        $event = Event::query()->findOrFail($event_id);

        $user = User::query()->findOrFail(getCurrentUser()->id);

        $user->notifications()
            ->shouldBeBasedOnEvent($event->id)
            ->shouldBeEngagement()
            ->update(['read_at' => now()]);
    }
}
