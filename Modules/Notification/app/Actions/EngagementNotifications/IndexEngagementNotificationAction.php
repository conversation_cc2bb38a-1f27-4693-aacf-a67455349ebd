<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\EngagementNotifications;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Event\Entities\Event;
use Modules\Notification\DataTransferObjects\GeneralNotifications\IndexGeneralNotificationData;
use Modules\Notification\Entities\User;

final class IndexEngagementNotificationAction
{
    public function __invoke(IndexGeneralNotificationData $data): LengthAwarePaginator
    {
        $event = Event::query()->findOrFail($data->event_id);

        $user = User::query()->findOrFail(getCurrentUser()->id);

        $notifications =  $user->notifications()
            ->shouldBeBasedOnEvent($event->id)
            ->shouldBeEngagement()->paginate($data->records_per_page);

        return $notifications;
    }
}
