<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Messaging;

use Illuminate\Support\Facades\Auth;
use Modules\Notification\DataTransferObjects\Messaging\MessageNotificationHandlerData;
use Modules\Notification\DataTransferObjects\Messaging\SendMessageData;
use Modules\Notification\Entities\User;
use Modules\Notification\Notifications\MessageNotification;

final class SendMessagingAction
{
    /**
     * @param SendMessageData $data
     *
     * @return void
     */
    public function __invoke(SendMessageData $data): void
    {
        /** @var User $sender */
        $sender = Auth::user();

        $senderProfile = $sender->profile;

        /** @var User $receiver */
        $receiver = User::query()->findOrFail($data->receiver_id);

        $receiver->notify(
            new MessageNotification(
                new MessageNotificationHandlerData(
                    sender_id: (string) $sender->id,
                    receiver_id: (string) $receiver->id,
                    sender_name: $sender->name,
                    sender_avatar: $senderProfile->avatar,
                    sender_company: $senderProfile->company,
                    sender_job: $senderProfile->job,
                    sender_gender: $senderProfile->gender,
                )
            )
        );
    }
}
