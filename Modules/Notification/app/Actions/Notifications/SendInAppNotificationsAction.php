<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Notifications;

use Illuminate\Support\Facades\Notification;
use Modules\Notification\DataTransferObjects\GeneralNotifications\AnnouncementData;
use Modules\Notification\Notifications\AnnouncementNotification;

final class SendInAppNotificationsAction
{
    public function __invoke(AnnouncementData $data): void
    {
        Notification::send(
            $data->users,
            (new AnnouncementNotification(
                $data->event,
                $data->title,
                $data->content,
                $data->send_method,
            ))
        );
    }
}
