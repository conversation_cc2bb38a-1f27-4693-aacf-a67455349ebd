<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Notifications;

use App\Enums\Queues;
use Illuminate\Http\Client\ConnectionException;
use Modules\Notification\DataTransferObjects\GeneralNotifications\AnnouncementData;
use Modules\Notification\DataTransferObjects\Invitations\WhatsappOutAppNotificationData;
use Modules\Notification\Entities\User;
use Modules\Notification\Jobs\SendWhatsappOutAppNotificationJob;

final class SendWhatsappOutAppNotificationsAction
{
    /**
     * @throws ConnectionException
     */
    public function __invoke(AnnouncementData $data): void
    {
        $phoneNumberId = config('services.whatsapp.phone_number_id');
        $accessToken = config('services.whatsapp.access_token');

        $url = "https://graph.facebook.com/v22.0/{$phoneNumberId}/messages";

        $data->users->each(function (User $user) use ($data, $url, $accessToken): void {
            $dto = WhatsappOutAppNotificationData::from([
                'accessToken' => $accessToken,
                'url' => $url,
                'recipientPhoneNumber' => $user->country->mobile_code . $user->mobile,
                'eventName' => 'ar' === $user->locale ? $data->event->name_ar : $data->event->name_en,
                'message' => $data->content,
            ]);

            SendWhatsappOutAppNotificationJob::dispatch(
                $dto,
            )->onQueue(
                Queues::HIGH->value
            );
        });
    }
}
