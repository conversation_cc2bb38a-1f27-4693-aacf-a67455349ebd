<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Notifications;

use Illuminate\Http\Client\ConnectionException;
use InvalidArgumentException;
use Modules\Event\Enums\OutAppCommunicationMethod;
use Modules\Notification\DataTransferObjects\GeneralNotifications\AnnouncementData;

final class HandleOutAppNotificationsAction
{
    /**
     * @throws ConnectionException
     */
    public function __invoke(AnnouncementData $data): void
    {
        match ($data->send_method) {
            OutAppCommunicationMethod::MAIL->value => app(SendEmailOutAppNotificationsAction::class)($data),
            OutAppCommunicationMethod::WHATSAPP->value => app(SendWhatsappOutAppNotificationsAction::class)($data),
            OutAppCommunicationMethod::BOTH->value => app(SendBothOutAppNotificationsAction::class)($data),
            default => throw new InvalidArgumentException('Invalid out app communication method'),
        };
    }
}
