<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Notifications;

use Illuminate\Http\Client\ConnectionException;
use Modules\Notification\DataTransferObjects\GeneralNotifications\AnnouncementData;

final class SendBothOutAppNotificationsAction
{
    /**
     * @throws ConnectionException
     */
    public function __invoke(AnnouncementData $data): void
    {
        app(SendEmailOutAppNotificationsAction::class)(
            $data
        );

        app(SendWhatsappOutAppNotificationsAction::class)(
            $data
        );
    }
}
