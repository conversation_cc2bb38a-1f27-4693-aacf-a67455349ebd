<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Notifications;

use Modules\Event\Entities\Event;
use Modules\Notification\Entities\User;

final class UnreadNotificationsIndicatorAction
{
    public function __invoke(int $event_id): bool
    {
        $event = Event::query()->findOrFail($event_id);

        $user = User::query()->findOrFail(getCurrentUser()->id);

        return $user->notifications()
            ->shouldBeBasedOnEvent($event->id)
            ->unread()
            ->exists();
    }
}
