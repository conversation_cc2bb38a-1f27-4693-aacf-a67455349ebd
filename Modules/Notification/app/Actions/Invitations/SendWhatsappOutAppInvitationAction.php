<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Invitations;

use App\Enums\Queues;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Client\ConnectionException;
use Modules\Notification\DataTransferObjects\Invitations\WhatsappOutAppInvitationData;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\Invitation;
use Modules\Notification\Entities\User;
use Modules\Notification\Jobs\SendWhatsappOutAppInvitationJob;

final class SendWhatsappOutAppInvitationAction
{
    /**
     * @throws ConnectionException
     */
    public function __invoke(User $user, Model|Event $event): void
    {
        $phoneNumberId = config('services.whatsapp.phone_number_id');
        $accessToken = config('services.whatsapp.access_token');

        $url = "https://graph.facebook.com/v22.0/{$phoneNumberId}/messages";

        $invitation = Invitation::query()->with([
            'tag',
        ])->where(fn (Builder $query) => $query->where(
            'event_id',
            $event->id
        )->where(
            'user_id',
            $user->id
        ))->firstOrFail();

        $eventStartAt = Carbon::parse($event->start_at)->timezone(config('app.default_timezone'));

        $data = WhatsappOutAppInvitationData::from([
            'accessToken' => $accessToken,
            'url' => $url,
            'language' => $user->locale,
            'recipientPhoneNumber' => $user->country->mobile_code . $user->mobile,
            'organizationName' => $event->organization->name,
            'eventName' => 'ar' === $user->locale ? $event->name_ar : $event->name_en,
            'eventDate' => $eventStartAt->format('Y-m-d'),
            'eventTime' => $eventStartAt->format('H:i'),
            'eventLocation' => $event->settings->location,
            'eventLocationUrl' => mapUrl($event->settings->latitude, $event->settings->longitude),
            'recipientName' => $user->name,
            'recipientClass' => $invitation->tag->name,
            'badgeUrl' => badgeUrl($event->id, $user->id),
            'eventId' => (int) $event->id,
            'invitedId' => (int) $user->id,
        ]);

        SendWhatsappOutAppInvitationJob::dispatch(
            $data
        )->onQueue(
            Queues::HIGH->value
        );
    }
}
