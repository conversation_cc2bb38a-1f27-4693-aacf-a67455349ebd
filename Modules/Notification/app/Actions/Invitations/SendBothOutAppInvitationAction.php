<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Invitations;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Client\ConnectionException;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\User;

final class SendBothOutAppInvitationAction
{
    /**
     * @throws ConnectionException
     */
    public function __invoke(User $user, Model|Event $event): void
    {
        app(SendEmailOutAppInvitationAction::class)(
            $user,
            $event
        );

        app(SendWhatsappOutAppInvitationAction::class)(
            $user,
            $event
        );
    }
}
