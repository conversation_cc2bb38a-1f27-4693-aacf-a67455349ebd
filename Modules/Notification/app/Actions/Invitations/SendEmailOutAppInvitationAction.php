<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Invitations;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Notification;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\Invitation;
use Modules\Notification\Entities\User;
use Modules\Notification\Notifications\SendOutAppInvitationEmailNotification;

final class SendEmailOutAppInvitationAction
{
    public function __invoke(User $user, Model|Event $event): void
    {
        $invitation = Invitation::where('user_id', $user->id)
            ->where('event_id', $event->id)
            ->firstOrFail();

        $eventStartAt = Carbon::parse($event->start_at)->timezone(config('app.default_timezone'));

        $event_name = 'ar' === $user->locale ? $event->name_ar : $event->name_en;
        $invitee_class = $invitation->type;
        $date = $eventStartAt->format('Y-m-d');
        $time = $eventStartAt->format('H:i');
        $location = $event->settings->location;
        $latitude = $event->settings->latitude;
        $longitude = $event->settings->longitude;
        $map_link = mapUrl($latitude, $longitude);
        $ticket_link = badgeUrl($event->id, $user->id);

        Notification::send(
            $user,
            (new SendOutAppInvitationEmailNotification(
                $event->settings->logo,
                $user->name,
                $event->organization->name,
                $event_name ?: $event->name_ar,
                $invitee_class,
                $date,
                $time,
                $location,
                $map_link,
                $ticket_link,
            ))->locale($user->locale)
        );
    }
}
