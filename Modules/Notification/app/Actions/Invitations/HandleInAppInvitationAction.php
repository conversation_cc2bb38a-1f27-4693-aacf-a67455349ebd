<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Invitations;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Notification;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\User;
use Modules\Notification\Notifications\SendAttendeeInvitationEmailNotification;

final class HandleInAppInvitationAction
{
    public function __invoke(User $user, Model|Event $event): void
    {
        $event_name = 'ar' === $user->locale ? $event->name_ar : $event->name_en;

        Notification::send(
            $user,
            (new SendAttendeeInvitationEmailNotification(
                $event->settings->logo,
                $event_name ?: $event->name_ar,
                Carbon::parse($event->start_at)->format('Y-m-d'),
                $event->settings->location,
            ))->locale($user->locale)
        );
    }
}
