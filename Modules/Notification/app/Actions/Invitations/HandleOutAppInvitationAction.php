<?php

declare(strict_types=1);

namespace Modules\Notification\Actions\Invitations;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Client\ConnectionException;
use InvalidArgumentException;
use Modules\Event\Enums\OutAppCommunicationMethod;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\User;

final class HandleOutAppInvitationAction
{
    /**
     * @throws ConnectionException
     */
    public function __invoke(User $user, Model|Event $event): void
    {
        match ($event->settings->out_app_communication_method) {
            OutAppCommunicationMethod::MAIL->value => app(SendEmailOutAppInvitationAction::class)($user, $event),
            OutAppCommunicationMethod::WHATSAPP->value => app(SendWhatsappOutAppInvitationAction::class)($user, $event),
            OutAppCommunicationMethod::BOTH->value => app(SendBothOutAppInvitationAction::class)($user, $event),
            default => throw new InvalidArgumentException('Invalid out app communication method'),
        };
    }
}
