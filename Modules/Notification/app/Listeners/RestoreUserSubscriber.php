<?php

declare(strict_types=1);

namespace Modules\Notification\Listeners;

use App\Enums\Queues;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Notification;
use Modules\Notification\Entities\User;
use Modules\Notification\Notifications\SendRestoreUserEmailNotification;
use Modules\User\Events\RestoreUserEvent;

final class RestoreUserSubscriber implements ShouldQueue
{
    public function sendRestoreUserEmail(RestoreUserEvent $event): void
    {
        $user = User::query()->findOrFail($event->id);

        Notification::send(
            $user,
            (new SendRestoreUserEmailNotification($event->otp)
            )->locale($user->locale)
        );
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @return array<string, string>
     */
    public function subscribe(Dispatcher $events): array
    {
        return [
            RestoreUserEvent::class => 'sendRestoreUserEmail',
        ];
    }

    /**
     * define subscriber queue name at runtime.
     */
    public function viaQueue(): string
    {
        return Queues::EMAILS->value;
    }
}
