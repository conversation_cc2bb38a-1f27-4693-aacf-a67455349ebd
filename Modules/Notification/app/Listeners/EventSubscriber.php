<?php

declare(strict_types=1);

namespace Modules\Notification\Listeners;

use App\Enums\Queues;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Events\Dispatcher;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Notification;
use InvalidArgumentException;
use LogicException;
use Modules\Event\Enums\EngagementType;
use Modules\Event\Enums\InvitationMethod;
use Modules\Event\Events\AgendaNotificationEnabled;
use Modules\Event\Events\AnnouncementCreated;
use Modules\Event\Events\EngagementPublished;
use Modules\Notification\Actions\Notifications\HandleInAppNotificationsAction;
use Modules\Notification\Actions\Notifications\HandleOutAppNotificationsAction;
use Modules\Notification\DataTransferObjects\GeneralNotifications\AnnouncementData;
use Modules\Notification\Entities\Agenda;
use Modules\Notification\Entities\Engagement;
use Modules\Notification\Entities\Event;
use Modules\Notification\Entities\User;
use Modules\Notification\Jobs\AgendaReminderJob;
use Modules\Notification\Notifications\PollEngagementStartedNotification;
use Modules\Notification\Notifications\QuestionsEngagementStartedNotification;

final class EventSubscriber implements ShouldQueue
{
    /**
     * @throws ConnectionException
     */
    public function userAnnouncement(AnnouncementCreated $event): void
    {
        $users = User::query()->whereHas('invitations', function ($q) use ($event): void {
            $q->where(
                'event_id',
                $event->event_id
            )->whereIn(
                'tag_id',
                $event->tag_ids
            );
        })->get();

        $model = Event::query()->with(
            'settings'
        )->withExists([
            'materials as has_materials' => fn ($query) => $query->where('is_published', true),
        ])->findOrFail($event->event_id);

        $data = AnnouncementData::from([
            'event' => $model,
            'users' => $users,
            'title' => $event->title,
            'content' => $event->content,
            'send_method' => $event->send_method,
        ]);

        match ($model->settings->invitation_method) {
            InvitationMethod::IN_APP->value => app(HandleInAppNotificationsAction::class)($data),
            InvitationMethod::OUT_APP->value => app(HandleOutAppNotificationsAction::class)($data),
            default => throw new InvalidArgumentException('Invalid notification method'),
        };
    }

    public function agendaNotification(AgendaNotificationEnabled $event): void
    {
        AgendaReminderJob::dispatch($event->event_id, $event->agenda_id, $event->start_at, $event->reminder_period)
            ->delay(Carbon::parse($event->start_at)->subMinutes($event->reminder_period));
    }

    public function handleEngagementPublished(EngagementPublished $event): void
    {
        $users = User::query()
            ->whereHas('attendedAgendas', fn ($query) => $query->where('agenda_id', $event->agenda_id))
            ->get();

        $engagement = Engagement::query()->where('id', $event->engagement_id)->first();

        $agenda = Agenda::query()->where('id', $event->agenda_id)->first();

        $appEvent = Event::query()->with(
            'settings'
        )->withExists([
            'materials as has_materials' => fn ($query) => $query->where('is_published', true)
        ])->findOrFail($event->event_id);

        $notification = match ($engagement->engagement_type) {
            EngagementType::POLL->value => new PollEngagementStartedNotification(
                $appEvent,
                $agenda->title,
                $event->agenda_id,
                $engagement->id,
                $engagement->engagement_type,
                $engagement->poll_type,
                $engagement->end_at,
            ),
            EngagementType::QUESTIONS->value => new QuestionsEngagementStartedNotification(
                $appEvent,
                $agenda->title,
                $event->agenda_id,
                $engagement->id,
                $engagement->engagement_type,
                $engagement->end_at,
            ),
            default => throw new LogicException("Unsupported engagement type: {$engagement->engagement_type}"),
        };

        Notification::send(
            $users,
            $notification
        );
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @return array<string, string>
     */
    public function subscribe(Dispatcher $events): array
    {
        return [
            AnnouncementCreated::class => 'userAnnouncement',
            AgendaNotificationEnabled::class => 'agendaNotification',
            EngagementPublished::class => 'handleEngagementPublished',
        ];
    }

    /**
     * define subscriber queue name at runtime.
     */
    public function viaQueue(): string
    {
        return Queues::PUSH_NOTIFICATION->value;
    }
}
