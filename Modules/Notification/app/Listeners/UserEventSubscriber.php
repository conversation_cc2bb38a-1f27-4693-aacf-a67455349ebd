<?php

declare(strict_types=1);

namespace Modules\Notification\Listeners;

use App\Enums\Queues;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Events\Dispatcher;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Notification;
use InvalidArgumentException;
use Modules\Event\Enums\InvitationMethod;
use Modules\Notification\Actions\Invitations\HandleInAppInvitationAction;
use Modules\Notification\Actions\Invitations\HandleOutAppInvitationAction;
use Modules\Notification\Actions\Invitations\HandleSpeakerInAppInvitationAction;
use Modules\Notification\Actions\Invitations\HandleSupervisorInAppInvitationAction;
use Modules\Notification\Contracts\EventCommunicatorContract;
use Modules\Notification\Entities\User;
use Modules\Notification\Notifications\SendLoginOtpEmailNotification;
use Modules\User\Events\AttendeeInvitationEmailEvent;
use Modules\User\Events\LoginEvent;
use Modules\User\Events\SpeakerInvitationEvent;
use Modules\User\Events\SupervisorInvitationEvent;

final class UserEventSubscriber implements ShouldQueue
{
    public function sendLoginOtpEmail(LoginEvent $event): void
    {
        $actor = User::query()->findOrFail($event->id);

        Notification::send(
            $actor,
            (new SendLoginOtpEmailNotification($event->otp)
            )->locale($actor->locale)
        );
    }

    /**
     * @throws ConnectionException
     */
    public function sendSpeakerInvitationEmail(SpeakerInvitationEvent $event): void
    {
        $actor = User::query()->findOrFail($event->id);
        $event_model = app(EventCommunicatorContract::class)::getEvent($event->event_id);

        match ($event_model->settings->invitation_method) {
            InvitationMethod::IN_APP->value => app(HandleSpeakerInAppInvitationAction::class)($actor, $event_model, $event),
            InvitationMethod::OUT_APP->value => app(HandleOutAppInvitationAction::class)($actor, $event_model),
            default => throw new InvalidArgumentException('Invalid invitation method'),
        };
    }

    /**
     * @throws ConnectionException
     */
    public function sendAttendeeInvitationEmail(AttendeeInvitationEmailEvent $event): void
    {
        $actor = User::query()->findOrFail($event->user_id);
        $event_model = app(EventCommunicatorContract::class)::getEvent($event->event_id);

        match ($event_model->settings->invitation_method) {
            InvitationMethod::IN_APP->value => app(HandleInAppInvitationAction::class)($actor, $event_model),
            InvitationMethod::OUT_APP->value => app(HandleOutAppInvitationAction::class)($actor, $event_model),
            default => throw new InvalidArgumentException('Invalid invitation method'),
        };
    }

    /**
     * @throws ConnectionException
     */
    public function sendSupervisorInvitationEmail(SupervisorInvitationEvent $event): void
    {
        $actor = User::query()->findOrFail($event->id);
        $event_model = app(EventCommunicatorContract::class)::getEvent($event->event_id);

        match ($event_model->settings->invitation_method) {
            InvitationMethod::IN_APP->value => app(HandleSupervisorInAppInvitationAction::class)($actor, $event_model, $event),
            InvitationMethod::OUT_APP->value => app(HandleOutAppInvitationAction::class)($actor, $event_model),
            default => throw new InvalidArgumentException('Invalid invitation method'),
        };
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @return array<string, string>
     */
    public function subscribe(Dispatcher $events): array
    {
        return [
            LoginEvent::class => 'sendLoginOtpEmail',
            SpeakerInvitationEvent::class => 'sendSpeakerInvitationEmail',
            AttendeeInvitationEmailEvent::class => 'sendAttendeeInvitationEmail',
            SupervisorInvitationEvent::class => 'sendSupervisorInvitationEmail',
        ];
    }

    /**
     * define subscriber queue name at runtime.
     */
    public function viaQueue(): string
    {
        return Queues::EMAILS->value;
    }
}
