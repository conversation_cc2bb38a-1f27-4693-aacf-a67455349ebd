<?php

declare(strict_types=1);

namespace Modules\Notification\Listeners;

use App\Enums\Queues;
use App\Exceptions\LogicalException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Events\Dispatcher;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;
use Modules\Notification\Entities\Admin;
use Modules\Notification\Entities\Organizer;
use Modules\Notification\Entities\User;
use Modules\Notification\Mails\ContactUsEmail;
use Modules\Notification\Notifications\ForgetPasswordNotification;
use Modules\Support\Events\ContactUsEvent;
use Modules\Support\Events\ForgetPasswordGenerated;

final class SupportEventSubscriber implements ShouldQueue
{
    /**
     * @throws LogicalException
     */
    public function sendForgetPasswordEmail(ForgetPasswordGenerated $event): void
    {
        $resettable = Str::after(
            $event->resettableType,
            '\\Entities\\'
        );

        $actor = match ($resettable) {
            'Organizer' => Organizer::query()->findOrFail($event->resettableId),
            'Admin' => Admin::query()->findOrFail($event->resettableId),
            'User' => User::query()->findOrFail($event->resettableId),
            default => throw new LogicalException("Unknown resettable type {$event->resettableType}", 405),
        };

        Notification::send(
            $actor,
            (new ForgetPasswordNotification(
                $event->senderName,
                $event->otp,
            ))->locale(
                $event->locale
            )
        );
    }


    public function sendContactUsEmail(ContactUsEvent $event): void
    {
        app()->setlocale($event->message_locale);

        $mail = new ContactUsEmail(
            $event->name,
            $event->email,
            $event->subject,
            $event->message,
            $event->mobile,
        );

        $mail->onQueue('emails');

        Mail::to(
            '<EMAIL>'
        )->queue(
            $mail
        );
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @return array<string, string>
     */
    public function subscribe(Dispatcher $events): array
    {
        return [
            ForgetPasswordGenerated::class => 'sendForgetPasswordEmail',
            ContactUsEvent::class => 'sendContactUsEmail',
        ];
    }

    /**
     * define subscriber queue name at runtime.
     */
    public function viaQueue(): string
    {
        return Queues::EMAILS->value;
    }
}
