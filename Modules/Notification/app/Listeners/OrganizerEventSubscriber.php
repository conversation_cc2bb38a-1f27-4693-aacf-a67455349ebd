<?php

declare(strict_types=1);

namespace Modules\Notification\Listeners;

use App\Enums\Queues;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Events\Dispatcher;
use Illuminate\Support\Facades\Notification;
use Modules\Notification\Entities\Organizer;
use Modules\Notification\Notifications\WelcomeNotification;
use Modules\Organizer\Events\OrganizerCreated;
use Modules\User\Events\OrganizerSpeakerCreated;

final class OrganizerEventSubscriber implements ShouldQueue
{
    public function sendWelcomeEmail(OrganizerCreated|OrganizerSpeakerCreated $event): void
    {
        $organizer = Organizer::query()->findOrFail($event->id);

        Notification::send(
            $organizer,
            (new WelcomeNotification(
                $event->senderName,
                $event->token
            ))->locale($organizer->locale)
        );
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @return array<string, string>
     */
    public function subscribe(Dispatcher $events): array
    {
        return [
            OrganizerCreated::class => 'sendWelcomeEmail',
            OrganizerSpeakerCreated::class => 'sendWelcomeEmail',
        ];
    }

    /**
     * define subscriber queue name at runtime.
     */
    public function viaQueue(): string
    {
        return Queues::EMAILS->value;
    }
}
