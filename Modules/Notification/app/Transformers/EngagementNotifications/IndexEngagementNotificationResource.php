<?php

declare(strict_types=1);

namespace Modules\Notification\Transformers\EngagementNotifications;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class IndexEngagementNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'content' => $this->content,
            'is_read' => $this->read_at ? true : false,
            'data' => $this->data,
            'published_at' => $this->created_at,
        ];
    }
}
