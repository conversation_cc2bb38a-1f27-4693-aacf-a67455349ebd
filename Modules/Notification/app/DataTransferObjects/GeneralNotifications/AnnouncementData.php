<?php

declare(strict_types=1);

namespace Modules\Notification\DataTransferObjects\GeneralNotifications;

use Illuminate\Support\Collection;
use Modules\Notification\Entities\Event;
use Spatie\LaravelData\Data;

final class AnnouncementData extends Data
{
    public function __construct(
        public readonly Event $event,
        public readonly Collection|array $users,
        public readonly string $title,
        public readonly string $content,
        public readonly string $send_method,
    ) {}
}
