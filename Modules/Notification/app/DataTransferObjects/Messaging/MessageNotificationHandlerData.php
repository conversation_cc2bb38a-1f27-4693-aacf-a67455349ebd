<?php

declare(strict_types=1);

namespace Modules\Notification\DataTransferObjects\Messaging;

use Modules\Notification\Enums\AppNotifications;
use Modules\Notification\Enums\NotificationRoutes;
use <PERSON><PERSON>\LaravelData\Data;

final class MessageNotificationHandlerData extends Data
{
    public function __construct(
        public string $sender_id,
        public string $receiver_id,
        public string $sender_name,
        public ?string $sender_avatar,
        public ?string $sender_company,
        public ?string $sender_job,
        public ?string $sender_gender,
    ) {}

    /**
     * @return array<string,string|null>|string|null
     */
    public function getArabicTitle(): array|null|string
    {
        return __('notification::messages.chats.messages.title', [
            'sender_name' => $this->sender_name,
        ], 'ar');
    }

    /**
     * @return array<string,string|null>|string|null
     */
    public function getEnglishTitle(): array|null|string
    {
        return __('notification::messages.chats.messages.title', [
            'sender_name' => $this->sender_name,
        ], 'en');
    }

    /**
     * @return array<string,string|null>|string|null
     */
    public function getFcmTitle(): array|null|string
    {
        return __('notification::messages.chats.messages.title', [
            'sender_name' => $this->sender_name,
        ]);
    }

    /**
     * @return array<string,string|null>|string|null
     */
    public function getArabicBody(): array|null|string
    {
        return __('notification::messages.chats.messages.body', [], 'ar');
    }

    /**
     * @return array<string,string|null>|string|null
     */
    public function getEnglishBody(): array|null|string
    {
        return __('notification::messages.chats.messages.body', [], 'en');
    }

    /**
     * @return array<string,string|null>|string|null
     */
    public function getFcmBody(): array|null|string
    {
        return __('notification::messages.chats.messages.body');
    }

    /**
     * @return array<string, mixed>
     */
    public function getFcmData(): array
    {
        return [
            'sender_id' => toString($this->sender_id),
            'receiver_id' => toString($this->receiver_id),
            'sender_name' => toString($this->sender_name),
            'notification_type' => AppNotifications::GENERAL->value,
            'route_name' => NotificationRoutes::MESSAGES->value,
            'sender_avatar' => toString($this->sender_avatar),
            'sender_company' => toString($this->sender_company),
            'sender_job' => toString($this->sender_job),
            'sender_gender' => toString($this->sender_gender),
        ];
    }
}
