<?php

declare(strict_types=1);

namespace Modules\Notification\DataTransferObjects\Invitations;

use Illuminate\Support\Str;
use <PERSON>tie\LaravelData\Data;

final class WhatsappOutAppInvitationData extends Data
{
    public function __construct(
        public readonly string $accessToken,
        public readonly string $url,
        public readonly string $language,
        public readonly string $recipientPhoneNumber, // E.164 format
        public readonly string $organizationName,
        public readonly string $eventName,
        public readonly string $eventDate,
        public readonly string $eventTime,
        public readonly string $eventLocation,
        public readonly string $eventLocationUrl,
        public readonly string $recipientName,
        public readonly string $recipientClass,
        public readonly string $badgeUrl,
        public readonly int $eventId,
        public readonly int $invitedId,
    ) {}

    public function toArray(): array
    {
        return [
            'messaging_product' => 'whatsapp',
            'to' => Str::of($this->recipientPhoneNumber)->ltrim('+')->toString(),
            'type' => 'template',
            'template' => [
                'name' => 'event_invitation',
                'language' => [
                    'code' => 'en',
                ],
                'components' => [
                    [
                        'type' => 'body',
                        'parameters' => [
                            [
                                'type' => 'text',
                                'parameter_name' => 'event_name',
                                'text' => $this->eventName,
                            ],
                            [
                                'type' => 'text',
                                'parameter_name' => 'event_date',
                                'text' => $this->eventDate,
                            ],
                            [
                                'type' => 'text',
                                'parameter_name' => 'event_time',
                                'text' => $this->eventTime,
                            ],
                            [
                                'type' => 'text',
                                'parameter_name' => 'event_location',
                                'text' => $this->eventLocation,
                            ],
                            [
                                'type' => 'text',
                                'parameter_name' => 'event_location_url',
                                'text' => $this->eventLocationUrl,
                            ],
                            [
                                'type' => 'text',
                                'parameter_name' => 'name',
                                'text' => $this->recipientName,
                            ],
                            [
                                'type' => 'text',
                                'parameter_name' => 'user_type',
                                'text' => $this->recipientClass,
                            ],
                            [
                                'type' => 'text',
                                'parameter_name' => 'event_url',
                                'text' => $this->badgeUrl,
                            ],
                            [
                                'type' => 'text',
                                'parameter_name' => 'organization_name',
                                'text' => $this->organizationName,
                            ],
                        ],
                    ],
                    [
                        'type' => 'button',
                        'sub_type' => 'url',
                        'index' => 0,
                        'parameters' => [
                            [
                                'type' => 'text',
                                'parameter_name' => 'event_url',
                                'text' => "events/{$this->eventId}/download-badge/{$this->invitedId}",
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }
}
