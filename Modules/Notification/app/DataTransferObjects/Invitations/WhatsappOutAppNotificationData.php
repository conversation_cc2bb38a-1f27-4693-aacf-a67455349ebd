<?php

declare(strict_types=1);

namespace Modules\Notification\DataTransferObjects\Invitations;

use Illuminate\Support\Str;
use <PERSON>tie\LaravelData\Data;

final class WhatsappOutAppNotificationData extends Data
{
    public function __construct(
        public readonly string $accessToken,
        public readonly string $url,
        public readonly string $recipientPhoneNumber, // E.164 format
        public readonly string $eventName,
        public readonly string $message,
    ) {}

    public function toArray(): array
    {
        return [
            'messaging_product' => 'whatsapp',
            'to' => Str::of($this->recipientPhoneNumber)->ltrim('+')->toString(),
            'type' => 'template',
            'template' => [
                'name' => 'notification_message',
                'language' => [
                    'code' => 'en',
                ],
                'components' => [
                    [
                        'type' => 'body',
                        'parameters' => [
                            [
                                'type' => 'text',
                                'parameter_name' => 'event_name',
                                'text' => $this->eventName,
                            ],
                            [
                                'type' => 'text',
                                'parameter_name' => 'message_body',
                                'text' => $this->message,
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }
}
