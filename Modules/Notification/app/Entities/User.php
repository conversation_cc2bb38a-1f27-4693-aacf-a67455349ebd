<?php

declare(strict_types=1);

namespace Modules\Notification\Entities;

use App\Concerns\InteractsWithFcmTokens;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Log;
use Modules\Notification\Concerns\HasNotifications;
use Snowflake\SnowflakeCast;

/**
 * @property string $name
 * @property string $locale
 */
final class User extends Model implements HasLocalePreference
{
    use HasNotifications;
    use InteractsWithFcmTokens;

    protected $guarded = ['id'];

    public function invitations(): HasMany
    {
        return $this->hasMany(Invitation::class);
    }

    public function routeNotificationForFcm(): array
    {
        Log::debug('routeNotificationForFcm', [
            'tokens' => $this->getTokens(),
        ]);

        return $this->getTokens();
    }

    public function preferredLocale(): ?string
    {
        return $this->locale;
    }

    public function attendedAgendas(): BelongsToMany
    {
        return $this->belongsToMany(
            Agenda::class,
            'participants',
        )->withTimestamps();
    }

    public function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
        ];
    }

    public function profile(): HasOne
    {
        return $this->hasOne(Profile::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }
}
