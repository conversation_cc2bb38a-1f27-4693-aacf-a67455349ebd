<?php

declare(strict_types=1);

namespace Modules\Notification\Entities;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Notification\Presenters\EventSettingPresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class EventSetting extends Model
{
    use EventSettingPresenter;
    use Snowflakes;

    protected $guarded = ['id'];

    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    protected function isNotificationEnabled(): Attribute
    {
        return Attribute::make(
            get: fn () => null !== $this->reminder_period,
        );
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'event_id' => SnowflakeCast::class,
            'category_id' => SnowflakeCast::class,
        ];
    }
}
