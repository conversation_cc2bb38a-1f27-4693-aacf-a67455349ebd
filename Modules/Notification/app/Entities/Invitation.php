<?php

declare(strict_types=1);

namespace Modules\Notification\Entities;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

final class Invitation extends Pivot
{
    protected $with = ['tag'];

    protected $guarded = ['id'];

    final public function user(): BelongsTo
    {
        return $this->belongsTo(
            User::class
        );
    }

    final public function event(): BelongsTo
    {
        return $this->belongsTo(
            Event::class
        );
    }

    public function tag(): BelongsTo
    {
        return $this->belongsTo(
            Tag::class,
            'tag_id',
            'id'
        );
    }
}
