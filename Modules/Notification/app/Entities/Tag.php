<?php

declare(strict_types=1);

namespace Modules\Notification\Entities;

use Illuminate\Database\Eloquent\Model;
use Snowflake\SnowflakeCast;

final class Tag extends Model
{
    /**
     * The attributes that are mass assignable.
     */
    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'event_id' => SnowflakeCast::class,
            'user_id' => SnowflakeCast::class,
        ];
    }
}
