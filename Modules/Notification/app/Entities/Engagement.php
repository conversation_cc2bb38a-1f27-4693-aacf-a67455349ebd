<?php

declare(strict_types=1);

namespace Modules\Notification\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Snowflake\Snowflakes;

final class Engagement extends Model
{
    use Snowflakes;

    protected $guarded = ['id'];

    public function agendas(): BelongsTo
    {
        return $this->belongsTo(Agenda::class);
    }


    protected function casts(): array
    {
        return [
            'start_at' => 'datetime',
            'end_at' => 'datetime',
        ];
    }
}
