<?php

declare(strict_types=1);

namespace Modules\Notification\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Material extends Model
{
    use Snowflakes;

    protected $guarded = ['id'];

    public function materialable(): MorphTo
    {
        return $this->morphTo();
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'materialable_id' => SnowflakeCast::class,
            'created_by_id' => SnowflakeCast::class,
        ];
    }
}
