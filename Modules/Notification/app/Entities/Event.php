<?php

declare(strict_types=1);

namespace Modules\Notification\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Modules\Notification\Presenters\EventPresenter;
use Snowflake\SnowflakeCast;
use Snowflake\Snowflakes;

final class Event extends Model
{
    use EventPresenter;
    use Snowflakes;

    protected $guarded = ['id'];

    public function agendas(): HasMany
    {
        return $this->hasMany(Agenda::class);
    }

    public function settings(): HasOne
    {
        return $this->hasOne(EventSetting::class);
    }

    public function invitedUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'invitation')
            ->withTimestamps()
            ->withPivot(['status', 'checked_in_at'])
            ->using(Invitation::class);
    }

    public function materials(): MorphMany
    {
        return $this->morphMany(Material::class, 'materialable');
    }

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'organization_id' => SnowflakeCast::class,
            'category_id' => SnowflakeCast::class,
            'start_at' => 'datetime',
            'end_at' => 'datetime',
        ];
    }
}
