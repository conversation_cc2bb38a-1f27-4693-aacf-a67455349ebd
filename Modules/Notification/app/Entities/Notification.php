<?php

declare(strict_types=1);

namespace Modules\Notification\Entities;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Notifications\DatabaseNotification;
use Modules\Notification\Enums\AppNotifications;
use Modules\Notification\Presenters\NotificationPresenter;

/**
 * @property string $name
 * @property string $locale
 */
final class Notification extends DatabaseNotification
{
    use NotificationPresenter;

    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Scope a query to only include general notifications.
     */
    public function scopeShouldBeBasedOnEvent(Builder $query, int $id): Builder
    {
        return $query->where(fn (Builder $query) => $query->where(
            'event_id',
            $id
        ));
    }

    /**
     * Scope a query to only include general notifications.
     */
    public function scopeShouldBeGeneral(Builder $query): Builder
    {
        return $query->where(fn ($query) => $query->where(
            'notification_type',
            AppNotifications::GENERAL
        ));
    }

    /**
     * Scope a query to only include general notifications.
     */
    public function scopeShouldBeEngagement(Builder $query): Builder
    {
        return $query->where(fn ($query) => $query->where(
            'notification_type',
            AppNotifications::ENGAGEMENTS
        ));
    }
}
